import {createServerClient} from "@supabase/ssr";
import {NextResponse, type NextRequest} from "next/server";

export async function updateSession(request: NextRequest) {
  const { pathname } = request.nextUrl;
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({name, value, options}) =>
            request.cookies.set(name, value)
          );
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({name, value, options}) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        },
      },
    }
  );

  // Check if the path is a protected route
  const isProtectedRoute = pathname.startsWith("/dashboard") || pathname.startsWith("/admin");
  
  // Check if the path is an auth route
  const isAuthRoute = pathname.startsWith("/login") || pathname.startsWith("/signup");

  try {
    // Efficiently get user data - only one database call
    const { data: { user }} = await supabase.auth.getUser();
    
    // If no user and trying to access protected route, redirect to login
    if (isProtectedRoute && !user) {
      const url = new URL("/login", request.url);
      url.searchParams.set("callbackUrl", encodeURI(request.url));
      return NextResponse.redirect(url);
    }
    
    // If user is authenticated
    if (user) {
      // Get user profile in a single query
      const { data: profile } = await supabase
        .from("profiles")
        .select("role")
        .eq("id", user.id)
        .single();

        console.log("profile", profile)
        
      const isAdmin = profile?.role === "admin";
      
      // Authenticated users should not access auth routes
      if (isAuthRoute) {
        // Redirect based on role
        return NextResponse.redirect(
          new URL(isAdmin ? "/admin" : "/dashboard", request.url)
        );
      }
      
      // Restrict admin routes to admin users
      if (pathname.startsWith("/admin") && !isAdmin) {
        return NextResponse.redirect(new URL("/dashboard", request.url));
      }

      if(pathname.startsWith("/dashboard") && isAdmin) {
        return NextResponse.redirect(new URL("/admin", request.url));
      }
      
      // Simple role-based redirects for root URL
      if (pathname === "/") {
        return NextResponse.redirect(
          new URL(isAdmin ? "/admin" : "/dashboard", request.url)
        );
      }
    }
  } catch (error) {
    console.error("Middleware error:", error);
  }

  return supabaseResponse;
}