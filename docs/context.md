# The Travella - Feature Overview & Flow*

## *Introduction*

*The Travella App provides a seamless experience for users to find, list, and book rental cars. This document outlines the app's flow and features in a structured manner to assist developers in understanding and implementing the functionalities.

## 1. Welcome Screen

- When the app is opened, users are greeted with a clean and intuitive welcome screen.
- Users have two options to sign up or log in:
  - **Google Authentication**
  - **Email and Password**

## 2. Home Screen

After signing in, users land on the home screen where they can:

- View available rental cars sorted by proximity to their current location.
- Search for cars by typing a location name.
- Filter search results by:
  - **Price**
  - **Vehicle type** (Sedan, SUV, etc.)
  - **Car make and model**
  - **Manufacture year**
  - **Number of seats**
  - **Pickup options** (Self-pickup or delivery)
- Switch between **list view** and **map view** to browse cars.

## 3. Car Details Screen

When a user selects a car from the search results:

- They are directed to the **Car Details Screen**, which displays:
  - **Car images**
  - **Car name and model year**
  - **Ratings and reviews**
  - **Car owner details**
  - **Car features (Fuel type, transmission, mileage, etc.)**
  - **Return policy**
- Users can proceed to book the car.

## 4. Booking a Car

On the car details screen, users can:

1. Select the **pickup date and time**.
2. Select the **drop-off date and time**.
3. Choose the **pickup location**.
4. Click the **Continue** button to proceed to payment.

## 5. Payment Process

- After confirming the booking details, users are redirected to the **payment page**.
- Payment options may include:
  - **Credit/Debit Card**
  - **Mobile Wallets**
  - **Bank Transfer**
- Once the payment is successful, a **booking confirmation** is displayed.

## 6. Booking Confirmation & Management

- Users receive a **confirmation email or notification** with booking details.
- Bookings can be managed from the **My Bookings** section, where users can:
  - View upcoming and past rentals.
  - Modify or cancel a booking (if permitted by policy).
  - Contact the car owner.

## 7. Listing a Car for Rent

Car owners can list their vehicles on the platform by:

1. Providing **vehicle details** (make, model, year, etc.).
2. Uploading **high-quality images**.
3. Setting **rental price and availability**.
4. Choosing **pickup options** (self-pickup or delivery).
5. Publishing the listing for users to find and book.

## 8. Additional Features

- **User Profile Management:** Update profile details, payment methods, and rental history.
- **Ratings & Reviews:** Users can rate their rental experience and leave reviews.
- **Notifications:** Receive updates on bookings, payments, and offers.
- **Support & Help Center:** Contact support for any assistance.
