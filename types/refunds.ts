export type RefundStatus = 'pending' | 'processing' | 'completed' | 'failed';

export interface BookingRefundRequest {
    id: string;
    booking_id: string;
    user_id: string;
    amount: number;
    percentage: number;
    status: RefundStatus;
    transaction_id: string | null;
    payment_method: string | null;
    notes: string | null;
    processed_at: string | null;
    start_date: string;
    end_date: string;
    cancelled_at: string;
    model: string;
    brand_name: string;
    currency: string;
    user_details: {
        id: string;
        full_name: string;
        avatar_url: string;
        email: string;
    }
    created_at: string;
    updated_at: string | null;
    meta_data: {
        type?: string;
        expiry?: string;
        issuer?: string;
        country?: string;
        last_4digits?: string;
        first_6digits?: string;
        booking_id?: string;
        payment_source?: string;
        wallet_transaction_id?: string;
        wallet_transaction_date?: string;
    }
}

export interface GetBookingRefundsPayload {
    page: number;
    items_per_page: number;
    status?: RefundStatus;
    user_name?: string;
}

export interface GetBookingRefundResquestsResponse {
    data: BookingRefundRequest[];
    count: number;
    has_next: boolean;
    page: number;
    items_per_page: number;
    total_pages: number;
    sort_direction: string;
}

export interface UpdateRefundStatusPayload {
    refund_id: string;
    status: RefundStatus;
    notes?: string | null;
}
