import { CarStatus } from "./supabase";

export interface CarListingFeature {
	id: string;
	car_features: { icon: string; name: string };
}

export interface CarAvailablePickupLocation {
	id: string;
	car_id: string;
	formatted_address: string;
	latitude: number;
	longitude: number;
	place_id: string;
	place_name: string;
	fee: number;
	is_main?: boolean;
	is_active?: boolean;
}

export type Car = {
	id: string;
	daily_rate: number;
	available: boolean;
	description: string;
	year: number;
	fuel_type: string;
	guidelines: string;
	model: string;
	brand: string;
	available_pickup_locations: CarAvailablePickupLocation[];
	car_images: string[];
	car_features: CarListingFeature[];
};

export interface CarCancellationPolicy {
	id: string;
	name: string;
	description: string;
	code: string;
}

export interface CurrencyDetails {
	code: string; // TODO: Add type for currency from flutterwave
	prefix: string;
	country: string;
	countryid: string;
}

export interface CarListingResponse {
	id: string;
	available: boolean;
	available_pickup_locations: CarAvailablePickupLocation[];
	brand: string;
	seats: number;
    registration_number: string;
    vin: string;
	car_features: {
		category: string;
		created_at: string;
		icon: string;
		id: string;
		name: string;
	}[];
    owner_name: string;
    owner_avatar: string;
	car_images: string[];
	daily_rate: number;
	description: string;
	feature_ids: string[];
	fuel_type: "Petrol" | "Diesel" | "Electric" | "Hybrid";
	guidelines: string;
	year: number;
	model: string;
	owner_id: string;
	brand_name: string;
	avg_maintenance_rating: number;
	avg_communication_rating: number;
	avg_convenience_rating: number;
	avg_accuracy_rating: number;
	avg_overall_rating: number;
	total_ratings: number;
	total_bookings: number;
	total_earnings: number;
	pickup_fee: number;
	next_available_date: string;
	cancellation_policy_id: string;
	cancellation_policy: CarCancellationPolicy;
	country_name: string;
	// Driver availability
	driver_available?: boolean;
	// Currency details
	currency_code?: string;
	currency_prefix?: string;
	country_code?: string;
	currency_details?: CurrencyDetails;
	status: CarStatus;
    created_at: string;
    updated_at: string;
}

export interface GetCarlistingsPayload {
    page?: number;
    items_per_page?: number;
	country_id?: string | null;
    status?: CarStatus | null;
	search?: string | null;
}

export interface CarListingResponse {
    data: CarListingResponse[]
	count: number;
	has_next: boolean;
	page: number;
	items_per_page: number;
	total_pages: number;
	sort_by: string;
	sort_direction: string;
}

export interface CarFeature {
    id: string;
    name: string;
    icon: string;
    category: string;
    description: string;
    created_at: string;
    updated_at?: string;
}

export interface CancellationPolicy {
    id: string;
    name: string;
    code: string;
    description: string;
    full_refund_hours: number;
    partial_refund_hours: number;
    partial_refund_percentage: number;
    is_active: boolean;
    created_at: string;
    updated_at?: string;
}

export interface CarBrand {
    id: string;
    name: string;
    created_at: string;
    updated_at?: string;
}

// Admin types
export interface CreateCarBrandInput {
    name: string;
}

export interface CreateCarFeatureInput {
    name: string;
    icon: string;
    category: string;
}

export interface CreateCancellationPolicyInput {
    name: string;
    code: string;
    description: string;
    full_refund_hours: number;
    partial_refund_hours: number;
    partial_refund_percentage: number;
    is_active: boolean;
}

export interface UpdateCarBrandInput {
    name?: string;
}

export interface UpdateCarFeatureInput {
    name?: string;
    icon?: string;
    category?: string;
}

export interface UpdateCancellationPolicyInput {
    name?: string;
    code?: string;
    description?: string;
    full_refund_hours?: number;
    partial_refund_hours?: number;
    partial_refund_percentage?: number;
    is_active?: boolean;
}
