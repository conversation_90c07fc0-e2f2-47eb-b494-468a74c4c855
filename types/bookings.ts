import { FlutterwaveCustomer } from "./flutterwave";
import { FlutterwaveCard } from "./flutterwave";
import { CurrencyDetails } from "./listings";
import { BookingStatus } from "./supabase";

export type CancellationPolicy = {
    id: string;
    name: string;
    code: string;
    description: string;
    full_refund_hours: number;
    partial_refund_hours: number | null;
    partial_refund_percentage: number | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
};

export interface BookingResponse {
	id: string;
	brand: string;
	brand_name: string;
	car_id: string;
	car_images: string[];
	created_at: string;
	daily_charge: number;
	dropoff_time: string;
	end_date: string;
	pickup_fee: number;
	guidelines: string | null;
	host: {
		avatar: string;
		first_name: string;
		id: string;
		last_name: string;
		name?: string;
		phone: string | null;
		joined: string;
	};
	host_notes: string | null;
	model: string;
	owner_id: string;
	pick_up_location?: {
		id: string;
		place_name: string;
		formatted_address: string;
		fee: number;
		latitude: number;
		longitude: number;
	};
	pickup_location?: {
		id: string;
		place_name: string;
		formatted_address: string;
		fee: number;
		latitude: number;
		longitude: number;
	};
	pickup_instructions: string | null;
	pickup_location_id: string;
	pickup_time: string;
	renter_id: string;
	return_instructions: string | null;
	start_date: string;
	status: BookingStatus;
	total_amount: number;
	extras_amount?: number;
	updated_at: string;
	modified_at?: string;
	canceled_at?: string;
	cancellation_reason?: string;
	refund_amount?: number;
	refund_percentage?: number;
	year: number;
	next_available_date: string;
	currency_details: CurrencyDetails;
	cancellation_policy_id: string;
	cancellation_policy: CancellationPolicy;
	booking_issues?: BookingIssue[];
	booking_modification_requests?: BookingModificationRequest[];
}

export interface FilterBookingsPayload {
	status?: BookingStatus | "all" | undefined;
	search?: string | undefined;
	startDate?: string | undefined;
	endDate?: string | undefined;
	country?: string | undefined;
}

export type PaymentType = 'booking_payment' | 'booking_modification_payment';

export interface BookingPayment {
	id: string,
	booking_id: string,
	amount: number,
	currency: string,
	transaction_id: string,
	payment_status: string,
	payment_method: string,
	payment_type: PaymentType,
	created_at: string,
	metadata: {
	  verified_status: string,
	  booking_id: string,
	  modification_request_id?: string,
	  card: FlutterwaveCard,
	  customer: FlutterwaveCustomer,
	}
};

export interface BookingIssue {
	id: string;
	booking_id: string;
	reporter_id: string;
	issue_type: "Car Damage" | "Mechanical Issue" | "Cleanliness" | "Other" | "Late Return" | "Location Issue";
	description: string;
	created_at: string;
	updated_at: string;
	resolved_at: string | null;
	status: "pending" | "reviewing" | "resolved";
}

export type PaymentStatus = "pending" | "approved" | "declined" | "canceled" | "completed";

export interface BookingModificationRequest {
	id: string;
	booking_id: string;
	requester_id: string;
	host_id: string;
	previous_data: {
		end_date: string;
		start_date: string;
		total_amount: string;
		pickup_location_id: string;
	},
	requested_changes: {
		message: string;
		end_date?: string;
		start_date?: string;
		total_amount?: string;
		pickup_location_id?: string;
		additional_amount?: number;
	},
	message: string;
	status: PaymentStatus;
	created_at: string;
	updated_at: string;
	host_response_message: string | null;
	additional_amount: number | null;
	payment_status: "pending" | "completed";
	payment_transaction_id: string | null;
	notification_sent: boolean;
	payment_id: string | null;
	completed_at: string | null;
	requester_details: {
		first_name: string;
		last_name: string;
		avatar_url: string;
	};
	host_details: {
		first_name: string;
		last_name: string;
		avatar_url: string;
	};
}
