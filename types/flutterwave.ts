export interface FlutterwaveCustomer {
  id: number,
  name: string,
  phone_number: string | null,
  email: string,
  created_at: string
}

export interface FlutterwaveCard {
  first_6digits: string,
  last_4digits: string,
  issuer: string,
  country: string,
  type: string,
  expiry: string
}

export interface FlutterwavePaymentData {
  id: number,
  tx_ref: string,
  flw_ref: string,
  device_fingerprint: string,
  amount: number,
  currency: string,
  charged_amount: number,
  app_fee: number,
  merchant_fee: number,
  processor_response: string,
  auth_model: string,
  ip: string,
  narration: string,
  status: string,
  payment_type: string,
  created_at: string,
  account_id: number,
  customer: FlutterwaveCustomer,
  card: FlutterwaveCard;
}

export type PaymentType = 'booking_payment' | 'booking_modification_payment';

export interface FlutterwavewebhookPayload {
  event: string,
  data: FlutterwavePaymentData,
  meta_data: {
    __CheckoutInitAddress: string,
    type: PaymentType,
    booking_id: string
    modification_request_id?: string
  },
  'event.type': string
}

export interface FlutterwavePaymentVerificationResponse {
  status: 'successful' | 'failed',
  message: string,
  data: FlutterwavePaymentData
}
