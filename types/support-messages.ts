export interface SupportTicketMessage {
  id: string;
  ticket_id: string;
  sender_id: string;
  content: string;
  is_support: boolean;
  read_at: string | null;
  created_at: string;
  updated_at: string;
  sender_first_name: string;
  sender_last_name: string;
  sender_avatar_url: string;
  sender_role: string;
  booking_id: string;
  reporter_id: string;
  ticket_status: string;
  isOptimistic?: boolean;
  tempId?: string | null;
}

export interface SupportTicketMessageResponse {
  messages: SupportTicketMessage[];
  hasMore: boolean;
  total: number;
}
