import { CurrencyDetails } from "./listings";
import { BookingStatus } from "./supabase";

interface Profile {
    id: string;
    first_name: string;
    last_name: string;
    full_name: string;
    avatar_url: string;
    phone: string;
}

export interface BookingPayment {
    payment_id: string;
    booking_id: string;
    amount: number;
    currency: string;
    transaction_id: string;
    payment_status: 'success' | 'failed' | 'pending';
    payment_method: string;
    payment_type: 'booking_payment' | 'booking_refund';
    metadata: Record<string, any>;
    payment_date: string;
    start_date: string;
    end_date: string;
    booking_total: number;
    booking_status: BookingStatus;
    car_id: string;
    car_brand_id: string;
    car_brand_name: string;
    car_modal: string;
    car_year: number;
    pickup_address: string;
    pickup_name: string;
    car_owner: Profile;
    renter: Profile;
    currency_details: Omit<CurrencyDetails, 'countryid'>;
}

export interface GetBookingPaymentsPayload {
    page: number;
    items_per_page: number;
    status?: BookingStatus | null;
    payment_method?: string | null;
    car_owner_id?: string | null;
    renter_id?: string | null;
    start_date?: string | null;
    end_date?: string | null;
}

export interface GetBookingPaymentsResponse {
    data: BookingPayment[];
    count: number;
    page: number;
    itemsPerPage: number;
    totalPages: number;
    hasNextPage: boolean;
}

export interface PaymentFilterValues {
    status: string | null;
    payment_type: string | null;
    payment_method: string | null;
    car_owner_id: string | null;
    renter_id: string | null;
    start_date: string | null;
    end_date: string | null;
}