export enum UserRole {
  ADMIN = "admin",
  USER = "user",
}

export interface User {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
  is_active?: boolean;
  is_verified?: boolean;
  role?: string;
  email_verified?: boolean;
  phone_verified?: boolean;
  // Any additional fields as needed
} 

export interface UserProfile {
  id: string;
  email: string;
  phone_number: string;
  first_name: string;
  last_name: string;
  avatar_url: string;
  bio: string;
  license_number: string;
  license_expiry: string;
  license_verified: boolean;
  license_front: string;
  license_back: string;
  email_verified: boolean;
  phone_verified: boolean;
  language: string;
  email_notifications: boolean;
  push_notifications: boolean;
  marketing_emails: boolean;
  role: UserRole;
  created_at: string;
  updated_at: string;
}

export interface GetAdminUsersPayload {
  page: number;
  limit: number;
  search?: string;
  role?: UserRole;
}

export interface GetAdminUsersResponse {
  data: UserProfile[];
  count: number;
  has_next: boolean;
  page: number;
  items_per_page: number;
  total_pages: number;
}

export interface UpdateUserRolePayload {
  user_id: string;
  role: UserRole;
}

export interface UpdateUserRoleResponse {
  success: boolean;
  message: string;
  user: UserProfile;
}
