export interface SupportTicket {
    id: string;
    booking_id: string;
    reporter_id: string;
    type: 'other' | 'technical' | 'billing' | 'account' | 'other';
    description: string;
    status: 'open' | 'in_progress' | 'resolved' | 'closed';
    created_at: string;
    updated_at: string;
    start_date: string;
    end_date: string;
    brand: string;
    model: string;
    year: number;
    car_image: string;
    reporter_first_name: string;
    reporter_last_name: string;
    reporter_avatar: string;
    message_count: number;
    last_message_at: string;
    last_message: string;
    last_admin_name: string | null;
    unread_admin_messages_count: number;
    unread_user_messages_count: number;
}
