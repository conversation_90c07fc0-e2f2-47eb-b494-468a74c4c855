import { UserProfile } from "./users";

export interface DashboardStats {
    hosts: { count: number; growth: number };
    revenue: { total: number; growth: number };
    bookings: { count: number; growth: number };
    retrieved_at: string;
    active_car_listings: { count: number; growth: number };
    categorized_bookings: {
      pending: number;
      rejected: number;
      cancelled: number;
      completed: number;
      confirmed: number;
      in_progress: number;
    };
    booking_completion_rate: number;
}

export interface Activity {
  id: string;
  type: string;
  user: {
    id: string;
    avatar_url: string;
    name: string;
  };
  time_ago: string;
  created_at: string;
  formatted_date: string;
}

export interface PlatformOverview {
  monthly_bookings: { month: string; revenue: number }[];
  recent_activities: Activity[];
  recent_users: UserProfile[];
  retrieved_at: string;
}
