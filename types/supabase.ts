import { CarListingResponse } from "./listings"

export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          full_name: string | null
          avatar_url: string | null
          phone: string | null
          address: string | null
          created_at: string
          updated_at: string
          user_type: string
        }
        Insert: {
          id: string
          full_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          address?: string | null
          created_at?: string
          updated_at?: string
          user_type: string
        }
        Update: {
          id?: string
          full_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          address?: string | null
          created_at?: string
          updated_at?: string
          user_type?: string
        }
      }
      cars: {
        Row: {
          id: string
          owner_id: string
          title: string
          description: string | null
          make: string
          model: string
          year: number
          price: number
          location: string
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          owner_id: string
          title: string
          description?: string | null
          make: string
          model: string
          year: number
          price: number
          location: string
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          owner_id?: string
          title?: string
          description?: string | null
          make?: string
          model?: string
          year?: number
          price?: number
          location?: string
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
      car_features: {
        Row: {
          id: string
          car_id: string
          feature: string
        }
        Insert: {
          id?: string
          car_id: string
          feature: string
        }
        Update: {
          id?: string
          car_id?: string
          feature?: string
        }
      }
      car_images: {
        Row: {
          id: string
          car_id: string
          url: string
          created_at: string
        }
        Insert: {
          id?: string
          car_id: string
          url: string
          created_at?: string
        }
        Update: {
          id?: string
          car_id?: string
          url?: string
          created_at?: string
        }
      }
      bookings: {
        Row: {
          id: string
          car_id: string
          renter_id: string
          start_date: string
          end_date: string
          status: string
          total_amount: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          car_id: string
          renter_id: string
          start_date: string
          end_date: string
          status?: string
          total_amount: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          car_id?: string
          renter_id?: string
          start_date?: string
          end_date?: string
          status?: string
          total_amount?: number
          created_at?: string
          updated_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          booking_id: string | null
          user_id: string
          amount: number
          type: string
          status: string
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          booking_id?: string | null
          user_id: string
          amount: number
          type: string
          status?: string
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          booking_id?: string | null
          user_id?: string
          amount?: number
          type?: string
          status?: string
          description?: string | null
          created_at?: string
        }
      }
      filtered_cars: {
        Row: CarListingResponse
      }
    }
  }
}

export enum SupabaseErrorCodes {
  NO_ITEMS_FOUND = 'PGRST116',
  INVALID_INPUT = '22P02',
}

export interface SupabaseResponse<T> {
	data: T;
	error: {
		code: SupabaseErrorCodes;
    details: string;
		hint: string;
    message: string;
	}
}

// Check here for all defined types: https://supabase.com/dashboard/project/seyangbvwjuxyvbwbgux/database/types
export type NotificationType = 'booking_confirmed' | 'booking_cancelled' | 'modification_requested' | 'modification_approved' | 'modification_rejected' | 'message_received' | 'support_message' | 'support_status_update' | 'booking_completed' | 'booking_started' | 'booking_modification_request' | 'booking_modification_response' | 'modification_response' | 'modification_request' | 'booking_request' | 'booking_modification_payment' | 'payment_received' | 'modification_completed' | 'booking_canceled' | 'car_status_update' | 'payment_failed'

export type BookingStatus = 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'ongoing'

export type CarStatus = 'draft' | 'approved' | 'rejected' | 'blocked' | 'reported'

export type PayoutStatus = 'pending' | 'processing' | 'completed' | 'failed'

export type BookingModificationStatus = 'pending' | 'approved' | 'declined' | 'canceled' | 'completed'

export type PaymentType = 'booking_payment' | 'booking_modification_payment' | 'wallet_credit'

export type RefundStatus = 'pending' | 'processing' | 'completed' | 'failed'

export type CarListingStatus = 'draft' | 'under_review' | 'approved' | 'rejected' | 'reported'

export type UserRole = 'admin' | 'user'
