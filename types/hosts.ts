import { BookingStatus } from "./supabase";

export interface Host {
    id: string;
    avatar_url: string;
    first_name: string;
    last_name: string;
    full_name: string;
    phone: string;
    created_at: string;
    bio: string;
    cars: number;
    trips: number;
    lisence_verified: boolean;
    license_back: string;
    license_front: string;
    country_id: string;
    avg_rating: number;
    total_ratings: number;
    payment_settings: {
        is_primary: boolean;
        bank_name: string;
        swift_code: string;
        account_number: string;
        account_holder_name: string;
        phone_number: string;
    }[]
    country_details: {
        id: string;
        name: string;
        code: string;
        currency_code: string;
        currency_prefix: string;
        is_active: boolean;
        flag_image: string;
    }
}

export interface HostPaymentAccount {
    id: string;
    user_id: string;
    account_holder_name: string | null;
    account_number: string | null;
    bank_name: string | null;
    bank_branch: string | null;
    bank_code: string | null;
    swift_code: string | null;
    phone_number: string | null;
    mobile_provider: string | null;
    payout_method: 'bank_transfer' | 'mobile_money';
    is_primary: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

export enum PayoutStatus {
    PENDING = 'pending',
    PROCESSING = 'processing',
    COMPLETED = 'completed',
    FAILED = 'failed',
}

export interface Payout {
    id: string;
    host_id: string;
    booking_id: string;
    amount: number;
    fee: number;
    net_amount: number;
    payout_status: PayoutStatus;
    payout_method: 'bank_transfer' | 'mobile_money';
    transaction_id: string;
    notes: string;
    payout_created_at: string;
    payout_updated_at: string;
    processed_at: string;
    start_date: string;
    end_date: string;
    pickup_location_id: string;
    booking_status: BookingStatus;
    booking_total: number;
    renter_id: string;
    car_id: string;
    car_brand: string;
    car_modal: string;
    host_first_name: string;
    host_last_name: string;
    host_avatar: string;
    preferred_payout_method: 'bank_transfer' | 'mobile_money' | null;
    account_holder_name: string | null;
    payment_details: {
        bank_name?: string;
        swift_code?: string;
        account_number?: string;
        account_holder_name?: string;
        phone_number?: string;
        mobile_provider?: string;
    } | null;
}

export interface GetPayoutsPayload {
    itemsPerPage: number;
    page: number;
    status: PayoutStatus | null;
    hostId: string | null;
    booking_status: BookingStatus | null;
    host_name: string | null;
}

export interface HostPayoutStats {
    total_payouts_count: number;
    pending_payouts_count: number;
    completed_payouts_count: number;
    total_earnings: number;
    pending_amount: number;
    paid_amount: number;
}

export interface UpdateHostPayoutStatusPayload {
    payoutId: string;
    payoutStatus: PayoutStatus;
}

export interface PaymentTransfer {
    id: string;
    user_id: string;
    payout_setting_id: string;
    amount: number;
    currency: string;
    status: PayoutStatus;
    reference_id: string;
    description: string;
    transaction_date: string;
    host_first_name: string;
    host_last_name: string;
    host_avatar: string;
    payout_details: {
        bank_name?: string;
        swift_code?: string;
        account_number?: string;
        account_holder_name?: string;
        phone_number?: string;
        mobile_provider?: string;
    } | null;
    created_at: string;
    updated_at: string;
}

export interface GetHostTransfersPayload {
    itemsPerPage: number;
    page: number;
    status: PayoutStatus | null;
    hostId: string | null;
}

export interface CreateTransferPayload {
    hostId: string;
    payout_id: string;
    amount: number;
    currency: string;
    description: string | null;
    reference_id: string | null;
}

export interface UpdateTransferPayload {
    transferId: string;
    status: PayoutStatus;
    reference_id: string | null;
    description: string | null;
    transaction_date: string;
}

export interface GetHostsPayload {
    itemsPerPage: number;
    page: number;
    searchTerm: string;
    sortBy: string;
    sortOrder: string;
}

export interface HostResponseData {
    data: Host[];
    count: number;
    hasNextPage: boolean;
    page: number;
    itemsPerPage: number;
    totalPages: number;
}

export interface GetHostsResponse {
    error: Error | null;
    data: HostResponseData;
}
