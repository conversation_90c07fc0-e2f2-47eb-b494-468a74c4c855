// Followed this documentation to create the webhook: https://developer.flutterwave.com/docs/webhooks

import { NextResponse } from "next/server";
import { createAdminClient } from "@/lib/supabase-admin";
import { headers } from "next/headers";
// import Flutterwave from "flutterwave-node-v3";
import {  BookingPayment, FlutterwavewebhookPayload } from "@/types/flutterwave";

export async function POST(request: Request) {
  // Initialize Supabase client with service role key for admin operations
  const supabase = createAdminClient();
  
  async function createLog(data: any, message: string) {
    const payload = data;
    await supabase.from('flutterwave_logs').insert({
      data: payload,
      event_name: message,
    });
  }

  try {
    // Step 1: Verify the webhook signature
    const headersList = await headers();
    const signature = headersList.get("verif-hash");
    const secretHash = process.env.FLW_SECRET_HASH;

    if (!signature || signature !== secretHash) {
      await createLog({ received_signature: signature }, 'Invalid signature, rejecting webhook');
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Step 2: Parse the webhook payload
    const webhookPayload = <FlutterwavewebhookPayload>(await request.json());
    await createLog(webhookPayload, 'Webhook payload received');
    const { event, data: paymentData, meta_data } = webhookPayload;

    if (event === 'charge.completed' && paymentData.status === 'successful') {
      const { id: transaction_id, status, amount, currency, tx_ref: bookingId, payment_type: payment_method, customer, card } = paymentData;
      const { booking_id, modification_request_id, type } = meta_data;

      // verify booking exists
      const { data: booking, error: bookingError } = await supabase
        .from("bookings")
        .select("id")
        .eq("id", bookingId);

      if (!booking || bookingError) {
        await createLog(bookingError, 'Booking not found');
        return new NextResponse("Booking not found", { status: 404 });
      }
        
      // Since we're trusting the webhook payload directly, 
      // we assume the payment was successful if status is 'successful'
      
      // Update booking status to confirmed
      const { error: bookingError2 } = await supabase
        .from("bookings")
        .update({ status: "confirmed" })
        .eq("id", bookingId);

      if (bookingError2) {
        await createLog(bookingError2, 'Error updating booking status');
        return new NextResponse("Error updating booking status", { status: 200 });
      }

      //TODO:: update booking modification request status to confirmed if it exists

      // For booking modification payments, update the modification request status to completed
      if (modification_request_id) {
        const { error: modificationRequestError } = await supabase
          .from("booking_modification_requests")
          .update({ status: "completed", payment_status: 'completed', completed_at: new Date().toISOString() })
          .eq("id", modification_request_id);

        if (modificationRequestError) {
          await createLog(modificationRequestError, 'Error updating modification request status');
        }
      }

      const bookingPaymentPayload = {
        booking_id,
        amount,
        currency,
        transaction_id: transaction_id.toString(),
        payment_status: status,
        payment_method,
        payment_type: type || 'booking_payment',
        metadata: {
          verified_status: status,
          booking_id,
          modification_request_id,
          card,
          customer,
        }
      } as BookingPayment;

      // check if the payment exists in the database
      const { data: bookingPayment, error: bookingPaymentError } = await supabase
        .from("booking_payments")
        .select("id")
        .eq("transaction_id", transaction_id.toString());

      if (bookingPayment && bookingPayment.length) {
        const { error: paymentError } = await supabase
          .from("booking_payments")
          .update(bookingPaymentPayload)
          .eq("booking_id", bookingId);

        if (paymentError) {
          await createLog(paymentError, 'Error updating booking payment');
          return new NextResponse("Error updating booking payment", { status: 200 });
        }
      } else {
        const { error: paymentError } = await supabase
          .from("booking_payments")
          .insert({ ...bookingPaymentPayload, created_at: new Date().toISOString() });

        if (paymentError) {
          await createLog(paymentError, 'Error creating booking payment');
          return new NextResponse("Error creating booking payment", { status: 200 });
        }
      }

      return new NextResponse("Payment processed and booking confirmed", { status: 200 });
    } else if (event === 'charge.completed' && paymentData.status !== 'successful') {
      // Log unsuccessful payment attempts
      await createLog(paymentData, 'Payment unsuccessful');
      return new NextResponse("Payment status not successful", { status: 200 });
    }
    
    // For any other events, acknowledge receipt
    return new NextResponse("Webhook received", { status: 200 });
  } catch (error) {
    await createLog(error, 'Webhook processing error');
    return new NextResponse("Webhook received", { status: 200 });
  }
}

