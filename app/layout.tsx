import type React from "react"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/svg/travella_logo_light.svg" sizes="32x32" />
      </head>
      <body className={inter.className}>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
            <ReactQueryClientProvider>
            {children}
            </ReactQueryClientProvider>
            <Toaster />
          </ThemeProvider>
      </body>
    </html>
  )
}



import './globals.css'
import { ReactQueryClientProvider } from "./providers/ReactQueryProvider"

export const metadata = {
  generator: 'v0.dev'
};
