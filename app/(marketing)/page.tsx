import Link from "next/link"
import Image from "next/image"
import { ArrowRight, Check, Download, Star, Car, Shield, CreditCard, Calendar, MapPin, Phone, Menu, X, Send, Repeat, ExternalLink, Users, Clock, Sparkles, Zap, Globe, Compass } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button-new"
import { Card, CardContent } from "@/components/ui/card-new"
import { Badge } from "@/components/ui/badge-new"
import { Suspense } from "react"

// Predefine screenshots array for better performance
const screenshots = [
  {
    src: "/images/1.jpg",
    alt: "App Screenshot 1"
  },
  {
    src: "/images/2.jpg",
    alt: "App Screenshot 2"
  },
  {
    src: "/images/3.jpg",
    alt: "App Screenshot 3"
  },
  {
    src: "/images/4.jpg",
    alt: "App Screenshot 4"
  },
]

export default function LandingPage() {
  return (
    <div className="flex min-h-screen flex-col overflow-hidden">
      {/* Hero Section - Asymmetric Design with Floating Elements */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Sophisticated Black & Green Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
          <div className="absolute inset-0 bg-gradient-to-tr from-[#adbf3b]/15 via-transparent to-[#adbf3b]/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(173,191,59,0.2),transparent_60%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(173,191,59,0.15),transparent_70%)]"></div>
        </div>

        {/* Elegant Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-[#adbf3b]/15 to-black/20 rounded-full blur-xl animate-pulse-slow"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-black/30 to-[#adbf3b]/15 rounded-full blur-lg animate-pulse-slow" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-gradient-to-br from-[#adbf3b]/10 to-black/15 rounded-full blur-2xl animate-pulse-slow" style={{animationDelay: '2s'}}></div>

          {/* Organic SVG Shapes */}
          <svg className="absolute top-0 left-0 w-full h-full" viewBox="0 0 1200 800" fill="none">
            <path d="M0,400 Q300,200 600,400 T1200,400 L1200,0 L0,0 Z" fill="url(#gradient1)" opacity="0.1"/>
            <path d="M0,600 Q400,400 800,600 T1200,600 L1200,800 L0,800 Z" fill="url(#gradient2)" opacity="0.1"/>
            <defs>
              <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#adbf3b" />
                <stop offset="100%" stopColor="#000000" />
              </linearGradient>
              <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#000000" />
                <stop offset="100%" stopColor="#adbf3b" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        <div className="container relative z-10 px-4 md:px-6">
          <div className="grid gap-12 lg:grid-cols-12 lg:gap-16 items-center">
            {/* Left Content - Asymmetric Layout */}
            <div className="lg:col-span-7 space-y-8">
              {/* Floating Badge */}
              <div className="relative">
                <Badge className="bg-white/10 backdrop-blur-md text-white border-white/20 hover:bg-white/20 transition-all duration-300 shadow-lg">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Your #1 Car Rental Experience
                </Badge>
              </div>

              {/* Dynamic Typography */}
              <div className="space-y-6">
                <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight text-white leading-[1.1]">
                  <span className="block">Discover</span>
                  <span className="block bg-gradient-to-r from-[#adbf3b] via-white to-[#adbf3b] bg-clip-text text-transparent">
                    Freedom
                  </span>
                  <span className="block text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-light text-white/90">
                    on every journey
                  </span>
                </h1>

                <p className="text-lg md:text-xl text-white/80 max-w-2xl leading-relaxed">
                  Transform the way you travel with our revolutionary car rental platform.
                  <span className="text-[#adbf3b] font-medium"> Seamless booking</span>,
                  <span className="text-white font-medium"> premium vehicles</span>, and
                  <span className="text-[#adbf3b] font-medium"> unforgettable experiences</span> await.
                </p>
              </div>

              {/* Interactive CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 items-start">
                <Link href="https://apps.apple.com/app/the-travella/id6743700616" target="_blank"
                      className="group relative overflow-hidden rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Image
                    src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg"
                    alt="Download on the App Store"
                    width={160}
                    height={48}
                    priority
                    className="relative z-10 transition-transform duration-300 group-hover:scale-105"
                  />
                </Link>

                <Link href="https://play.google.com/store/apps/details?id=com.prodevkampala.travella" target="_blank"
                      className="group relative overflow-hidden rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Image
                    src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png"
                    alt="Get it on Google Play"
                    width={180}
                    height={70}
                    priority
                    className="relative z-10 transition-transform duration-300 group-hover:scale-105"
                  />
                </Link>
              </div>

              {/* Social Proof with Animation */}
              <div className="flex items-center space-x-6 pt-4">
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-yellow-400 animate-pulse" style={{animationDelay: `${i * 0.1}s`}} />
                  ))}
                </div>
                <div className="text-white/90">
                  <span className="text-2xl font-bold text-cyan-300">10,000+</span>
                  <span className="text-sm ml-2">happy travelers</span>
                </div>
              </div>
            </div>

            {/* Right Content - Floating Phone with Glassmorphism */}
            <div className="lg:col-span-5 flex justify-center lg:justify-end">
              <div className="relative group">
                {/* Floating Glow Effect */}
                <div className="absolute -inset-4 bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500 rounded-3xl blur-2xl opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>

                {/* Phone Container with Glassmorphism */}
                <div className="relative h-[500px] w-[280px] md:h-[650px] md:w-[320px] rounded-[3rem] bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl overflow-hidden">
                  {/* Phone Notch */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black/80 rounded-b-2xl z-20 flex items-center justify-center">
                    <div className="w-16 h-1 bg-white/30 rounded-full"></div>
                  </div>

                  {/* Screen Content */}
                  <div className="absolute inset-2 top-8 rounded-[2.5rem] overflow-hidden">
                    <Image
                      src="/images/travella_app.png"
                      alt="Travella App Screenshot"
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-110"
                      priority
                      quality={90}
                    />

                    {/* Overlay Gradient */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-[#adbf3b]/10"></div>
                  </div>

                  {/* Floating UI Elements */}
                  <div className="absolute top-20 -right-8 bg-[#adbf3b]/20 backdrop-blur-md rounded-2xl p-3 border border-[#adbf3b]/30 animate-bounce" style={{animationDelay: '0.5s'}}>
                    <Car className="w-6 h-6 text-[#adbf3b]" />
                  </div>

                  <div className="absolute bottom-32 -left-8 bg-black/20 backdrop-blur-md rounded-2xl p-3 border border-white/30 animate-bounce" style={{animationDelay: '1.5s'}}>
                    <MapPin className="w-6 h-6 text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Features Section - Floating Cards with Depth */}
      <section id="features" className="relative py-20 md:py-32 overflow-hidden">
        {/* Background with Subtle Pattern */}
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(99,102,241,0.05),transparent_70%)]"></div>
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                             radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.1) 0%, transparent 50%)`
          }}></div>
        </div>

        <div className="container relative z-10 px-4 md:px-6">
          {/* Section Header with Creative Typography */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full px-6 py-2 mb-6">
              <Zap className="w-5 h-5 text-indigo-600" />
              <span className="text-sm font-medium text-indigo-600 dark:text-indigo-400">Why Choose Travella</span>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
              <span className="text-gray-900 dark:text-white">Experience the</span>
              <br />
              <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                Future of Travel
              </span>
            </h2>

            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
              Discover a new way to explore the world with our innovative car rental platform.
              Every feature is designed to make your journey seamless and memorable.
            </p>
          </div>

          {/* Floating Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6">
            {/* Card 1 - Wide Selection */}
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-500"></div>
              <div className="relative bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-xl border border-gray-200/50 dark:border-gray-700/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Car className="h-8 w-8 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-cyan-400 rounded-full animate-pulse"></div>
                </div>

                <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-cyan-600 transition-colors">
                  Endless Choices
                </h3>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  From sleek sedans to rugged SUVs, discover the perfect vehicle for every adventure and occasion.
                </p>

                <div className="mt-6 flex items-center text-cyan-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                  <span className="text-sm">Explore Fleet</span>
                  <ArrowRight className="w-4 h-4 ml-2" />
                </div>
              </div>
            </div>

            {/* Card 2 - Easy Booking */}
            <div className="group relative lg:mt-8">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-500"></div>
              <div className="relative bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-xl border border-gray-200/50 dark:border-gray-700/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Calendar className="h-8 w-8 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-pink-400 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
                </div>

                <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-purple-600 transition-colors">
                  Instant Booking
                </h3>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  Reserve your dream car in seconds with our streamlined booking process and real-time availability.
                </p>

                <div className="mt-6 flex items-center text-purple-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                  <span className="text-sm">Book Now</span>
                  <ArrowRight className="w-4 h-4 ml-2" />
                </div>
              </div>
            </div>

            {/* Card 3 - Flexible Pickup */}
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-500"></div>
              <div className="relative bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-xl border border-gray-200/50 dark:border-gray-700/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <MapPin className="h-8 w-8 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-400 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
                </div>

                <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-emerald-600 transition-colors">
                  Smart Locations
                </h3>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  Pick up your car wherever it's convenient - airports, hotels, or right at your doorstep.
                </p>

                <div className="mt-6 flex items-center text-emerald-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                  <span className="text-sm">Find Locations</span>
                  <ArrowRight className="w-4 h-4 ml-2" />
                </div>
              </div>
            </div>

            {/* Card 4 - Verified Rentals */}
            <div className="group relative lg:mt-8">
              <div className="absolute -inset-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-500"></div>
              <div className="relative bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-xl border border-gray-200/50 dark:border-gray-700/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Shield className="h-8 w-8 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-orange-400 rounded-full animate-pulse" style={{animationDelay: '1.5s'}}></div>
                </div>

                <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-orange-600 transition-colors">
                  Trust & Safety
                </h3>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  Every vehicle is thoroughly inspected and verified to ensure your safety and peace of mind.
                </p>

                <div className="mt-6 flex items-center text-orange-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                  <span className="text-sm">Learn More</span>
                  <ArrowRight className="w-4 h-4 ml-2" />
                </div>
              </div>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-20">
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-medium hover:shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer">
              <Globe className="w-5 h-5" />
              <span>Discover All Features</span>
              <ArrowRight className="w-5 h-5" />
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section - Interactive Journey */}
      <section id="how-it-works" className="relative py-20 md:py-32 overflow-hidden">
        {/* Dynamic Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-indigo-950 dark:to-purple-950">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(120,119,198,0.1),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(255,119,198,0.1),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_40%_40%,rgba(59,130,246,0.05),transparent_50%)]"></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-xl animate-pulse-slow"></div>
          <div className="absolute bottom-20 right-10 w-32 h-32 bg-gradient-to-br from-pink-400/20 to-orange-600/20 rounded-full blur-2xl animate-pulse-slow" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container relative z-10 px-4 md:px-6">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full px-6 py-2 mb-6">
              <Compass className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">Your Journey Starts Here</span>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
              <span className="text-gray-900 dark:text-white">Simple Steps to</span>
              <br />
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                Adventure
              </span>
            </h2>

            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
              Getting on the road has never been easier. Follow these three simple steps
              and you'll be driving your dream car in minutes.
            </p>
          </div>

          {/* Interactive Steps */}
          <div className="relative max-w-6xl mx-auto">
            {/* Connection Lines */}
            <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 dark:from-blue-800 dark:via-purple-800 dark:to-pink-800 transform -translate-y-1/2"></div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 lg:gap-8">
              {/* Step 1 */}
              <div className="group relative">
                <div className="flex flex-col items-center text-center">
                  {/* Step Number */}
                  <div className="relative mb-8">
                    <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-3xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-all duration-500 relative z-10">
                      <Download className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute -inset-2 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg">
                      1
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors">
                    Get Started
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed max-w-sm">
                    Download the Travella app and create your account in under 2 minutes.
                    It's free and takes just a few taps.
                  </p>

                  {/* Floating Elements */}
                  <div className="absolute -top-4 -left-4 w-6 h-6 bg-blue-400/30 rounded-full animate-ping"></div>
                  <div className="absolute -bottom-4 -right-4 w-4 h-4 bg-cyan-400/40 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
                </div>
              </div>

              {/* Step 2 */}
              <div className="group relative lg:mt-12">
                <div className="flex flex-col items-center text-center">
                  {/* Step Number */}
                  <div className="relative mb-8">
                    <div className="w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-600 rounded-3xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-all duration-500 relative z-10">
                      <Car className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute -inset-2 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-purple-400 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg">
                      2
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-purple-600 transition-colors">
                    Choose & Book
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed max-w-sm">
                    Browse our curated selection of vehicles, compare prices,
                    and book instantly with our smart recommendation engine.
                  </p>

                  {/* Floating Elements */}
                  <div className="absolute -top-4 -left-4 w-6 h-6 bg-purple-400/30 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                  <div className="absolute -bottom-4 -right-4 w-4 h-4 bg-pink-400/40 rounded-full animate-pulse" style={{animationDelay: '1.5s'}}></div>
                </div>
              </div>

              {/* Step 3 */}
              <div className="group relative">
                <div className="flex flex-col items-center text-center">
                  {/* Step Number */}
                  <div className="relative mb-8">
                    <div className="w-24 h-24 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-all duration-500 relative z-10">
                      <MapPin className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute -inset-2 bg-gradient-to-br from-emerald-500/30 to-teal-600/30 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-emerald-400 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg">
                      3
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-emerald-600 transition-colors">
                    Drive Away
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed max-w-sm">
                    Pick up your car at the location of your choice,
                    complete a quick digital check, and hit the road!
                  </p>

                  {/* Floating Elements */}
                  <div className="absolute -top-4 -left-4 w-6 h-6 bg-emerald-400/30 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
                  <div className="absolute -bottom-4 -right-4 w-4 h-4 bg-teal-400/40 rounded-full animate-pulse" style={{animationDelay: '2s'}}></div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Stats */}
          <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">2 min</div>
              <div className="text-gray-600 dark:text-gray-400">Average setup time</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-purple-600 mb-2">24/7</div>
              <div className="text-gray-600 dark:text-gray-400">Instant booking</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-emerald-600 mb-2">5 min</div>
              <div className="text-gray-600 dark:text-gray-400">Quick pickup</div>
            </div>
          </div>
        </div>
      </section>

      {/* About/Impact Section - Data Visualization */}
      <section id="about-us" className="relative py-20 md:py-32 overflow-hidden">
        {/* Sophisticated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-indigo-50 dark:from-gray-950 dark:via-gray-900 dark:to-indigo-950">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(99,102,241,0.05),transparent_70%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(168,85,247,0.05),transparent_70%)]"></div>
        </div>

        {/* Geometric Patterns */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <svg className="absolute top-0 left-0 w-full h-full opacity-30" viewBox="0 0 1200 800" fill="none">
            <defs>
              <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1" className="text-indigo-200 dark:text-indigo-800"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>

        <div className="container relative z-10 px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 items-center">
            {/* Content Side */}
            <div className="space-y-8">
              <div>
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full px-6 py-2 mb-6">
                  <Users className="w-5 h-5 text-indigo-600" />
                  <span className="text-sm font-medium text-indigo-600 dark:text-indigo-400">Our Impact</span>
                </div>

                <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
                  <span className="text-gray-900 dark:text-white">Connecting</span>
                  <br />
                  <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Communities
                  </span>
                </h2>

                <div className="space-y-6 text-lg text-gray-600 dark:text-gray-400 leading-relaxed">
                  <p>
                    Travella is revolutionizing the way people think about transportation.
                    We're not just a car rental platform – we're building a community of
                    <span className="text-indigo-600 font-medium"> explorers</span>,
                    <span className="text-purple-600 font-medium"> entrepreneurs</span>, and
                    <span className="text-pink-600 font-medium"> dreamers</span>.
                  </p>
                  <p>
                    Every vehicle on our platform is carefully curated and verified to ensure
                    the highest standards of quality and safety. Our mission is to make premium
                    transportation accessible to everyone, everywhere.
                  </p>
                  <p>
                    Join thousands of car owners earning extra income and travelers discovering
                    new adventures. Together, we're creating the future of mobility.
                  </p>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center gap-2 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-200/50 dark:border-gray-700/50">
                  <Shield className="w-5 h-5 text-green-600" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Verified Safe</span>
                </div>
                <div className="flex items-center gap-2 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-200/50 dark:border-gray-700/50">
                  <Clock className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">24/7 Support</span>
                </div>
                <div className="flex items-center gap-2 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-200/50 dark:border-gray-700/50">
                  <Star className="w-5 h-5 text-yellow-600" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">5-Star Rated</span>
                </div>
              </div>
            </div>

            {/* Interactive Stats Visualization */}
            <div className="relative">
              {/* Background Glow */}
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-purple-500/20 to-pink-500/20 rounded-3xl blur-3xl"></div>

              <div className="relative bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-3xl p-8 border border-white/20 dark:border-gray-700/20 shadow-2xl">
                <div className="grid grid-cols-2 gap-6">
                  {/* Stat 1 */}
                  <div className="group relative overflow-hidden bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/50 dark:to-cyan-950/50 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full -translate-y-10 translate-x-10"></div>
                    <Car className="w-8 h-8 text-blue-600 mb-3" />
                    <div className="text-3xl font-bold text-blue-600 mb-1">5,000+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Premium Vehicles</div>
                  </div>

                  {/* Stat 2 */}
                  <div className="group relative overflow-hidden bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/50 dark:to-pink-950/50 rounded-2xl p-6 hover:scale-105 transition-all duration-300 mt-8">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full -translate-y-10 translate-x-10"></div>
                    <Download className="w-8 h-8 text-purple-600 mb-3" />
                    <div className="text-3xl font-bold text-purple-600 mb-1">10,000+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">App Downloads</div>
                  </div>

                  {/* Stat 3 */}
                  <div className="group relative overflow-hidden bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950/50 dark:to-teal-950/50 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-emerald-400/20 to-teal-400/20 rounded-full -translate-y-10 translate-x-10"></div>
                    <Users className="w-8 h-8 text-emerald-600 mb-3" />
                    <div className="text-3xl font-bold text-emerald-600 mb-1">12,000+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Happy Travelers</div>
                  </div>

                  {/* Stat 4 */}
                  <div className="group relative overflow-hidden bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/50 dark:to-red-950/50 rounded-2xl p-6 hover:scale-105 transition-all duration-300 mt-8">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-400/20 to-red-400/20 rounded-full -translate-y-10 translate-x-10"></div>
                    <Globe className="w-8 h-8 text-orange-600 mb-3" />
                    <div className="text-3xl font-bold text-orange-600 mb-1">50+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Cities Worldwide</div>
                  </div>
                </div>

                {/* Growth Indicator */}
                <div className="mt-8 text-center">
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-full px-4 py-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-green-600">Growing every day</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* App Experience Section - Immersive Showcase */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        {/* Dynamic Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
          <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/20 via-transparent to-cyan-500/20"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_50%,rgba(99,102,241,0.3),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_50%,rgba(168,85,247,0.3),transparent_50%)]"></div>
        </div>

        {/* Floating Orbs */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-10 w-32 h-32 bg-gradient-to-br from-cyan-400/30 to-blue-600/30 rounded-full blur-2xl animate-pulse-slow"></div>
          <div className="absolute bottom-1/4 right-10 w-40 h-40 bg-gradient-to-br from-pink-400/30 to-purple-600/30 rounded-full blur-3xl animate-pulse-slow" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-gradient-to-br from-violet-400/20 to-indigo-600/20 rounded-full blur-xl animate-pulse-slow" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container relative z-10 px-4 md:px-6">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-6 py-2 mb-6 border border-white/20">
              <Sparkles className="w-5 h-5 text-cyan-300" />
              <span className="text-sm font-medium text-white">Experience Excellence</span>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-white mb-6">
              <span className="block">Designed for</span>
              <span className="block bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                Perfection
              </span>
            </h2>

            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
              Every pixel crafted with care. Every interaction designed to delight.
              Experience the future of car rental in the palm of your hand.
            </p>
          </div>

          {/* Interactive Phone Showcase */}
          <div className="relative max-w-6xl mx-auto">
            <Suspense fallback={
              <div className="h-[600px] w-full flex items-center justify-center">
                <div className="text-white/60">Loading experience...</div>
              </div>
            }>
              {/* Desktop View - Multiple Phones */}
              <div className="hidden lg:flex justify-center items-center gap-8">
                {screenshots.map((screenshot, index) => (
                  <div
                    key={index}
                    className="group relative"
                    style={{
                      transform: `translateY(${index % 2 === 0 ? '20px' : '-20px'}) scale(${index === 1 || index === 2 ? '1.1' : '0.9'})`,
                      zIndex: index === 1 || index === 2 ? 10 : 5
                    }}
                  >
                    {/* Glow Effect */}
                    <div className="absolute -inset-4 bg-gradient-to-r from-cyan-500/30 via-purple-500/30 to-pink-500/30 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Phone Container */}
                    <div className="relative h-[480px] w-[240px] rounded-[2.5rem] bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl overflow-hidden group-hover:scale-105 transition-all duration-500">
                      {/* Phone Notch */}
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-24 h-6 bg-black/80 rounded-b-2xl z-20 flex items-center justify-center">
                        <div className="w-12 h-1 bg-white/30 rounded-full"></div>
                      </div>

                      {/* Screen */}
                      <div className="absolute inset-2 top-8 rounded-[2rem] overflow-hidden">
                        <Image
                          src={screenshot.src}
                          alt={screenshot.alt}
                          fill
                          className="object-cover transition-transform duration-700 group-hover:scale-110"
                          loading="lazy"
                          sizes="240px"
                          quality={85}
                        />

                        {/* Screen Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-purple-900/20 via-transparent to-cyan-900/20"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Mobile/Tablet View - Single Featured Phone */}
              <div className="flex lg:hidden justify-center">
                <div className="group relative">
                  {/* Enhanced Glow */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-cyan-500/40 via-purple-500/40 to-pink-500/40 rounded-3xl blur-3xl opacity-60 group-hover:opacity-80 transition-opacity duration-500"></div>

                  {/* Main Phone */}
                  <div className="relative h-[600px] w-[320px] rounded-[3rem] bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl overflow-hidden">
                    {/* Phone Notch */}
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black/80 rounded-b-2xl z-20 flex items-center justify-center">
                      <div className="w-16 h-1 bg-white/30 rounded-full"></div>
                    </div>

                    {/* Screen */}
                    <div className="absolute inset-2 top-8 rounded-[2.5rem] overflow-hidden">
                      <Image
                        src="/images/travella_app.png"
                        alt="Travella App Experience"
                        fill
                        className="object-cover"
                        loading="lazy"
                        sizes="320px"
                        quality={90}
                      />

                      {/* Interactive Elements */}
                      <div className="absolute inset-0 bg-gradient-to-t from-purple-900/10 via-transparent to-cyan-900/10"></div>
                    </div>

                    {/* Floating UI Elements */}
                    <div className="absolute top-24 -right-6 bg-white/20 backdrop-blur-md rounded-2xl p-3 border border-white/30 animate-bounce" style={{animationDelay: '0.5s'}}>
                      <Car className="w-6 h-6 text-cyan-300" />
                    </div>

                    <div className="absolute bottom-40 -left-6 bg-white/20 backdrop-blur-md rounded-2xl p-3 border border-white/30 animate-bounce" style={{animationDelay: '1.5s'}}>
                      <MapPin className="w-6 h-6 text-pink-300" />
                    </div>

                    <div className="absolute top-1/2 -right-8 bg-white/20 backdrop-blur-md rounded-2xl p-3 border border-white/30 animate-bounce" style={{animationDelay: '2.5s'}}>
                      <Star className="w-6 h-6 text-yellow-300" />
                    </div>
                  </div>
                </div>
              </div>
            </Suspense>
          </div>

          {/* Feature Highlights */}
          <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Lightning Fast</h3>
              <p className="text-white/70">Book a car in under 30 seconds</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Secure & Safe</h3>
              <p className="text-white/70">Bank-level security for all transactions</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Community Driven</h3>
              <p className="text-white/70">Powered by real people, real reviews</p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section - Social Proof */}
      <section id="testimonials" className="relative py-20 md:py-32 overflow-hidden">
        {/* Elegant Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-indigo-50 dark:from-gray-950 dark:via-gray-900 dark:to-indigo-950">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(99,102,241,0.05),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,rgba(168,85,247,0.05),transparent_50%)]"></div>
        </div>

        <div className="container relative z-10 px-4 md:px-6">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full px-6 py-2 mb-6">
              <Star className="w-5 h-5 text-indigo-600" />
              <span className="text-sm font-medium text-indigo-600 dark:text-indigo-400">Customer Love</span>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
              <span className="text-gray-900 dark:text-white">Stories from our</span>
              <br />
              <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                Community
              </span>
            </h2>

            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
              Real experiences from real travelers who've discovered the joy of seamless car rental.
            </p>
          </div>

          {/* Featured Testimonial */}
          <div className="max-w-5xl mx-auto">
            <div className="relative group">
              {/* Background Glow */}
              <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-pink-500/20 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="relative bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-3xl p-8 md:p-12 border border-white/20 dark:border-gray-700/20 shadow-2xl">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                  {/* Customer Photo */}
                  <div className="flex justify-center lg:justify-start">
                    <div className="relative">
                      <div className="w-32 h-32 md:w-40 md:h-40 rounded-full overflow-hidden border-4 border-white/50 dark:border-gray-700/50 shadow-xl">
                        <Image
                          src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=200&auto=format&fit=crop"
                          alt="Michael Chen - Happy Customer"
                          width={200}
                          height={200}
                          className="object-cover w-full h-full"
                          loading="lazy"
                          quality={85}
                        />
                      </div>

                      {/* Floating Quote Icon */}
                      <div className="absolute -top-2 -right-2 w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Testimonial Content */}
                  <div className="lg:col-span-2 space-y-6">
                    <div className="flex items-center gap-2 mb-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-6 h-6 text-yellow-400 fill-yellow-400" />
                      ))}
                    </div>

                    <blockquote className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 leading-relaxed italic">
                      "Travella completely transformed my business travel experience. The seamless booking process,
                      pristine vehicle condition, and effortless pickup made everything so convenient.
                      It's now my go-to solution for all car rental needs!"
                    </blockquote>

                    <div className="flex items-center justify-between pt-6 border-t border-gray-200/50 dark:border-gray-700/50">
                      <div>
                        <p className="text-lg font-bold text-gray-900 dark:text-white">Michael Chen</p>
                        <p className="text-gray-600 dark:text-gray-400">Business Consultant • San Francisco</p>
                      </div>

                      <div className="hidden md:flex items-center gap-2 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-full px-4 py-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium text-green-600">Verified Customer</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Social Proof */}
            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
                <div className="text-3xl font-bold text-indigo-600 mb-2">4.9/5</div>
                <div className="text-gray-600 dark:text-gray-400">Average Rating</div>
              </div>
              <div className="text-center p-6 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
                <div className="text-3xl font-bold text-purple-600 mb-2">98%</div>
                <div className="text-gray-600 dark:text-gray-400">Customer Satisfaction</div>
              </div>
              <div className="text-center p-6 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
                <div className="text-3xl font-bold text-pink-600 mb-2">24/7</div>
                <div className="text-gray-600 dark:text-gray-400">Support Available</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section - Grand Finale */}
      <section id="download" className="relative py-20 md:py-32 overflow-hidden">
        {/* Epic Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
          <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/30 via-transparent to-cyan-500/30"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(99,102,241,0.4),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(168,85,247,0.4),transparent_50%)]"></div>
        </div>

        {/* Animated Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-40 h-40 bg-gradient-to-br from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute bottom-20 right-10 w-32 h-32 bg-gradient-to-br from-pink-400/20 to-purple-600/20 rounded-full blur-2xl animate-pulse-slow" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-gradient-to-br from-violet-400/15 to-indigo-600/15 rounded-full blur-xl animate-pulse-slow" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container relative z-10 px-4 md:px-6">
          <div className="grid gap-12 lg:grid-cols-2 lg:gap-16 items-center">
            {/* Content Side */}
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-6 py-2 border border-white/20">
                  <Sparkles className="w-5 h-5 text-cyan-300" />
                  <span className="text-sm font-medium text-white">Start Your Journey</span>
                </div>

                <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-white leading-[1.1]">
                  <span className="block">Your next</span>
                  <span className="block bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                    Adventure
                  </span>
                  <span className="block text-3xl md:text-4xl lg:text-5xl font-light text-white/90">
                    awaits
                  </span>
                </h2>

                <p className="text-xl text-white/80 max-w-2xl leading-relaxed">
                  Join thousands of travelers who've discovered the freedom of seamless car rental.
                  Download Travella today and unlock a world of possibilities.
                </p>
              </div>

              {/* Enhanced CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="https://apps.apple.com/app/the-travella/id6743700616" target="_blank"
                      className="group relative overflow-hidden rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Image
                    src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg"
                    alt="Download on the App Store"
                    width={160}
                    height={48}
                    priority
                    className="relative z-10 transition-transform duration-300 group-hover:scale-105"
                  />
                </Link>

                <Link href="https://play.google.com/store/apps/details?id=com.prodevkampala.travella" target="_blank"
                      className="group relative overflow-hidden rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Image
                    src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png"
                    alt="Get it on Google Play"
                    width={180}
                    height={70}
                    priority
                    className="relative z-10 transition-transform duration-300 group-hover:scale-105"
                  />
                </Link>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap items-center gap-6 pt-4">
                <div className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span className="text-white/90 text-sm">Secure & Trusted</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-blue-400" />
                  <span className="text-white/90 text-sm">10,000+ Users</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-yellow-400" />
                  <span className="text-white/90 text-sm">4.9 Rating</span>
                </div>
              </div>
            </div>

            {/* Phone Showcase */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative group">
                {/* Epic Glow Effect */}
                <div className="absolute -inset-8 bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500 rounded-3xl blur-3xl opacity-40 group-hover:opacity-60 transition-opacity duration-500"></div>

                {/* Phone Container */}
                <div className="relative h-[550px] w-[300px] md:h-[650px] md:w-[350px] rounded-[3rem] bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl overflow-hidden group-hover:scale-105 transition-all duration-500">
                  {/* Phone Notch */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black/80 rounded-b-2xl z-20 flex items-center justify-center">
                    <div className="w-16 h-1 bg-white/30 rounded-full"></div>
                  </div>

                  {/* Screen Content */}
                  <div className="absolute inset-2 top-8 rounded-[2.5rem] overflow-hidden">
                    <Image
                      src="/images/travella_app.png"
                      alt="Travella App - Your Journey Starts Here"
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-110"
                      loading="lazy"
                      sizes="(max-width: 768px) 300px, 350px"
                      quality={90}
                    />

                    {/* Screen Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-purple-900/20 via-transparent to-cyan-900/20"></div>
                  </div>

                  {/* Floating Success Elements */}
                  <div className="absolute top-24 -right-6 bg-white/20 backdrop-blur-md rounded-2xl p-3 border border-white/30 animate-bounce" style={{animationDelay: '0.5s'}}>
                    <Check className="w-6 h-6 text-green-300" />
                  </div>

                  <div className="absolute bottom-40 -left-6 bg-white/20 backdrop-blur-md rounded-2xl p-3 border border-white/30 animate-bounce" style={{animationDelay: '1.5s'}}>
                    <ArrowRight className="w-6 h-6 text-cyan-300" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

