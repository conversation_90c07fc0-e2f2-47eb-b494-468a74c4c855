import Link from "next/link"
import Image from "next/image"
import { ArrowRight, Check, Download, Star, Car, Shield, CreditCard, Calendar, MapPin, Phone, Menu, X, Send, Repeat, ExternalLink, Users, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button-new"
import { Card, CardContent } from "@/components/ui/card-new"
import { Badge } from "@/components/ui/badge-new"
import { Suspense } from "react"

// Predefine screenshots array for better performance
const screenshots = [
  {
    src: "/images/1.jpg",
    alt: "App Screenshot 1"
  },
  {
    src: "/images/2.jpg",
    alt: "App Screenshot 2"
  },
  {
    src: "/images/3.jpg",
    alt: "App Screenshot 3"
  },
  {
    src: "/images/4.jpg",
    alt: "App Screenshot 4"
  },
]

export default function LandingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Hero Section */}
      <section className="w-full pt-24 pb-12 md:pt-32 md:py-24 lg:py-32 xl:py-40 bg-indigo-600 dark:bg-indigo-950">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 xl:grid-cols-2">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <Badge variant="outline" className="mb-2 bg-white/10 text-white border-white/20">
                  Your #1 Car Rental App
                </Badge>
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none text-white">
                  Listing & <span className="text-indigo-200">Renting</span> cars made simple
                </h1>
                <p className="max-w-[600px] text-indigo-100 md:text-xl">
                  Find and rent the perfect car for any occasion. Download our mobile app and get access to hundreds of vehicles at competitive prices.
                </p>
              </div>
              <div className="flex items-center flex-col gap-2 min-[400px]:flex-row">
                <Link href="https://apps.apple.com/app/the-travella/id6743700616" target="_blank" className="inline-block hover:opacity-90 transition-opacity">
                  <Image 
                    src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg" 
                    alt="Download on the App Store" 
                    width={140} 
                    height={42}
                    priority
                  />
                </Link>
                <Link href="https://play.google.com/store/apps/details?id=com.prodevkampala.travella" target="_blank" className="inline-block hover:opacity-90 transition-opacity">
                  <Image 
                    src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png" 
                    alt="Get it on Google Play" 
                    width={160} 
                    height={62}
                    priority
                  />
                </Link>
              </div>
              <div className="flex items-center space-x-4 mt-4">
                <div className="flex items-center">
                  <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />
                  <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />
                  <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />
                  <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />
                  <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />
                </div>
                <span className="text-sm text-indigo-100">Over 10,000 happy customers</span>
              </div>
            </div>
            <div className="flex items-center justify-center mt-8 lg:mt-0">
              <div className="relative h-[450px] w-[280px] md:h-[600px] md:w-[300px] overflow-hidden rounded-3xl border-8 border-indigo-500 dark:border-indigo-400 shadow-2xl bg-white">
                <div className="absolute top-0 w-full h-7 bg-indigo-500 dark:bg-indigo-400 rounded-t-2xl z-10 flex items-center justify-center">
                  <div className="w-16 h-1 bg-white/20 rounded-full"></div>
                </div>
                <Image
                  src="/images/travella_app.png"
                  alt="Travella App Screenshot"
                  fill
                  className="object-cover"
                  priority
                  quality={85}
                  style={{ 
                    objectFit: 'cover',
                    objectPosition: 'top',
                    borderRadius: '1.25rem', 
                    padding: '0',
                    paddingTop: '1.75rem'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="w-full py-12 md:py-24 lg:py-32 bg-white dark:bg-gray-950">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2 max-w-3xl">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-gray-900 dark:text-white">Save time and money with <span className="text-indigo-600 dark:text-indigo-400">Travella</span></h2>
              <p className="text-xl text-gray-500 dark:text-gray-400">
                Our app makes finding and renting cars simple and convenient. Here's why users love Travella.
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mt-16">
            <div className="bg-[#E7FFE9] dark:bg-emerald-950/50 p-6 md:p-8 rounded-xl">
              <div className="w-12 h-12 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center mb-6 shadow-sm">
                <Car className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 dark:text-white">Wide Selection</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Browse hundreds of cars from economy to luxury vehicles for any occasion, all in one place.
              </p>
            </div>
            
            <div className="bg-[#E7FFE9] dark:bg-emerald-950/50 p-6 md:p-8 rounded-xl">
              <div className="w-12 h-12 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center mb-6 shadow-sm">
                <Calendar className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 dark:text-white">Easy Booking</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Book your ideal car with just a few taps. Our simplified process makes reservations quick and hassle-free.
              </p>
            </div>
            
            <div className="bg-[#E7FFE9] dark:bg-emerald-950/50 p-6 md:p-8 rounded-xl">
              <div className="w-12 h-12 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center mb-6 shadow-sm">
                <MapPin className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 dark:text-white">Flexible Pickup</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Choose from multiple convenient pickup locations across the city, including airport and downtown spots.
              </p>
            </div>
            
            <div className="bg-[#E7FFE9] dark:bg-emerald-950/50 p-6 md:p-8 rounded-xl">
              <div className="w-12 h-12 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center mb-6 shadow-sm">
                <Shield className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 dark:text-white">Verified Rentals</h3>
              <p className="text-gray-600 dark:text-gray-300">
                All cars on our platform are verified and inspected regularly to ensure quality, safety, and reliability.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="w-full py-12 md:py-24 lg:py-32 bg-gray-100 dark:bg-gray-900">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-12 md:mb-16">
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-2">HOW IT WORKS</p>
            <h2 className="text-3xl md:text-4xl font-bold">
              <span className="text-gray-900 dark:text-white">How it</span> <span className="text-indigo-600 dark:text-indigo-400">works</span>
            </h2>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10 max-w-4xl mx-auto">
            <div className="flex flex-col items-center text-center">
              <div className="w-14 h-14 bg-[#E7FFE9] dark:bg-emerald-900/40 rounded-full flex items-center justify-center mb-6">
                <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                  <Download className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 dark:text-white">Download Travella</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Get the Travella app from the App Store or Google Play and create your account in minutes.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="w-14 h-14 bg-[#E7FFE9] dark:bg-emerald-900/40 rounded-full flex items-center justify-center mb-6">
                <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                  <Car className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 dark:text-white">Browse & Book</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Search for available cars by location, date, and preferences. Select and book your ideal vehicle.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="w-14 h-14 bg-[#E7FFE9] dark:bg-emerald-900/40 rounded-full flex items-center justify-center mb-6">
                <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                  <MapPin className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 dark:text-white">Pickup & Go</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Collect your car at the designated location, complete a quick check, and start your journey.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* About/Partners Section */}
      <section id="about-us" className="w-full py-12 md:py-24 lg:py-32 bg-white dark:bg-gray-950">
        <div className="container px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 md:gap-16 items-center">
            <div>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-2">ABOUT US</p>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                <span className="text-gray-900 dark:text-white">Travella</span> <span className="text-indigo-600 dark:text-indigo-400">Partners</span>
              </h2>
              <div className="text-gray-600 dark:text-gray-400 space-y-4">
                <p>
                  Travella is the leading car rental platform connecting car owners with travelers looking for quality vehicles. Our mission is to make car rental accessible, affordable, and enjoyable for everyone.
                </p>
                <p>
                  We carefully vet all vehicles on our platform to ensure they meet our high standards of safety and comfort. With Travella, you can rent with confidence knowing you're getting a quality car at a competitive price.
                </p>
                <p>
                  Whether you're a car owner looking to earn extra income from your vehicle or a traveler in need of a reliable car, Travella is your trusted partner for all car rental needs.
                </p>
              </div>
            </div>
            
            <div className="relative mt-10 lg:mt-0">
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div className="bg-indigo-100 dark:bg-indigo-900/40 rounded-full w-28 h-28 md:w-32 md:h-32 flex items-center justify-center">
                  <div className="text-center">
                    <p className="text-2xl md:text-3xl font-bold text-indigo-600 dark:text-indigo-400">5k</p>
                    <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Cars Listed</p>
                  </div>
                </div>
                
                <div className="bg-indigo-100 dark:bg-indigo-900/40 rounded-full w-28 h-28 md:w-32 md:h-32 flex items-center justify-center mt-8 md:mt-12">
                  <div className="text-center">
                    <p className="text-2xl md:text-3xl font-bold text-indigo-600 dark:text-indigo-400">10k</p>
                    <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Downloads</p>
                  </div>
                </div>
                
                <div className="bg-indigo-100 dark:bg-indigo-900/40 rounded-full w-28 h-28 md:w-32 md:h-32 flex items-center justify-center">
                  <div className="text-center">
                    <p className="text-2xl md:text-3xl font-bold text-indigo-600 dark:text-indigo-400">12k</p>
                    <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Happy Users</p>
                  </div>
                </div>
                
                <div className="bg-indigo-100 dark:bg-indigo-900/40 rounded-full w-28 h-28 md:w-32 md:h-32 flex items-center justify-center mt-8 md:mt-12">
                  <div className="text-center">
                    <p className="text-2xl md:text-3xl font-bold text-indigo-600 dark:text-indigo-400">50+</p>
                    <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Cities</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* App Screenshots Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-indigo-600 dark:bg-indigo-950">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10 md:mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Simple and Intuitive
            </h2>
            <p className="text-xl text-indigo-100 md:max-w-2xl mx-auto">
              Travella App
            </p>
            <p className="text-indigo-200 md:max-w-2xl mx-auto">
              Intuitive and easy-to-use interface that makes finding and renting the perfect car a breeze.
            </p>
          </div>
          
          {/* Mobile-optimized screenshots display */}
          <Suspense fallback={<div className="h-[500px] w-full flex items-center justify-center"><p className="text-white">Loading screenshots...</p></div>}>
            <div className="hidden md:flex justify-center">
              <div className="relative flex -mx-4">
                {screenshots.map((screenshot, index) => (
                  <div 
                    key={index} 
                    className="px-1 md:px-2"
                    style={{ 
                      transform: index === 3 ? 'translateY(0)' : 
                              index % 2 === 0 ? 'translateY(20px)' : 'translateY(-20px)'
                    }}
                  >
                    <div className="relative h-[400px] w-[180px] md:h-[500px] md:w-[220px] rounded-3xl overflow-hidden border-8 border-indigo-500 dark:border-indigo-400 shadow-2xl bg-white">
                      <div className="absolute top-0 w-full h-7 bg-indigo-500 dark:bg-indigo-400 rounded-t-2xl z-10 flex items-center justify-center">
                        <div className="w-12 h-1 bg-white/20 rounded-full"></div>
                      </div>
                      <Image
                        src={screenshot.src}
                        alt={screenshot.alt}
                        fill
                        className="object-cover"
                        loading="lazy"
                        sizes="(max-width: 768px) 180px, 220px"
                        quality={75}
                        style={{ 
                          objectFit: 'cover',
                          objectPosition: 'top',
                          borderRadius: '1rem', 
                          padding: '0',
                          paddingTop: '1rem'
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Suspense>
          
          {/* Mobile view - Single centered phone */}
          <div className="flex md:hidden justify-center">
            <div className="relative h-[500px] w-[250px] rounded-3xl overflow-hidden border-8 border-indigo-500 dark:border-indigo-400 shadow-2xl bg-white">
              <div className="absolute top-0 w-full h-7 bg-indigo-500 dark:bg-indigo-400 rounded-t-2xl z-10 flex items-center justify-center">
                <div className="w-12 h-1 bg-white/20 rounded-full"></div>
              </div>
              <Image
                src="/images/travella_app.png"
                alt="App Screenshot"
                fill
                className="object-cover"
                loading="lazy"
                sizes="250px"
                quality={80}
                style={{ 
                  objectFit: 'cover',
                  objectPosition: 'top',
                  borderRadius: '1rem', 
                  padding: '0',
                  paddingTop: '1.75rem'
                }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="w-full py-12 md:py-24 lg:py-32 bg-white dark:bg-gray-950">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10 md:mb-16">
            <div className="space-y-2 max-w-3xl">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-gray-900 dark:text-white">
                <span>Kind words from</span>
                <br className="md:hidden"/>
                <span> our lovely</span> <span className="text-indigo-600 dark:text-indigo-400">customers</span>
              </h2>
            </div>
          </div>
          
          <div className="max-w-3xl mx-auto">
            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-6 md:p-8 flex flex-col md:flex-row gap-8 items-center">
              <div className="w-24 h-24 md:w-40 md:h-40 rounded-full overflow-hidden flex-shrink-0 bg-indigo-100 dark:bg-indigo-900/40">
                <Image
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=150&auto=format&fit=crop"
                  alt="Customer portrait"
                  width={150}
                  height={150}
                  className="object-cover w-full h-full"
                  loading="lazy"
                  quality={80}
                />
              </div>
              <div className="space-y-4">
                <p className="text-base md:text-lg text-gray-600 dark:text-gray-300 italic">
                  "Travella made my business trip so convenient. The car was in perfect condition, pickup was seamless, and the entire rental process was incredibly smooth. Definitely my go-to car rental app now!"
                </p>
                <div>
                  <p className="font-bold text-gray-900 dark:text-white">Michael Chen</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Business Traveler</p>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-8 h-2 rounded-full bg-indigo-600 dark:bg-indigo-400"></div>
                  <div className="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-700"></div>
                  <div className="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-700"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section id="download" className="w-full py-12 md:py-24 lg:py-32 bg-indigo-600 dark:bg-indigo-950">
        <div className="container px-4 md:px-6">
          <div className="grid gap-8 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-white">
                  Listing & renting cars
                  <br/>
                  made simple.
                </h2>
                <p className="max-w-[600px] text-indigo-100 md:text-xl">
                  Start your journey with Travella. Download our app now and get access to hundreds of quality cars at competitive prices.
                </p>
              </div>
              <div className="flex flex-wrap items-center gap-4 pt-6">
                <Link href="https://apps.apple.com/app/the-travella/id6743700616" target="_blank" className="inline-block hover:opacity-90 transition-opacity">
                  <Image 
                    src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg" 
                    alt="Download on the App Store" 
                    width={140} 
                    height={42}
                    priority
                  />
                </Link>
                <Link href="https://play.google.com/store/apps/details?id=com.prodevkampala.travella" target="_blank" className="inline-block hover:opacity-90 transition-opacity">
                  <Image 
                    src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png" 
                    alt="Get it on Google Play" 
                    width={160} 
                    height={62}
                    priority
                  />
                </Link>
              </div>
            </div>
            <div className="flex items-center justify-center lg:justify-end mt-8 lg:mt-0">
              <div className="relative h-[450px] w-[250px] md:h-[600px] md:w-[300px] rounded-3xl border-8 border-indigo-500 dark:border-indigo-400 shadow-2xl bg-white">
                <div className="absolute top-0 w-full h-7 bg-indigo-500 dark:bg-indigo-400 rounded-t-2xl z-10 flex items-center justify-center">
                  <div className="w-16 h-1 bg-white/20 rounded-full"></div>
                </div>
                <Image
                  src="/images/travella_app.png"
                  alt="Travella App Screenshot"
                  fill
                  className="object-cover"
                  loading="lazy"
                  sizes="(max-width: 768px) 250px, 300px"
                  quality={80}
                  style={{ 
                    objectFit: 'cover',
                    objectPosition: 'top',
                    borderRadius: '1.25rem',
                    padding: '0',
                    paddingTop: '1.75rem'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

