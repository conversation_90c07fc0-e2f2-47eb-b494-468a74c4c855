import React from "react";
import { cn } from "@/lib/utils";
import PolicyNavClient from "@/components/marketing/policy-nav-client";

// Server component wrapper
export default function PoliciesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="container py-10">
      <div className="flex flex-col space-y-8 lg:space-y-0 lg:flex-row lg:space-x-12">
        <div className="lg:w-1/4">
          <div className="sticky top-20">
            <div className="mb-4">
              <h2 className="text-2xl font-bold">Legal Matters</h2>
              <p className="text-sm text-muted-foreground mt-1">
                Important information about using Travella
              </p>
            </div>
            <PolicyNavClient />
          </div>
        </div>
        <div className="lg:w-3/4">
          <div className="prose prose-gray max-w-none dark:prose-invert">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
} 