import React from "react";

export const metadata = {
  title: "Support - Travella",
  description: "Get help with Travella services, bookings, and account management.",
};

export default function SupportPage() {
  const currentDate = new Date();
  const formattedDate = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(currentDate);

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-4">Support Center</h1>
        <p className="text-muted-foreground">Last updated: {formattedDate}</p>
      </div>

      <p className="text-lg">
        Welcome to Travella Support. We're here to help you with any questions or issues you might have with our services. 
        Browse through our support topics below or contact our support team directly.
      </p>

      <div className="space-y-12">
        <section id="contact-us" className="space-y-4">
          <h2 className="text-2xl font-semibold">Contact Us</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="border rounded-lg p-6 space-y-4">
              <h3 className="text-xl font-medium">Customer Support</h3>
              <p>Our team is available 24/7 to assist with booking issues, account questions, and general support.</p>
              <div className="pt-2">
                <p className="font-medium">Email: <EMAIL></p>
                <p className="font-medium">Phone: +256 (780) 101-601</p>
              </div>
            </div>
            <div className="border rounded-lg p-6 space-y-4">
              <h3 className="text-xl font-medium">Emergency Support</h3>
              <p>For urgent assistance during a trip or with an active booking.</p>
              <div className="pt-2">
                {/* Update the contact number to the one in the database */}
                <p className="font-medium">Emergency Phone: +256 (780) 101-601</p>
                <p className="text-sm">Available 24/7 for active bookings only</p>
              </div>
            </div>
          </div>
        </section>

        <section id="faq" className="space-y-4">
          <h2 className="text-2xl font-semibold">Frequently Asked Questions</h2>
          
          <div className="space-y-6">
            <div className="border rounded-lg p-6">
              <h3 className="text-xl font-medium mb-4">Booking Questions</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold">How do I book a trip?</h4>
                  <p>Select desired vehicle type, brand, model, and select your dates, and browse available options. Choose the one that suits your needs, review the details, and follow the checkout process to complete your booking.</p>
                </div>
                <div>
                  <h4 className="font-semibold">Can I modify my booking?</h4>
                  <p>Yes, you can modify your booking through your account dashboard under "Trips" tab. Some changes may incur additional fees depending on the timing and nature of the modification.</p>
                </div>
                <div>
                  <h4 className="font-semibold">What if I need to cancel my booking?</h4>
                  <p>Cancellation policies vary by booking type. Please refer to the specific cancellation policy shown on your booking confirmation or visit our <a href="/policies/cancellation" className="text-primary underline">Cancellation Policy</a> page.</p>
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-6">
              <h3 className="text-xl font-medium mb-4">Account Management</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold">How do I reset my password?</h4>
                  <p>Click on "Forgot Password" on the login page, enter your email address, and follow the instructions sent to your email to reset your password.</p>
                </div>
                <div>
                  <h4 className="font-semibold">How do I update my profile information?</h4>
                  <p>Log in to your account, navigate to "Account Settings" or "Profile," and update your information as needed.</p>
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-6">
              <h3 className="text-xl font-medium mb-4">Using Travella</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold">How do I search for specific features or amenities?</h4>
                  <p>Use the filters on the search page to narrow down your options based on specific features, amenities, or preferences.</p>
                </div>
                <div>
                  <h4 className="font-semibold">What should I do if I encounter an issue during my trip?</h4>
                  <p>Contact our 24/7 emergency support line at +256 (780) 101-601. You can also report issues through the app by going to "Trips" then select your trip then "Help."</p>
                </div>
                <div>
                  <h4 className="font-semibold">How do reviews work?</h4>
                  <p>After completing a trip, you'll receive a prompt to leave a review. Your honest feedback helps our community make informed decisions.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section id="troubleshooting" className="space-y-4">
          <h2 className="text-2xl font-semibold">Troubleshooting Guide</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-medium mb-2">App Issues</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li>Ensure you have the latest version of the app installed</li>
                <li>Try clearing the app cache in your device settings</li>
                <li>Check your internet connection</li>
                <li>If problems persist, try uninstalling and reinstalling the app</li>
                <li>Contact support if issues continue</li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-medium mb-2">Payment Issues</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li>Verify your payment method details are correct</li>
                <li>Check with your bank to ensure there are no holds or restrictions</li>
                <li>Try using an alternative payment method</li>
                <li>Contact our support team for assistance with failed payments</li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-medium mb-2">Booking Issues</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li>Check your confirmation email for booking details</li>
                <li>Verify dates, times, and location information</li>
                <li>Ensure all required information is completed in your profile</li>
                <li>Contact support if you're unable to complete a booking</li>
              </ul>
            </div>
          </div>
        </section>

        <section id="resources" className="space-y-4">
          <h2 className="text-2xl font-semibold">Additional Resources</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="border rounded-lg p-6 space-y-4">
              <h3 className="text-xl font-medium">Community Guidelines</h3>
              <p>Learn about our community values and expected behavior when using Travella.</p>
              <div className="pt-2">
                <a href="/policies/community-guidelines" className="text-primary underline">
                  View Guidelines
                </a>
              </div>
            </div>
            <div className="border rounded-lg p-6 space-y-4">
              <h3 className="text-xl font-medium">Terms of Service</h3>
              <p>Review our terms of service agreement for using the Travella platform.</p>
              <div className="pt-2">
                <a href="/policies/terms" className="text-primary underline">
                  Read Terms
                </a>
              </div>
            </div>
            <div className="border rounded-lg p-6 space-y-4">
              <h3 className="text-xl font-medium">Privacy Policy</h3>
              <p>Understand how we collect, use, and protect your personal information.</p>
              <div className="pt-2">
                <a href="/policies/privacy" className="text-primary underline">
                  Read Policy
                </a>
              </div>
            </div>
          </div>
        </section>

        <section id="special-support" className="space-y-4">
          <h2 className="text-2xl font-semibold">Special Support Needs</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-medium mb-2">Accessibility Support</h3>
              <p>If you require accessibility accommodations or have questions about accessibility features:</p>
              <p className="mt-2 font-medium">Email: <EMAIL></p>
            </div>

            <div>
              <h3 className="text-xl font-medium mb-2">Business Accounts</h3>
              <p>For questions related to business accounts or corporate travel programs:</p>
              <p className="mt-2 font-medium">Email: <EMAIL></p>
              <p className="font-medium">Phone: +256 (780) 101-601</p>
            </div>

            <div>
              <h3 className="text-xl font-medium mb-2">Media Inquiries</h3>
              <p>For press and media related questions:</p>
              <p className="mt-2 font-medium">Email: <EMAIL></p>
            </div>
          </div>
        </section>

        <section id="feedback" className="space-y-4">
          <h2 className="text-2xl font-semibold">We Value Your Feedback</h2>
          <p>
            We're constantly working to improve our service. If you have suggestions, feedback, or ideas for how we can make Travella better, we'd love to hear from you.
          </p>
          <p className="font-medium">
            Email your feedback to: <EMAIL>
          </p>
        </section>
      </div>
    </div>
  );
} 