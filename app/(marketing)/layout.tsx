import type React from "react"
import { MarketingHeader } from "@/components/marketing/header-new"
import { MarketingFooter } from "@/components/marketing/footer-new"
import { createClient } from "@/lib/supabase-server"

export default async function MarketingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser()
  return (
    <div className="flex min-h-screen flex-col">
      <MarketingHeader user={user} />
      <main className="flex-1 animate-in">{children}</main>
      <MarketingFooter />
    </div>
  )
}

