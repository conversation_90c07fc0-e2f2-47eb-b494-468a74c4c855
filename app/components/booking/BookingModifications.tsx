import { Arrow<PERSON><PERSON>I<PERSON>, ClockIcon } from "lucide-react";
import { format } from "date-fns";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BookingModificationRequest, BookingResponse } from "@/types/bookings";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { ModificationDetailsModal } from "@/components/admin/bookings/details/modification-details-modal";

interface BookingModificationsProps {
  requests: BookingModificationRequest[];
  booking: BookingResponse;
}

export function BookingModifications({ requests: modifications, booking }: BookingModificationsProps) {
  const [selectedModification, setSelectedModification] = useState<BookingModificationRequest | null>(null);
  
  if (modifications.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Modifications</CardTitle>
          <CardDescription>No modifications have been made to this booking</CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-1 text-lg">
          <Badge variant="outline">{modifications.length}</Badge>
          Modifications requests
        </CardTitle>
        <CardDescription>Requested changes to this booking</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Accordion type="single" collapsible className="w-full">
          {modifications.map((mod) => (
            <AccordionItem key={mod.id} value={mod.id}>
              <AccordionTrigger className="px-6 py-3 hover:no-underline hover:bg-muted/50">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 text-left">
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <ClockIcon className="h-3 w-3" />
                    <span>{format(new Date(mod.created_at), 'MMM d, yyyy hh:mm a')}</span>
                  </div>
                  <Badge variant={mod.status === 'approved' ? 'outline' : mod.status === 'pending' ? 'outline' : 'destructive'} 
                    className="px-2 py-0 h-5">
                    {mod.status}
                  </Badge>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-6 py-3 border-t">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-sm">Requested by</h4>
                    <p className="text-sm">{mod.requester_details.first_name} {mod.requester_details.last_name}</p>
                  </div>
                  
                  <div className="flex flex-col gap-2">
                    <h4 className="font-medium text-sm">Reason</h4>
                    <div className="bg-muted p-2 rounded-md">
                      <p className="text-xs text-muted-foreground">{mod.requested_changes.message}</p>
                    </div>
                    <Button variant="outline" size="sm" className="w-fit text-xs" onClick={() => setSelectedModification(mod)}>
                      View details
                    </Button>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>

      {selectedModification && (
        <ModificationDetailsModal
          modification={selectedModification}
          booking={booking}
          open={!!selectedModification}
          onOpenChange={(value) => setSelectedModification(value ? selectedModification : null)}
        />
      )}
    </Card>
  );
} 