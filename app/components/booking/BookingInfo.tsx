import { BookingResponse } from "@/types/bookings";
import { differenceInDays, format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Copy, CalendarRange, Clock, Calendar, CarFront } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { BookingStatus } from "@/types/supabase";
import Image from "next/image";

interface BookingInfoProps {
  booking: BookingResponse;
}

const getStatusBadge = (status: BookingStatus) => {
  switch (status) {
    case "confirmed":
      return <Badge className="bg-green-500 text-white">Confirmed</Badge>;
    case "pending":
      return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>;
    case "cancelled":
      return <Badge variant="destructive">Cancelled</Badge>;
    case "completed":
      return <Badge className="bg-blue-500 text-white">Completed</Badge>;
    case "ongoing":
      return <Badge className="bg-orange-500 text-white">Ongoing</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
};

export function BookingInfo({ booking }: BookingInfoProps) {
  const { toast } = useToast()
  
  const handleCopyBookingId = () => {
    navigator.clipboard.writeText(booking.id);
    toast({
      title: "Booking ID copied",
      description: "The booking ID has been copied to the clipboard.",
      duration: 3000,
    });
  };

  const bookingDuration = booking.end_date ? differenceInDays(new Date(booking.end_date), new Date(booking.start_date)) : 1;

  return (
    <Card>
      <CardContent className="p-4 space-y-4">
        {/* Car details */}
          <CardTitle className="flex flex-row justify-between items-center text-lg">
            <div className="flex flex-col gap-2">
            <Image src={booking.car_images[0]} alt={booking.brand_name} width={100} height={100} className="rounded-md overflow-hidden" />
            {booking.host.last_name} {booking.host.first_name}'s {booking.brand_name} {booking.year}
            </div>
            <div className="flex flex-col gap-1 items-center gap-2">
              <Image src={booking.host.avatar} alt={booking.brand_name} width={100} height={100} className="rounded-md overflow-hidden" />
              <p className="text-sm text-muted-foreground">{booking.host.first_name} {booking.host.last_name}</p>
            </div>
          </CardTitle>
        
        {/* Booking details */}
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Booking</CardTitle>
        </div>
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-muted-foreground">Booking ID</p>
            <div className="flex items-center gap-2 mt-1">
              <p className="font-mono text-sm">{booking.id}</p>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6" 
                onClick={handleCopyBookingId}
                title="Copy Booking ID"
              >
                <Copy className="h-3.5 w-3.5 text-muted-foreground" />
              </Button>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-muted-foreground">Created</p>
            <p>{booking.created_at ? format(new Date(booking.created_at), "MMM d, yyyy 'at' h:mm a") : "N/A"}</p>
          </div>
        </div>

        <Separator />

        <div className="grid grid-cols-2 gap-6">
        <div>
          <p className="text-sm text-muted-foreground">Pickup Date</p>
          <p className="font-medium">{booking.start_date ? format(new Date(booking.start_date), "MMMM d, yyyy") : "N/A"}</p>
          <div className="flex items-center gap-1 mt-1">
            <Clock className="h-3.5 w-3.5 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">{booking.pickup_time || "Any time"}</p>
          </div>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Return Date</p>
          <p className="font-medium">{booking.end_date ? format(new Date(booking.end_date), "MMMM d, yyyy") : "N/A"}</p>
          <div className="flex items-center gap-1 mt-1">
            <Clock className="h-3.5 w-3.5 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">{booking.dropoff_time || "Any time"}</p>
          </div>
          </div>
        </div>

        <div className="p-3 bg-muted/50 rounded-md mt-2">
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <p className="text-sm font-medium">
            {bookingDuration} {bookingDuration === 1 ? 'day' : 'days'} total
          </p>
        </div>

        {/* Charge */}
        <div className="flex flex-row justify-between items-center gap-2">
          <div className="text-sm font-medium">
            {Intl.NumberFormat("en-US", {
              style: "currency",
              currency: booking.currency_details?.code || "UGX"
            }).format(booking.daily_charge)} / day
          </div>

          <div className="text-sm font-medium">
            {Intl.NumberFormat("en-US", {
              style: "currency",
              currency: booking.currency_details?.code || "UGX"
            }).format(booking.total_amount || 0)}
          </div>
        </div>
      </div>
      
      {booking.pick_up_location && (
        <div>
          <p className="text-sm text-muted-foreground">Pickup/Dropoff Location</p>
          <p>{booking.pick_up_location.place_name}</p>
        </div>
      )}

        {booking.cancellation_reason && (
          <div>
            <p className="text-sm text-muted-foreground">Cancellation Reason</p>
            <p className="p-2 bg-muted rounded-md mt-1 text-sm">
              {booking.cancellation_reason}
            </p>
          </div>
        )}

        {booking.pickup_instructions && (
          <div>
            <p className="text-sm text-muted-foreground">Pickup Instructions</p>
            <p className="p-2 bg-muted rounded-md mt-1 text-sm">
              {booking.pickup_instructions}
            </p>
          </div>
        )}

        {booking.return_instructions && (
          <div>
            <p className="text-sm text-muted-foreground">Return Instructions</p>
            <p className="p-2 bg-muted rounded-md mt-1 text-sm">
              {booking.return_instructions}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 