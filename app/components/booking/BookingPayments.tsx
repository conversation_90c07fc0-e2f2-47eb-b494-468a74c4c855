import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button-new";
import { <PERSON>, CardHeader, CardTitle, CardContent, CardFooter } from "@/components/ui/card";
import { Table, TableHeader, TableBody, TableCell, TableRow, TableHead } from "@/components/ui/table";
import { BookingPayment } from "@/types/bookings";
import { ReceiptIcon } from "lucide-react";
import { format } from "date-fns";
interface BookingPaymentsProps {
    payments: BookingPayment[]
}

export const BookingPayments: React.FC<BookingPaymentsProps> = ({ payments }) => {
    return (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Payment Transactions</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>{format(payment.created_at, "MMM d, yyyy")}</TableCell>
                    <TableCell className="capitalize">
                      {payment.payment_type.replace(/_/g, " ")}
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: payment.currency || "USD"
                      }).format(payment.amount)}
                    </TableCell>
                    <TableCell>
                      {payment.payment_status === "completed" ? (
                        <Badge className="bg-green-500">Completed</Badge>
                      ) : payment.payment_status === "pending" ? (
                        <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>
                      ) : (
                        <Badge variant="outline">{payment.payment_status}</Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
          {/* <CardFooter className="flex justify-between items-center p-4 pt-2">
            <div>
              <span className="text-sm text-muted-foreground">Payment Method:</span>
              <span className="text-sm ml-2">Visa •••• 4242</span>
            </div>
            <Button variant="outline" size="sm">
              <ReceiptIcon className="mr-2 h-4 w-4" />
              Download Receipt
            </Button>
          </CardFooter> */}
        </Card>
      );
};
