import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>Left, Link } from "lucide-react";

export const LoadingBookingDetails = () => {
    return (
        <div className="container py-10">
          <div className="w-full max-w-7xl mx-auto">
            <div className="flex items-center mb-6">
              <Button variant="ghost" size="sm" className="gap-2" asChild>
                <Link href="/admin/bookings">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Bookings
                </Link>
              </Button>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[200px] w-full" />
              <div className="grid gap-6 md:grid-cols-2">
                <Skeleton className="h-[300px]" />
                <Skeleton className="h-[300px]" />
              </div>
            </div>
          </div>
        </div>
      )
}