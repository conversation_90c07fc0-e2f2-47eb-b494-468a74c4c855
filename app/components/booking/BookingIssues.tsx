import { AlertCircleIcon, CheckCircleIcon, ClockIcon, MessageCircleIcon } from "lucide-react";
import { format } from "date-fns";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { BookingIssue, BookingResponse } from "@/types/bookings";

interface BookingIssuesProps {
  booking: BookingResponse;
  issues?: BookingIssue[];
}

// We'll use these types for any additional UI features
type IssuePriority = 'low' | 'medium' | 'high' | 'critical';

// Map API issue types to display-friendly titles
const getIssueTitle = (issue: BookingIssue) => {
  switch (issue.issue_type) {
    case "Car Damage":
      return "Damage reported on vehicle";
    case "Mechanical Issue":
      return "Mechanical problem with vehicle";
    case "Cleanliness":
      return "Cleanliness issue reported";
    case "Late Return":
      return "Vehicle returned late";
    case "Location Issue":
      return "Problem with pickup/return location";
    case "Other":
    default:
      return "Issue reported";
  }
};

// Determine priority based on issue type (can be adjusted based on business logic)
const getIssuePriority = (issue: BookingIssue): IssuePriority => {
  switch (issue.issue_type) {
    case "Car Damage":
      return "high";
    case "Mechanical Issue":
      return "high";
    case "Cleanliness":
      return "low";
    case "Late Return":
      return "medium";
    case "Location Issue":
      return "medium";
    case "Other":
    default:
      return "medium";
  }
};

export function BookingIssues({ booking, issues = [] }: BookingIssuesProps) {
  const getPriorityBadge = (priority: IssuePriority) => {
    switch (priority) {
      case 'low':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Low</Badge>;
      case 'medium':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Medium</Badge>;
      case 'high':
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">High</Badge>;
      case 'critical':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Critical</Badge>;
    }
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="border-red-300 text-red-600">Pending</Badge>;
      case 'reviewing':
        return <Badge className="bg-blue-500">In Progress</Badge>;
      case 'resolved':
        return <Badge className="bg-green-500">Resolved</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  if (issues.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Issues & Incidents</CardTitle>
          <CardDescription>No issues have been reported for this booking</CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Issues & Incidents</CardTitle>
        <CardDescription>
          {issues.length} {issues.length === 1 ? 'issue' : 'issues'} reported
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Accordion type="single" collapsible className="w-full">
          {issues.map((issue) => (
            <AccordionItem key={issue.id} value={issue.id}>
              <AccordionTrigger className="px-6 py-3 hover:no-underline hover:bg-muted/50">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between w-full gap-2 text-left">
                  <div className="flex items-center gap-2">
                    <AlertCircleIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium text-sm">{getIssueTitle(issue)}</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {getPriorityBadge(getIssuePriority(issue))}
                    {getStatusBadge(issue.status)}
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-6 py-4 border-t space-y-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-start flex-wrap gap-2">
                    <div>
                      <h4 className="font-medium text-sm">Issue Type</h4>
                      <p className="text-sm">{issue.issue_type}</p>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <ClockIcon className="h-3 w-3" />
                      <span>Reported on {format(new Date(issue.created_at), 'MMM d, yyyy')}</span>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-sm">Description</h4>
                    <p className="text-sm">{issue.description}</p>
                  </div>
                </div>
                
                {/* Placeholder for comments - will be implemented in the future */}
                <div className="space-y-3">
                  <h4 className="font-medium text-sm flex items-center gap-1">
                    <MessageCircleIcon className="h-4 w-4" />
                    Comments
                  </h4>
                  <div className="border rounded-md p-3 text-sm text-muted-foreground">
                    Comments will be available soon
                  </div>
                </div>
                
                {/* Add comment form - only shown for unresolved issues */}
                {issue.status !== 'resolved' && (
                  <div className="space-y-3">
                    <h4 className="font-medium text-sm">Add a comment</h4>
                    <Textarea placeholder="Type your comment here..." className="min-h-[80px]" />
                    <div className="flex justify-between">
                      <div className="flex gap-2">
                        {issue.status === 'pending' && (
                          <Button variant="outline" size="sm">
                            Mark as In Progress
                          </Button>
                        )}
                        {(issue.status === 'pending' || issue.status === 'reviewing') && (
                          <Button variant="outline" size="sm" className="flex items-center gap-1">
                            <CheckCircleIcon className="h-3 w-3" />
                            Resolve
                          </Button>
                        )}
                      </div>
                      <Button size="sm">Send</Button>
                    </div>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
      <CardFooter className="flex justify-end p-4 pt-2">
        <Button>
          Report New Issue
        </Button>
      </CardFooter>
    </Card>
  );
} 