import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, Link } from "lucide-react";

export const BookingNotFound = () => {
    return (
        <div className="container py-10">
          <div className="w-full max-w-7xl mx-auto">
            <div className="flex items-center mb-6">
              <Button variant="ghost" size="sm" className="gap-2" asChild>
                <Link href="/admin/bookings">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Bookings
                </Link>
              </Button>
            </div>
            <div className="p-8 text-center">
              <h2 className="text-2xl font-bold mb-2">Error Loading Booking</h2>
              <p className="text-muted-foreground mb-6">
                There was an error loading the booking details. Please try again or contact support.
              </p>
              <Button asChild>
                <Link href="/admin/bookings">Return to Bookings</Link>
              </Button>
            </div>
          </div>
        </div>
      )
};