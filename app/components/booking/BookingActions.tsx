import { useState } from "react";
import { useRouter } from "next/navigation";
import { updateBookingStatus } from "@/app/actions/admin/admin-actions";
import { BookingResponse } from "@/types/bookings";
import { Button } from "@/components/ui/button";
import { 
  Calendar, 
  CheckCircle, 
  Clock, 
  Copy, 
  FileEdit, 
  MoreHorizontal, 
  Printer, 
  XCircle 
} from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

interface BookingActionsProps {
  booking: BookingResponse;
}

export default function BookingActions({ booking }: BookingActionsProps) {
  const router = useRouter();
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isCompleteDialogOpen, setIsCompleteDialogOpen] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [cancellationReason, setCancellationReason] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast()
  const handleCopyBookingId = () => {
    navigator.clipboard.writeText(booking.id);
    toast({
      title: "Booking ID copied",
      description: "The booking ID has been copied to the clipboard.",
      duration: 3000,
    });
  };
  
  const handlePrintBooking = () => {
    window.print();
  };

  const handleUpdateStatus = async (status: "confirmed" | "cancelled" | "completed") => {
    try {
      setIsUpdating(true);
      await updateBookingStatus(
        booking.id, 
        status, 
        status === "cancelled" ? cancellationReason : undefined
      );
      
      toast({
        title: "Booking updated",
        description: `Booking has been ${status} successfully.`,
        variant: "default",
      });
      
      // Close any open dialogs
      setIsConfirmDialogOpen(false);
      setIsCompleteDialogOpen(false);
      setIsCancelDialogOpen(false);
      setCancellationReason("");
      
      // Refresh the page to show updated data
      router.refresh();
    } catch (error) {
      toast({
        title: "Error updating booking",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };
  
  // Show different actions based on the current booking status
  const renderStatusActions = () => {
    switch (booking.status) {
      case "pending":
        return (
          <>
            <Button 
              variant="default" 
              className="gap-2" 
              onClick={() => setIsConfirmDialogOpen(true)}
            >
              <CheckCircle className="h-4 w-4" />
              Confirm Booking
            </Button>
            <Button 
              variant="outline" 
              className="gap-2 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600" 
              onClick={() => setIsCancelDialogOpen(true)}
            >
              <XCircle className="h-4 w-4" />
              Cancel Booking
            </Button>
          </>
        );
      case "confirmed":
        return (
          <>
            <Button 
              variant="default" 
              className="gap-2" 
              onClick={() => setIsCompleteDialogOpen(true)}
            >
              <CheckCircle className="h-4 w-4" />
              Mark as Completed
            </Button>
            <Button 
              variant="outline" 
              className="gap-2 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600" 
              onClick={() => setIsCancelDialogOpen(true)}
            >
              <XCircle className="h-4 w-4" />
              Cancel Booking
            </Button>
          </>
        );
      case "ongoing":
        return (
          <Button 
            variant="default" 
            className="gap-2" 
            onClick={() => setIsCompleteDialogOpen(true)}
          >
            <CheckCircle className="h-4 w-4" />
            Complete Booking
          </Button>
        );
      default:
        return (
          <Button
            variant="outline"
            className="gap-2"
            onClick={handlePrintBooking}
          >
            <Printer className="h-4 w-4" />
            Print Booking
          </Button>
        );
    }
  };
  
  return (
    <>
      <div className="flex items-center gap-2">
        {renderStatusActions()}
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={handleCopyBookingId} className="gap-2">
              <Copy className="h-4 w-4" />
              Copy Booking ID
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handlePrintBooking} className="gap-2">
              <Printer className="h-4 w-4" />
              Print Booking
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2">
              <Calendar className="h-4 w-4" />
              Add to Calendar
            </DropdownMenuItem>
            <DropdownMenuItem className="gap-2">
              <FileEdit className="h-4 w-4" />
              Edit Booking
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* Confirm Booking Dialog */}
      <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Booking</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to confirm this booking? This will notify the renter and car owner.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUpdating}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={isUpdating}
              onClick={(e) => {
                e.preventDefault();
                handleUpdateStatus("confirmed");
              }}
            >
              {isUpdating ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Confirm Booking"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Complete Booking Dialog */}
      <AlertDialog open={isCompleteDialogOpen} onOpenChange={setIsCompleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Complete Booking</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to mark this booking as completed? This will finalize the booking process.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUpdating}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={isUpdating}
              onClick={(e) => {
                e.preventDefault();
                handleUpdateStatus("completed");
              }}
            >
              {isUpdating ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Complete Booking"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Cancel Booking Dialog */}
      <AlertDialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Booking</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel this booking? This will notify the renter and car owner.
              Please provide a reason for the cancellation.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Reason for cancellation"
              value={cancellationReason}
              onChange={(e) => setCancellationReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUpdating}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={isUpdating || !cancellationReason.trim()}
              onClick={(e) => {
                e.preventDefault();
                handleUpdateStatus("cancelled");
              }}
              className="bg-red-500 hover:bg-red-600"
            >
              {isUpdating ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Cancel Booking"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 