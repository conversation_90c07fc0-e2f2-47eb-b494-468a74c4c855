import { UserProfile } from "@/types/users";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ExternalLink, Mail, Phone, UserCircle } from "lucide-react";
import { format, parseISO } from "date-fns";

interface RenterInfoProps {
  renter: UserProfile;
}

export function RenterInfo({ renter }: RenterInfoProps) {

  const fullName = `${renter.first_name || ""} ${renter.last_name || ""}`.trim() || "Unknown";
  const initials = fullName !== "Unknown" 
    ? `${renter.first_name?.[0] || ""}${renter.last_name?.[0] || ""}`.toUpperCase()
    : "?";

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Renter Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <Avatar className="h-14 w-14 border-2 border-primary/10">
            <AvatarImage src={renter.avatar_url} alt={fullName} />
            <AvatarFallback className="text-lg font-medium">{initials}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium text-lg">{fullName}</p>
            {renter.email && (
              <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
                <Mail className="h-3.5 w-3.5" />
                <span>{renter.email}</span>
              </div>
            )}
            {renter.phone_number && (
              <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
                <Phone className="h-3.5 w-3.5" />
                <span>{renter.phone_number}</span>
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 pt-3 border-t">
          <div>
            <p className="text-sm text-muted-foreground">User ID</p>
            <p className="font-mono text-xs truncate">{renter.id}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Joined</p>
            <p>{renter.created_at ? format(parseISO(renter.created_at), "MMM d, yyyy") : 
               "Unknown"}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">Role</p>
            <Badge variant="outline" className="mt-1">
              {renter.role || "Renter"}
            </Badge>
          </div>
        </div>

        <div className="pt-3">
          <Button variant="outline" className="w-full" asChild>
            <a href={`/admin/users/${renter.id}`} target="_blank" rel="noopener noreferrer">
              <UserCircle className="mr-2 h-4 w-4" />
              View Full Profile
              <ExternalLink className="ml-2 h-3.5 w-3.5" />
            </a>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 