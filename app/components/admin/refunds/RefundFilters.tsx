import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { RefundStatus } from '@/types/refunds';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface RefundFiltersProps {
  statusFilter: RefundStatus | undefined;
  setStatusFilter: (status: RefundStatus | undefined) => void;
  userNameFilter: string;
  setUserNameFilter: (userName: string) => void;
  onSearch: () => void;
}

const RefundFilters: React.FC<RefundFiltersProps> = ({
  statusFilter,
  setStatusFilter,
  userNameFilter,
  setUserNameFilter,
  onSearch,
}) => {
  // Handle user name input changes
  const handleUserNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUserNameFilter(e.target.value);
  };

  // Handle Enter key for search
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch();
    }
  };

  // Handle status filter change
  const handleStatusChange = (value: string) => {
    if (value === 'all') {
      setStatusFilter(undefined);
    } else {
      setStatusFilter(value as RefundStatus);
    }
  };

  return (
    <div className="space-y-4 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            value={statusFilter || 'all'}
            onValueChange={handleStatusChange}
          >
            <SelectTrigger id="status">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="userName">Customer Name</Label>
          <div className="flex space-x-2">
            <div className="relative flex-grow">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="userName"
                type="text"
                placeholder="Search by customer name"
                value={userNameFilter}
                onChange={handleUserNameChange}
                onKeyDown={handleKeyDown}
                className="pl-8"
              />
            </div>
            <Button onClick={onSearch} type="button">
              Search
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RefundFilters; 