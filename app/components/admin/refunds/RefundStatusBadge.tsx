import React from 'react';
import { Badge } from '@/components/ui/badge';
import { RefundStatus } from '@/types/refunds';

interface RefundStatusBadgeProps {
  status: RefundStatus;
}

const RefundStatusBadge: React.FC<RefundStatusBadgeProps> = ({ status }) => {
  const getStatusConfig = (status: RefundStatus) => {
    switch (status) {
      case 'pending':
        return { label: 'Pending', variant: 'secondary' as const };
      case 'processing':
        return { label: 'Processing', variant: 'warning' as const };
      case 'completed':
        return { label: 'Completed', variant: 'success' as const };
      case 'failed':
        return { label: 'Failed', variant: 'destructive' as const };
      default:
        return { label: status, variant: 'outline' as const };
    }
  };

  const { label, variant } = getStatusConfig(status);

  return (
    <Badge variant={variant}>
      {label}
    </Badge>
  );
};

export default RefundStatusBadge; 