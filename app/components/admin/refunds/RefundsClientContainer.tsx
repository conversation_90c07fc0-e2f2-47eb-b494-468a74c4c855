"use client";

import React, { useState, useTransition, useEffect } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { BookingRefundRequest, RefundStatus } from '@/types/refunds';
import RefundsTable from './RefundsTable';
import RefundFilters from './RefundFilters';
import RefundPagination from './RefundPagination';

interface RefundsClientContainerProps {
  initialRefunds: BookingRefundRequest[];
  totalItems: number;
  currentPage: number;
  itemsPerPage: number;
  statusFilter: RefundStatus | undefined;
  userNameFilter: string;
  hasMore: boolean;
  totalPages: number;
}

const RefundsClientContainer: React.FC<RefundsClientContainerProps> = ({
  initialRefunds,
  totalItems,
  currentPage,
  itemsPerPage,
  statusFilter,
  userNameFilter,
  hasMore,
  totalPages,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  
  // Local states for UI interactions before navigation
  const [localStatusFilter, setLocalStatusFilter] = useState<RefundStatus | undefined>(statusFilter);
  const [localUserNameFilter, setLocalUserNameFilter] = useState<string>(userNameFilter);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Update local state when props change (after navigation completes)
  useEffect(() => {
    setLocalStatusFilter(statusFilter);
    setLocalUserNameFilter(userNameFilter);
    setIsLoading(false);
  }, [initialRefunds, statusFilter, userNameFilter]);

  // Create a new URL with updated search params
  const createNewUrl = (params: Record<string, string | number | undefined>) => {
    const newParams = new URLSearchParams(searchParams.toString());
    
    // Update the parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value === undefined || value === '') {
        newParams.delete(key);
      } else {
        newParams.set(key, String(value));
      }
    });
    
    return `${pathname}?${newParams.toString()}`;
  };

  // Update URL and trigger server-side rendering with new parameters
  const updateURLParams = (params: Record<string, string | number | undefined>) => {
    setIsLoading(true);
    
    const newUrl = createNewUrl(params);
    
    // Use Next.js's router push with startTransition to navigate without blocking UI
    startTransition(() => {
      router.push(newUrl);
    });
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    updateURLParams({ page });
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    updateURLParams({ items_per_page: newItemsPerPage, page: 1 });
  };

  // Handle search button click - this applies filters immediately
  const handleSearch = () => {
    // Reset to first page and apply filters
    updateURLParams({
      status: localStatusFilter,
      user_name: localUserNameFilter,
      page: 1
    });
  };

  // Handle status filter change to apply immediately
  const handleStatusChange = (status: RefundStatus | undefined) => {
    setLocalStatusFilter(status);
    // Automatically apply the status filter when changed
    updateURLParams({
      status,
      user_name: localUserNameFilter,
      page: 1
    });
  };

  // Track if we're in a loading state
  const showLoadingState = isLoading || isPending;

  return (
    <>
      {/* Filters */}
      <RefundFilters 
        statusFilter={localStatusFilter}
        setStatusFilter={handleStatusChange}
        userNameFilter={localUserNameFilter}
        setUserNameFilter={setLocalUserNameFilter}
        onSearch={handleSearch}
      />

      {/* Refunds Table */}
      <RefundsTable 
        refunds={initialRefunds} 
        isLoading={showLoadingState}
      />

      {/* Pagination */}
      <RefundPagination 
        currentPage={currentPage}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
      />
    </>
  );
};

export default RefundsClientContainer; 