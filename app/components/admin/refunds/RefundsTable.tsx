import React, { useState } from 'react';
import { BookingRefundRequest, RefundStatus } from '@/types/refunds';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MoreHorizontal, Eye, Check, AlertTriangle, RotateCcw, Loader2 } from 'lucide-react';
import RefundStatusBadge from './RefundStatusBadge';
import RefundDetailsModal from './RefundDetailsModal';
import { formatCurrency } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { updateRefundStatus } from '@/app/actions/admin/refunds';
import { useToast } from '@/hooks/use-toast';

interface RefundsTableProps {
  refunds: BookingRefundRequest[];
  isLoading: boolean;
}

const statusIcons = {
  pending: <AlertTriangle className="h-4 w-4 mr-2" />,
  processing: <RotateCcw className="h-4 w-4 mr-2" />,
  completed: <Check className="h-4 w-4 mr-2" />,
  failed: <AlertTriangle className="h-4 w-4 mr-2" />
};

const RefundsTable: React.FC<RefundsTableProps> = ({ refunds, isLoading }) => {
  const [selectedRefund, setSelectedRefund] = useState<BookingRefundRequest | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [updatingRefundId, setUpdatingRefundId] = useState<string | null>(null);
  const { toast } = useToast()

  const handleViewDetails = (refund: BookingRefundRequest) => {
    setSelectedRefund(refund);
    setIsModalOpen(true);
  };

  const handleUpdateStatus = async (refundId: string, newStatus: RefundStatus) => {
    if (updatingRefundId) return; // Prevent multiple simultaneous updates
    
    setUpdatingRefundId(refundId);
    try {
      const result = await updateRefundStatus({
        refund_id: refundId,
        status: newStatus,
      });

      if (result?.success) {
        toast({
          title: result.message || 'Status updated successfully',
          description: 'The refund status has been updated successfully',
          duration: 3000,
        });
        // The server action will handle revalidation
      } else {
        toast({
          title: result?.message || 'Failed to update status',
          description: 'An error occurred while updating the refund status',
        });
      }
    } catch (error) {
      console.error('Error updating refund status:', error);
      toast({
        title: 'An error occurred while updating status',
        description: (error as Error).message || 'An error occurred while updating the refund status',
      });
    } finally {
      setUpdatingRefundId(null);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd MMM yyyy, HH:mm');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Extract initial from the user's name
  const getInitials = (name: string) => {
    if (!name) return '?';
    return name.split(' ').map(part => part[0]).join('').toUpperCase();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (refunds.length === 0) {
    return (
      <div className="flex items-center justify-center h-40">
        <p className="text-muted-foreground">No refund requests found</p>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Request ID</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Vehicle</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {refunds.map((refund) => (
              <TableRow key={refund.id}>
                <TableCell className="font-medium">#{refund.id.substring(0, 8)}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={refund.user_details.avatar_url} />
                      <AvatarFallback>{getInitials(refund.user_details.full_name)}</AvatarFallback>
                    </Avatar>
                    <span>{refund.user_details.full_name}</span>
                  </div>
                </TableCell>
                <TableCell>{refund.brand_name} {refund.model}</TableCell>
                <TableCell>
                  <RefundStatusBadge status={refund.status} />
                </TableCell>
                <TableCell>{refund.currency} {formatCurrency(refund.amount, refund.currency)}</TableCell>
                <TableCell>{formatDate(refund.created_at)}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end space-x-2">
                    <Button variant="ghost" size="icon" onClick={() => handleViewDetails(refund)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="icon"
                          disabled={updatingRefundId === refund.id}
                        >
                          {updatingRefundId === refund.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <MoreHorizontal className="h-4 w-4" />
                          )}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          className={refund.status === 'pending' ? 'text-muted-foreground cursor-not-allowed' : ''}
                          disabled={refund.status === 'pending'}
                          onClick={() => refund.status !== 'pending' && handleUpdateStatus(refund.id, 'pending')}
                        >
                          {statusIcons.pending}
                          Set to Pending
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className={refund.status === 'processing' ? 'text-muted-foreground cursor-not-allowed' : ''}
                          disabled={refund.status === 'processing'}
                          onClick={() => refund.status !== 'processing' && handleUpdateStatus(refund.id, 'processing')}
                        >
                          {statusIcons.processing}
                          Set to Processing
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className={refund.status === 'completed' ? 'text-muted-foreground cursor-not-allowed' : ''}
                          disabled={refund.status === 'completed'}
                          onClick={() => refund.status !== 'completed' && handleUpdateStatus(refund.id, 'completed')}
                        >
                          {statusIcons.completed}
                          Set to Completed
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className={refund.status === 'failed' ? 'text-muted-foreground cursor-not-allowed' : ''}
                          disabled={refund.status === 'failed'}
                          onClick={() => refund.status !== 'failed' && handleUpdateStatus(refund.id, 'failed')}
                        >
                          {statusIcons.failed}
                          Set to Failed
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <RefundDetailsModal 
        refund={selectedRefund} 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </>
  );
};

export default RefundsTable; 