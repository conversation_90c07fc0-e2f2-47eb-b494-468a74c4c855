import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { BookingRefundRequest } from '@/types/refunds';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import RefundStatusBadge from './RefundStatusBadge';
import { format } from 'date-fns';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { formatCurrency } from '@/lib/utils';
import { AlertTriangle, Check, RotateCcw, Clock } from 'lucide-react';

interface RefundDetailsModalProps {
  refund: BookingRefundRequest | null;
  isOpen: boolean;
  onClose: () => void;
}

const statusIcons = {
  pending: <Clock className="h-5 w-5 text-amber-500" />,
  processing: <RotateCcw className="h-5 w-5 text-blue-500" />,
  completed: <Check className="h-5 w-5 text-green-500" />,
  failed: <AlertTriangle className="h-5 w-5 text-red-500" />
};

const statusDescriptions = {
  pending: "Refund request is awaiting processing by an administrator.",
  processing: "Refund is currently being processed through the payment system.",
  completed: "Refund has been successfully processed and funds have been returned to the customer.",
  failed: "Refund processing failed. Please check transaction details and try again."
};

const RefundDetailsModal: React.FC<RefundDetailsModalProps> = ({
  refund,
  isOpen,
  onClose,
}) => {
  if (!refund) return null;

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd MMM yyyy, HH:mm');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Extract initial from the user's name
  const getInitials = (name: string) => {
    if (!name) return '?';
    return name.split(' ').map(part => part[0]).join('').toUpperCase();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Refund Request Details</DialogTitle>
          <DialogDescription>
            Complete information about this refund request
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Status Information */}
          <Card className="border-l-4" style={{ borderLeftColor: getStatusColor(refund.status) }}>
            <CardContent className="pt-6">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  {statusIcons[refund.status]}
                </div>
                <div>
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    Status: <RefundStatusBadge status={refund.status} />
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {statusDescriptions[refund.status]}
                  </p>
                  
                  {refund.processed_at && (
                    <p className="text-xs mt-2">
                      {refund.status === 'completed' ? 'Processed on: ' : 'Last updated: '}
                      {formatDate(refund.processed_at)}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* User Information */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">User Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <Avatar>
                  <AvatarImage src={refund.user_details.avatar_url} />
                  <AvatarFallback>{getInitials(refund.user_details.full_name)}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{refund.user_details.full_name}</p>
                  <p className="text-sm text-muted-foreground">{refund.user_details.email}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Vehicle & Booking Information */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Vehicle & Booking Details</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Vehicle</p>
                  <p>{refund.brand_name} {refund.model}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Booking Duration</p>
                  <p>{formatDate(refund.start_date)} - {formatDate(refund.end_date)}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <p className="text-sm text-muted-foreground">Cancelled On</p>
                  <p>{formatDate(refund.cancelled_at)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Booking ID</p>
                  <p className="font-mono">{refund.booking_id}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Refund Information */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Refund Information</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Refund Amount</p>
                  <p className="text-lg font-bold">{refund.currency} {formatCurrency(refund.amount, refund.currency)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Refund Percentage</p>
                  <p>{refund.percentage}%</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <p className="text-sm text-muted-foreground">Payment Method</p>
                  <p>{refund.payment_method || 'Not specified'}</p>
                </div>
                {refund.transaction_id && (
                  <div>
                    <p className="text-sm text-muted-foreground">Transaction ID</p>
                    <p className="font-mono text-xs">{refund.transaction_id}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Notes & Additional Information */}
          {(refund.notes || refund.meta_data) && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Additional Information</CardTitle>
              </CardHeader>
              <CardContent>
                {refund.notes && (
                  <div className="mb-4">
                    <p className="text-sm text-muted-foreground mb-1">Notes</p>
                    <p className="text-sm">{refund.notes}</p>
                  </div>
                )}
                {refund.meta_data && Object.keys(refund.meta_data).length > 0 && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Payment Details</p>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      {Object.entries(refund.meta_data).map(([key, value]) => (
                        value && (
                          <div key={key}>
                            <span className="text-muted-foreground capitalize">{key.replace(/_/g, ' ')}: </span>
                            <span>{value}</span>
                          </div>
                        )
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Timestamps */}
          <div className="text-xs text-muted-foreground grid grid-cols-2 gap-4">
            <div>Created: {formatDate(refund.created_at)}</div>
            {refund.updated_at && refund.updated_at !== refund.created_at && (
              <div>Last Updated: {formatDate(refund.updated_at)}</div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper function to get status color
function getStatusColor(status: string): string {
  switch (status) {
    case 'pending':
      return '#f59e0b'; // amber-500
    case 'processing':
      return '#3b82f6'; // blue-500
    case 'completed':
      return '#10b981'; // green-500
    case 'failed':
      return '#ef4444'; // red-500
    default:
      return '#6b7280'; // gray-500
  }
}

export default RefundDetailsModal; 