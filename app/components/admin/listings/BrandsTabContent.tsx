import React, { useState, useEffect } from "react";
import { CarBrand } from "@/types/listings";
import { TableHead, TableRow, TableCell } from "@/components/ui/table";
import TableWrapper from "./TableWrapper";
import AddBrandDialog from "./dialogs/AddBrandDialog";
import EditBrandDialog from "./dialogs/EditBrandDialog";

interface BrandsTabContentProps {
  brands: CarBrand[];
  loading: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  hasMore: boolean;
  loadMoreItems: () => void;
  filterItems: (items: CarBrand[], term: string) => CarBrand[];
}

const BrandsTabContent: React.FC<BrandsTabContentProps> = ({
  brands: initialBrands,
  loading,
  searchTerm,
  setSearchTerm,
  hasMore,
  loadMoreItems,
  filterItems
}) => {
  // Local state to manage brands
  const [brands, setBrands] = useState<CarBrand[]>(initialBrands);

  // Update local state when props change
  useEffect(() => {
    setBrands(initialBrands);
  }, [initialBrands]);

  // Function to handle brand updates
  const handleBrandUpdate = (updatedBrand: CarBrand) => {
    setBrands(prevBrands =>
      prevBrands.map(brand => 
        brand.id === updatedBrand.id ? updatedBrand : brand
      )
    );
  };

  // Function to handle brand additions
  const handleBrandAdd = (newBrand: CarBrand) => {
    setBrands(prevBrands => [...prevBrands, newBrand]);
  };

  // Empty refreshData function for compatibility
  const refreshData = () => {
    // No longer used, but kept for compatibility
  };

  const renderHeader = () => (
    <TableRow>
      <TableHead>Brand Name</TableHead>
      <TableHead className="w-[80px] text-right">Actions</TableHead>
    </TableRow>
  );

  const renderRow = (brand: CarBrand) => (
    <TableRow key={brand.id}>
      <TableCell>{brand.name}</TableCell>
      <TableCell className="flex items-center justify-end gap-2">
        <EditBrandDialog 
          brand={brand} 
          refreshData={refreshData} 
          onSuccessUpdate={handleBrandUpdate} 
        />
      </TableCell>
    </TableRow>
  );

  return (
    <TableWrapper
      title="Car Brands"
      items={brands}
      loading={loading}
      searchTerm={searchTerm}
      setSearchTerm={setSearchTerm}
      hasMore={hasMore && !searchTerm.trim()} // Only show "load more" if not searching
      loadMoreItems={loadMoreItems}
      addButton={
        <AddBrandDialog 
          refreshData={refreshData} 
          onSuccessAdd={handleBrandAdd} 
        />
      }
      renderHeader={renderHeader}
      renderRow={renderRow}
      filterItems={filterItems}
    />
  );
};

export default BrandsTabContent; 