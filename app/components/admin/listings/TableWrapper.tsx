import React from "react";
import { Loader2, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";

interface TableWrapperProps<T> {
  title: string;
  items: T[];
  loading: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  hasMore: boolean;
  loadMoreItems: () => void;
  addButton: React.ReactNode;
  renderHeader: () => React.ReactNode;
  renderRow: (item: T) => React.ReactNode;
  filterItems: (items: T[], term: string) => T[];
}

export function TableWrapper<T extends { id: string }>({
  title,
  items,
  loading,
  searchTerm,
  setSearchTerm,
  hasMore,
  loadMoreItems,
  addButton,
  renderHeader,
  renderRow,
  filterItems,
}: TableWrapperProps<T>) {
  // Apply the filter function
  const filteredItems = filterItems(items, searchTerm);
  const hasFilteredItems = filteredItems.length > 0;
  const isSearching = searchTerm.trim().length > 0;

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">{title}</h2>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={`Search ${title.toLowerCase()}...`}
              className="pl-8 w-[250px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {addButton}
        </div>
      </div>

      {loading && items.length === 0 ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="flex flex-col flex-grow overflow-auto min-h-[400px]">
          <div className="flex-grow overflow-auto">
            <Table>
              <TableHeader>
                {renderHeader()}
              </TableHeader>
              <TableBody>
                {hasFilteredItems ? (
                  filteredItems.map(renderRow)
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="h-40 text-center">
                      {isSearching ? (
                        <div className="text-muted-foreground">
                          No {title.toLowerCase()} matching "{searchTerm}"
                        </div>
                      ) : (
                        <div className="text-muted-foreground">
                          No {title.toLowerCase()} found. Add a new one to get started.
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          
          {hasMore && !isSearching && (
            <div className="mt-4 flex justify-center">
              <Button 
                onClick={loadMoreItems}
                disabled={loading}
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Load More
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default TableWrapper; 