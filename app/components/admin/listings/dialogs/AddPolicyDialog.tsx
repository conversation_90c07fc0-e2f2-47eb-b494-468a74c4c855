import React, { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Plus, Loader2 } from 'lucide-react';
import { createCancellationPolicy } from '@/app/actions/admin/listings';
import { CancellationPolicy, CreateCancellationPolicyInput } from '@/types/listings';
import { toast } from 'sonner';

// Define the form schema
const formSchema = z.object({
  name: z.string().min(2, { message: 'Policy name must be at least 2 characters.' }),
  code: z.string().min(1, { message: 'Policy code is required.' }),
  description: z.string().optional(),
  full_refund_hours: z.coerce.number().min(0, { message: 'Hours must be a positive number.' }),
  partial_refund_hours: z.coerce.number().min(0, { message: 'Hours must be a positive number.' }),
  partial_refund_percentage: z.coerce.number().min(0).max(100, { message: 'Percentage must be between 0 and 100.' }),
  is_active: z.boolean().default(true),
});

interface AddPolicyDialogProps {
  refreshData: () => void;
  onSuccessAdd: (policy: CancellationPolicy) => void;
}

const AddPolicyDialog: React.FC<AddPolicyDialogProps> = ({ refreshData, onSuccessAdd }) => {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      code: '',
      description: '',
      full_refund_hours: 24,
      partial_refund_hours: 48,
      partial_refund_percentage: 50,
      is_active: true,
    },
  });

  // Function to handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      const policyData: CreateCancellationPolicyInput = {
        name: values.name,
        code: values.code,
        description: values.description || '',
        full_refund_hours: values.full_refund_hours,
        partial_refund_hours: values.partial_refund_hours,
        partial_refund_percentage: values.partial_refund_percentage,
        is_active: values.is_active,
      };
      
      const newPolicy = await createCancellationPolicy(policyData);
      
      // Reset form and close dialog
      form.reset();
      setOpen(false);
      
      // Update UI with the new policy
      toast.success('Policy added successfully');
      if (newPolicy) {
        onSuccessAdd(newPolicy);
      }
    } catch (error) {
      console.error('Error adding cancellation policy:', error);
      toast.error('Failed to add policy');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Policy
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add New Cancellation Policy</DialogTitle>
          <DialogDescription>
            Enter the details for the new cancellation policy.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Policy Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Flexible" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Policy Code</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. FLEX" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe the details of this cancellation policy"
                      className="resize-none"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="full_refund_hours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Refund Hours</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="partial_refund_hours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Partial Refund Hours</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="partial_refund_percentage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Partial Refund Percentage</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" max="100" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Active Status</FormLabel>
                    <FormDescription>
                      Set whether this policy is active and available for use
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setOpen(false)} 
                type="button"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Add Policy
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddPolicyDialog; 