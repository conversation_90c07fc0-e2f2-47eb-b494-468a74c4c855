import React, { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Edit, Loader2 } from 'lucide-react';
import { updateCarBrand } from '@/app/actions/admin/listings';
import { CarBrand, UpdateCarBrandInput } from '@/types/listings';
import { toast } from 'sonner';

// Define the form schema
const formSchema = z.object({
  name: z.string().min(2, { message: 'Brand name must be at least 2 characters.' }),
});

interface EditBrandDialogProps {
  brand: CarBrand;
  refreshData: () => void;
  onSuccessUpdate: (updatedBrand: CarBrand) => void;
}

const EditBrandDialog: React.FC<EditBrandDialogProps> = ({ 
  brand, 
  refreshData,
  onSuccessUpdate 
}) => {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: brand.name,
    },
  });

  // Update form values when brand changes
  useEffect(() => {
    form.reset({
      name: brand.name,
    });
  }, [brand, form]);

  // Function to handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      // Only include fields that have changed
      const updates: UpdateCarBrandInput = {};
      if (values.name !== brand.name) {
        updates.name = values.name;
      }
      
      // Only proceed if there are changes
      if (Object.keys(updates).length > 0) {
        const updatedBrand = await updateCarBrand(brand.id, updates);
        toast.success('Brand updated successfully');
        
        // Update UI with the updated brand using the callback
        if (updatedBrand) {
          onSuccessUpdate(updatedBrand);
        }
      } else {
        toast.info('No changes to update');
      }
      
      // Close dialog
      setOpen(false);
    } catch (error) {
      console.error('Error updating car brand:', error);
      toast.error('Failed to update brand');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon">
          <Edit className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Car Brand</DialogTitle>
          <DialogDescription>
            Update the details for this car brand.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Brand Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setOpen(false)} 
                type="button"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditBrandDialog; 