import React, { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Edit, Loader2 } from 'lucide-react';
import { updateCancellationPolicy } from '@/app/actions/admin/listings';
import { CancellationPolicy, UpdateCancellationPolicyInput } from '@/types/listings';
import { toast } from 'sonner';

// Define the form schema
const formSchema = z.object({
  name: z.string().min(2, { message: 'Policy name must be at least 2 characters.' }),
  code: z.string().min(1, { message: 'Policy code is required.' }),
  description: z.string().optional(),
  full_refund_hours: z.coerce.number().min(0, { message: 'Hours must be a positive number.' }),
  partial_refund_hours: z.coerce.number().min(0, { message: 'Hours must be a positive number.' }),
  partial_refund_percentage: z.coerce.number().min(0).max(100, { message: 'Percentage must be between 0 and 100.' }),
  is_active: z.boolean().default(true),
});

interface EditPolicyDialogProps {
  policy: CancellationPolicy;
  refreshData: () => void;
  onSuccessUpdate: (updatedPolicy: CancellationPolicy) => void;
}

const EditPolicyDialog: React.FC<EditPolicyDialogProps> = ({ 
  policy, 
  refreshData,
  onSuccessUpdate 
}) => {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: policy.name,
      code: policy.code,
      description: policy.description || '',
      full_refund_hours: policy.full_refund_hours,
      partial_refund_hours: policy.partial_refund_hours,
      partial_refund_percentage: policy.partial_refund_percentage,
      is_active: policy.is_active,
    },
  });

  // Update form values when policy changes
  useEffect(() => {
    form.reset({
      name: policy.name,
      code: policy.code,
      description: policy.description || '',
      full_refund_hours: policy.full_refund_hours,
      partial_refund_hours: policy.partial_refund_hours,
      partial_refund_percentage: policy.partial_refund_percentage,
      is_active: policy.is_active,
    });
  }, [policy, form]);

  // Function to handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      // Only include fields that have changed
      const updates: UpdateCancellationPolicyInput = {};
      if (values.name !== policy.name) {
        updates.name = values.name;
      }
      if (values.code !== policy.code) {
        updates.code = values.code;
      }
      if (values.description !== policy.description) {
        updates.description = values.description;
      }
      if (values.full_refund_hours !== policy.full_refund_hours) {
        updates.full_refund_hours = values.full_refund_hours;
      }
      if (values.partial_refund_hours !== policy.partial_refund_hours) {
        updates.partial_refund_hours = values.partial_refund_hours;
      }
      if (values.partial_refund_percentage !== policy.partial_refund_percentage) {
        updates.partial_refund_percentage = values.partial_refund_percentage;
      }
      if (values.is_active !== policy.is_active) {
        updates.is_active = values.is_active;
      }
      
      // Only proceed if there are changes
      if (Object.keys(updates).length > 0) {
        const updatedPolicy = await updateCancellationPolicy(policy.id, updates);
        toast.success('Policy updated successfully');
        
        // Update UI with the updated policy using the callback
        if (updatedPolicy) {
          onSuccessUpdate(updatedPolicy);
        }
      } else {
        toast.info('No changes to update');
      }
      
      // Close dialog
      setOpen(false);
    } catch (error) {
      console.error('Error updating cancellation policy:', error);
      toast.error('Failed to update policy');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon">
          <Edit className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Cancellation Policy</DialogTitle>
          <DialogDescription>
            Update the details for this cancellation policy.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Policy Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Policy Code</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      className="resize-none"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="full_refund_hours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Refund Hours</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="partial_refund_hours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Partial Refund Hours</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="partial_refund_percentage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Partial Refund Percentage</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" max="100" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Active Status</FormLabel>
                    <FormDescription>
                      Set whether this policy is active and available for use
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setOpen(false)} 
                type="button"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditPolicyDialog; 