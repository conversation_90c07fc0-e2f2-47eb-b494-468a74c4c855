import React, { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Edit, Loader2 } from 'lucide-react';
import { updateCarFeature } from '@/app/actions/admin/listings';
import { CarFeature, UpdateCarFeatureInput } from '@/types/listings';
import { toast } from 'sonner';

// Define the form schema
const formSchema = z.object({
  name: z.string().min(2, { message: 'Feature name must be at least 2 characters.' }),
  icon: z.string().optional(),
  category: z.string().optional(),
});

// Define feature categories
const featureCategories = [
  "Safety",
  "Comfort",
  "Technology",
  "Performance",
  "General"
];

interface EditFeatureDialogProps {
  feature: CarFeature;
  refreshData: () => void;
  onSuccessUpdate: (updatedFeature: CarFeature) => void;
}

const EditFeatureDialog: React.FC<EditFeatureDialogProps> = ({ 
  feature, 
  refreshData,
  onSuccessUpdate 
}) => {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: feature.name,
      icon: feature.icon || '',
      category: feature.category || 'General',
    },
  });

  // Update form values when feature changes
  useEffect(() => {
    form.reset({
      name: feature.name,
      icon: feature.icon || '',
      category: feature.category || 'General',
    });
  }, [feature, form]);

  // Function to handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      // Only include fields that have changed
      const updates: UpdateCarFeatureInput = {};
      if (values.name !== feature.name) {
        updates.name = values.name;
      }
      if (values.icon !== feature.icon) {
        updates.icon = values.icon || '';
      }
      if (values.category !== feature.category) {
        updates.category = values.category || 'General';
      }
      
      // Only proceed if there are changes
      if (Object.keys(updates).length > 0) {
        const updatedFeature = await updateCarFeature(feature.id, updates);
        toast.success('Feature updated successfully');
        
        // Use the returned updated feature to update the UI
        onSuccessUpdate(updatedFeature);
      } else {
        toast.info('No changes to update');
      }
      
      // Close dialog
      setOpen(false);
    } catch (error) {
      console.error('Error updating car feature:', error);
      toast.error('Failed to update feature');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon">
          <Edit className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Car Feature</DialogTitle>
          <DialogDescription>
            Update the details for this car feature.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Feature Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="icon"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. map-pin" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {featureCategories.map(category => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setOpen(false)} 
                type="button"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditFeatureDialog; 