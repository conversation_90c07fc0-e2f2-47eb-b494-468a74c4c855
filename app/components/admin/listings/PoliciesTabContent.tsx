import React, { useState, useEffect } from "react";
import { CancellationPolicy } from "@/types/listings";
import { TableHead, TableRow, TableCell } from "@/components/ui/table";
import TableWrapper from "./TableWrapper";
import AddPolicyDialog from "./dialogs/AddPolicyDialog";
import EditPolicyDialog from "./dialogs/EditPolicyDialog";

interface PoliciesTabContentProps {
  policies: CancellationPolicy[];
  loading: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  hasMore: boolean;
  loadMoreItems: () => void;
  filterItems: (items: CancellationPolicy[], term: string) => CancellationPolicy[];
}

const PoliciesTabContent: React.FC<PoliciesTabContentProps> = ({
  policies: initialPolicies,
  loading,
  searchTerm,
  setSearchTerm,
  hasMore,
  loadMoreItems,
  filterItems
}) => {
  // Local state to manage policies
  const [policies, setPolicies] = useState<CancellationPolicy[]>(initialPolicies);

  // Update local state when props change
  useEffect(() => {
    setPolicies(initialPolicies);
  }, [initialPolicies]);

  // Function to handle policy updates
  const handlePolicyUpdate = (updatedPolicy: CancellationPolicy) => {
    setPolicies(prevPolicies =>
      prevPolicies.map(policy => 
        policy.id === updatedPolicy.id ? updatedPolicy : policy
      )
    );
  };

  // Function to handle policy additions
  const handlePolicyAdd = (newPolicy: CancellationPolicy) => {
    setPolicies(prevPolicies => [...prevPolicies, newPolicy]);
  };

  // Empty refreshData function for compatibility
  const refreshData = () => {
    // No longer used, but kept for compatibility
  };

  const renderHeader = () => (
    <TableRow>
      <TableHead>Policy Name</TableHead>
      <TableHead>Code</TableHead>
      <TableHead>Full Refund (Hours)</TableHead>
      <TableHead>Partial Refund (Hours)</TableHead>
      <TableHead>Active</TableHead>
      <TableHead className="w-[80px] text-right">Actions</TableHead>
    </TableRow>
  );

  const renderRow = (policy: CancellationPolicy) => (
    <TableRow key={policy.id}>
      <TableCell>{policy.name}</TableCell>
      <TableCell>{policy.code}</TableCell>
      <TableCell>{policy.full_refund_hours}</TableCell>
      <TableCell>{policy.partial_refund_hours}</TableCell>
      <TableCell>{policy.is_active ? "Yes" : "No"}</TableCell>
      <TableCell className="flex items-center justify-end gap-2">
        <EditPolicyDialog 
          policy={policy} 
          refreshData={refreshData} 
          onSuccessUpdate={handlePolicyUpdate} 
        />
      </TableCell>
    </TableRow>
  );

  return (
    <TableWrapper
      title="Cancellation Policies"
      items={policies}
      loading={loading}
      searchTerm={searchTerm}
      setSearchTerm={setSearchTerm}
      hasMore={hasMore && !searchTerm.trim()} // Only show "load more" if not searching
      loadMoreItems={loadMoreItems}
      addButton={
        <AddPolicyDialog 
          refreshData={refreshData} 
          onSuccessAdd={handlePolicyAdd} 
        />
      }
      renderHeader={renderHeader}
      renderRow={renderRow}
      filterItems={filterItems}
    />
  );
};

export default PoliciesTabContent; 