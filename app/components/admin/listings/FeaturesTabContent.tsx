import React, { useState, useEffect } from "react";
import { CarFeature } from "@/types/listings";
import { TableHead, TableRow, TableCell } from "@/components/ui/table";
import TableWrapper from "./TableWrapper";
import AddFeatureDialog from "./dialogs/AddFeatureDialog";
import EditFeatureDialog from "./dialogs/EditFeatureDialog";

interface FeaturesTabContentProps {
  features: CarFeature[];
  loading: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  hasMore: boolean;
  loadMoreItems: () => void;
  filterItems: (items: CarFeature[], term: string) => CarFeature[];
}

const FeaturesTabContent: React.FC<FeaturesTabContentProps> = ({
  features: initialFeatures,
  loading,
  searchTerm,
  setSearchTerm,
  hasMore,
  loadMoreItems,
  filterItems
}) => {
  // Local state to manage features
  const [features, setFeatures] = useState<CarFeature[]>(initialFeatures);

  // Update local state when props change
  useEffect(() => {
    setFeatures(initialFeatures);
  }, [initialFeatures]);

  // Function to handle feature updates
  const handleFeatureUpdate = (updatedFeature: CarFeature) => {
    setFeatures(prevFeatures =>
      prevFeatures.map(feature => 
        feature.id === updatedFeature.id ? updatedFeature : feature
      )
    );
  };

  // Function to handle feature additions
  const handleFeatureAdd = (newFeature: CarFeature) => {
    setFeatures(prevFeatures => [...prevFeatures, newFeature]);
  };

  // Empty refreshData function for compatibility
  const refreshData = () => {
    // No longer used, but kept for compatibility
  };

  const renderHeader = () => (
    <TableRow>
      <TableHead>Feature Name</TableHead>
      <TableHead>Icon</TableHead>
      <TableHead>Category</TableHead>
      <TableHead className="w-[80px] text-right">Actions</TableHead>
    </TableRow>
  );

  const renderRow = (feature: CarFeature) => (
    <TableRow key={feature.id}>
      <TableCell>{feature.name}</TableCell>
      <TableCell>
        <p className="text-xs">{feature.icon}</p>
      </TableCell>
      <TableCell>{feature.category || "General"}</TableCell>
      <TableCell className="flex items-center justify-end gap-2">
        <EditFeatureDialog 
          feature={feature} 
          refreshData={refreshData} 
          onSuccessUpdate={handleFeatureUpdate} 
        />
      </TableCell>
    </TableRow>
  );

  return (
    <TableWrapper
      title="Car Features"
      items={features}
      loading={loading}
      searchTerm={searchTerm}
      setSearchTerm={setSearchTerm}
      hasMore={hasMore && !searchTerm.trim()} // Only show "load more" if not searching
      loadMoreItems={loadMoreItems}
      addButton={
        <AddFeatureDialog 
          refreshData={refreshData} 
          onSuccessAdd={handleFeatureAdd} 
        />
      }
      renderHeader={renderHeader}
      renderRow={renderRow}
      filterItems={filterItems}
    />
  );
};

export default FeaturesTabContent; 