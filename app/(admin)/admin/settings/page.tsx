import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GeneralSettings } from "@/components/admin/settings/general-settings"
import { EmailSettings } from "@/components/admin/settings/email-settings"
import { PaymentSettings } from "@/components/admin/settings/payment-settings"
import { NotificationSettings } from "@/components/admin/settings/notification-settings"
import { UserSettings } from "@/components/admin/settings/user-settings"
import { CountriesSettings } from "@/components/admin/settings/countries-settings"
import { getAdminSettings, getCountries } from "@/app/actions/admin/admin-actions"

export const metadata: Metadata = {
  title: "Settings | Travella Admin",
  description: "Configure system settings for the Travella platform",
}

export default async function AdminSettingsPage() {
  const settings = await getAdminSettings()
  const countries = await getCountries()

  return (
    <div className="flex-1 space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">Manage and configure system settings for the platform.</p>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="payment">Payment</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="countries">Countries</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Configure general settings for the platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <GeneralSettings settings={settings} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Settings</CardTitle>
              <CardDescription>
                Configure email settings for sending notifications.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EmailSettings settings={settings} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="payment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Settings</CardTitle>
              <CardDescription>
                Configure payment gateways and transaction settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PaymentSettings settings={settings} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Configure notification preferences and templates.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NotificationSettings settings={settings} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Settings</CardTitle>
              <CardDescription>
                Configure user registration and default settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserSettings settings={settings} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="countries" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Supported Countries</CardTitle>
              <CardDescription>
                Manage countries where the platform operates.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CountriesSettings countries={countries} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
