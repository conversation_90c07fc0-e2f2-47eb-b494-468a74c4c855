"use client"

import { useEffect, useState, useRef, useCallback } from "react"
import { getBookingPayments } from "@/app/actions/admin/payments"
import { BookingPayment } from "@/types/payments"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Search, Filter, Eye } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import PaymentFilters from "@/components/admin/payments/payment-filters"
import PaymentsList from "@/components/admin/payments/payments-list"
import PaymentDetailsModal from "@/components/admin/payments/payment-details-modal"
import { PaymentFilterValues } from "@/types/payments"

export default function BookingPaymentsPage() {
  const [payments, setPayments] = useState<BookingPayment[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [totalPayments, setTotalPayments] = useState(0)
  const [filters, setFilters] = useState<PaymentFilterValues>({
    status: null,
    payment_type: null,
    payment_method: null,
    car_owner_id: null,
    renter_id: null,
    start_date: null,
    end_date: null,
  })
  const [selectedPayment, setSelectedPayment] = useState<BookingPayment | null>(null)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const { toast } = useToast()
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadingRef = useRef<HTMLDivElement>(null)
  const itemsPerPage = 20

  const fetchPayments = useCallback(async (pageNum: number, reset = false) => {
    try {
      setLoading(true)
      const { data, count, totalPages, hasNextPage } = await getBookingPayments({
        page: pageNum,
        items_per_page: itemsPerPage,
        // ...filters
      })

      console.log("Payments response", data);
      
      if (reset) {
        setPayments(data)
      } else {
        setPayments(prev => [...prev, ...data])
      }
      
      setHasMore(hasNextPage)
      setTotalPayments(count)
    } catch (error) {
      console.error("Failed to fetch payments:", error)
      toast({
        title: "Error",
        description: "Failed to fetch booking payments. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }, [filters, toast])

  useEffect(() => {
    fetchPayments(1, true)
  }, [fetchPayments])

  useEffect(() => {
    if (loading) return
    
    if (observerRef.current) {
      observerRef.current.disconnect()
    }
    
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore) {
          fetchPayments(page + 1)
        }
      },
      { threshold: 1.0 }
    )
    
    observerRef.current = observer
    
    if (loadingRef.current) {
      observer.observe(loadingRef.current)
    }
    
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [loading, hasMore, page, fetchPayments])

  const handleFilterChange = (newFilters: PaymentFilterValues) => {
    setFilters(newFilters)
    setPage(1)
    fetchPayments(1, true)
    setIsFilterOpen(false)
  }

  const handleResetFilters = () => {
    setFilters({
      status: null,
      payment_type: null,
      payment_method: null,
      car_owner_id: null,
      renter_id: null,
      start_date: null,
      end_date: null,
    })
    setPage(1)
    fetchPayments(1, true)
    setIsFilterOpen(false)
  }

  const handleViewDetails = (payment: BookingPayment) => {
    setSelectedPayment(payment)
    setIsDetailsModalOpen(true)
  }

  return (
    <div>
      <Card className="border-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>All Payments</CardTitle>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              onClick={() => setIsFilterOpen(true)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by ID, user..."
                className="pl-8"
                onChange={(e) => {
                  // Implement search functionality if needed
                }}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <PaymentsList 
            payments={payments}
            loading={loading}
            onViewDetails={handleViewDetails}
          />
          
          {hasMore && (
            <div ref={loadingRef} className="py-4 flex justify-center">
              {loading && <Skeleton className="h-8 w-32" />}
            </div>
          )}
        </CardContent>
      </Card>

      <PaymentFilters 
        isOpen={isFilterOpen}
        onOpenChange={setIsFilterOpen}
        filters={filters}
        onApplyFilters={handleFilterChange}
        onResetFilters={handleResetFilters}
      />

      {selectedPayment && (
        <PaymentDetailsModal
          isOpen={isDetailsModalOpen}
          payment={selectedPayment}
          onOpenChange={setIsDetailsModalOpen}
        />
      )}
    </div>
  )
} 