import type { Metada<PERSON> } from "next"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CarApprovalTable } from "@/components/admin/cars/car-approval-table"
import { getAllCarListings } from "@/app/actions/admin/admin-actions"
import { CarFilters } from "@/components/admin/cars/car-filters"
import { CarStatus } from "@/types/supabase"
export const metadata: Metadata = {
  title: "Car Listings | Travella Admin",
  description: "Manage car listings on the Travella platform",
}

interface AdminCarsPageProps {
  searchParams: {
    page?: string
    limit?: string
    status?: string
    country_id?: string
    search?: string
  }
}

export default async function AdminCarsPage({ searchParams }: AdminCarsPageProps) {
  const filters = (await searchParams)

  // Parse query parameters
  const page = filters?.page ? parseInt(filters.page) : 1
  const limit = filters?.limit ? parseInt(filters.limit) : 10
  const status = filters?.status || undefined
  const country_id = filters?.country_id || undefined
  const search = filters?.search || undefined
  
  // Get car listings with pagination and filters
  const { data: cars, count, has_next, items_per_page, total_pages } = await getAllCarListings({
    status: status as CarStatus | null,
    country_id: country_id as string | null,
    search: search as string | null,
    items_per_page: limit,
    page
  })

  return (
    <div className="flex-1 space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">{count} Car Listings</h1>
        <p className="text-muted-foreground">Manage and review car listings submitted by hosts.</p>
      </div>

      <Card className="border-border/60 shadow-sm">
        <CardHeader>
          
          {/* Add filters component */}
          <CarFilters 
            currentStatus={status} 
            currentCountryId={country_id}
            currentSearch={search}
          />
        </CardHeader>
        <CardContent>
          <CarApprovalTable 
            cars={cars} 
            pagination={{
              totalItems: count,
              hasNextPage: has_next,
              currentPage: page,
              itemsPerPage: items_per_page,
              totalPages: total_pages,
              // sortBy: sort_by,
              // sortDirection: sort_direction
            }}
            searchParams={{
              status,
              country_id,
              search,
              limit: limit.toString()
            }}
          />
        </CardContent>
      </Card>
    </div>
  )
}

