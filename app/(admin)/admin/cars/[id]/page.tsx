import { notFound } from "next/navigation"
import { getCarDetails, getCarListingUpdates } from "@/app/actions/admin/admin-actions"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Car } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  CarDetailsTab,
  CarFeaturesTab,
  CarHistoryTab,
  CarLocationsTab,
  CarOverview,
  CarOwnerInfo,
  CarPoliciesTab
} from "@/components/admin/cars/details"
import { CarApprovalActions } from "@/components/admin/cars/car-approval-actions"

export default async function CarDetailsPage({ params }: { params: { id: string } }) {
  const carId = params.id

  // Fetch car details and update history
  const [car, carUpdates] = await Promise.all([
    getCarDetails(carId),
    getCarListingUpdates(carId)
  ]);

  // If car not found, return 404
  if (!car) {
    notFound()
  }

  const carMainLocation = car.available_pickup_locations.find(location => location.is_main)

  return (
    <div className="space-y-6">
      {/* Header with Car Info and Actions */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 bg-white border rounded-lg p-6 mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">
            {car.brand_name || car.brand} {car.model} {car.year ? `(${car.year})` : ''}
          </h1>
          <div className="flex flex-wrap items-center gap-2 text-muted-foreground">
            <span>ID: {car.id?.substring(0, 8)}</span>
            <span className="inline-block w-1 h-1 rounded-full bg-muted-foreground mx-1"></span>
            <span>{new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: car.currency_code || "USD",
            }).format(Number(car.daily_rate || 0))}/day</span>
            <span className="inline-block w-1 h-1 rounded-full bg-muted-foreground mx-1"></span>
            <span>{car.fuel_type || 'Unknown'}</span>
            {carMainLocation && (
              <>
                <span className="inline-block w-1 h-1 rounded-full bg-muted-foreground mx-1"></span>
                <span>{carMainLocation.formatted_address}</span>
              </>
            )}
          </div>
        </div>
        <div className="flex gap-3 self-end md:self-auto">
          <Button variant="outline" asChild>
            <a href="/admin/cars">
              <Car className="mr-2 h-4 w-4" />
              Back to Listings
            </a>
          </Button>
          <CarApprovalActions carId={car.id} />
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid grid-cols-5 max-w-2xl mx-auto mb-6">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="locations">Locations</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="policies">Policies</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <div className="grid gap-6 md:grid-cols-3">
          {/* Left Column - Tab Content */}
          <div className="md:col-span-2">
            <TabsContent value="details" className="mt-0">
              <CarDetailsTab car={car} carMainLocation={carMainLocation} />
            </TabsContent>

            <TabsContent value="locations" className="mt-0">
              <CarLocationsTab car={car} />
            </TabsContent>

            <TabsContent value="features" className="mt-0">
              <CarFeaturesTab car={car} />
            </TabsContent>

            <TabsContent value="policies" className="mt-0">
              <CarPoliciesTab car={car} />
            </TabsContent>

            <TabsContent value="history" className="mt-0">
              <CarHistoryTab carUpdates={carUpdates} />
            </TabsContent>
          </div>

          {/* Right Column - Sidebar Info */}
          <div className="space-y-6">
            <CarOwnerInfo car={car} />
            <CarOverview car={car} />
            
            <Button variant="outline" className="w-full" asChild>
              <a href="/admin/cars">
                <Car className="mr-2 h-4 w-4" />
                Back to Car Listings
              </a>
            </Button>
          </div>
        </div>
      </Tabs>
    </div>
  )
}
