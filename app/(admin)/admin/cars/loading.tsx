import { <PERSON>, CardContent, <PERSON>Header } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { CarsTableSkeleton } from "@/components/admin/cars/cars-table-skeleton"

export default function Loading() {
  return (
    <div className="flex-1 space-y-6">
      <div className="flex flex-col gap-2">
        <Skeleton className="h-10 w-[300px]" />
        <Skeleton className="h-5 w-[400px]" />
      </div>

      <div className="flex gap-2 border-b pb-1">
        {Array.from({ length: 3 }).map((_, index) => (
          <Skeleton key={index} className="h-10 w-[120px]" />
        ))}
      </div>

      <Card>
        <CardHeader className="pb-3">
          <Skeleton className="h-6 w-[200px] mb-2" />
          <Skeleton className="h-4 w-[300px]" />
        </CardHeader>
        <CardContent>
          <CarsTableSkeleton />
        </CardContent>
      </Card>
    </div>
  )
}
