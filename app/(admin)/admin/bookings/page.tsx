"use client"

import { useState, useEffect, useCallback, useRef, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useInView } from "react-intersection-observer";
import { format, parseISO, isValid } from "date-fns";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Filter, 
  RotateCcw, 
  Search
} from "lucide-react";
import { getAdminBookingsWithFilters, getCountries } from "@/app/actions/admin/admin-actions";
import { BookingResponse, FilterBookingsPayload } from "@/types/bookings";
import { BookingStatus } from "@/types/supabase";
import Image from "next/image";
import { Country } from "@/types/settings";

export default function BookingsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  
  // Ref to track initial load
  const initialLoadRef = useRef(true);
  
  // Refs for continuous scrolling
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [bookings, setBookings] = useState<BookingResponse[]>([]);
  const [totalBookings, setTotalBookings] = useState(0);
  const [countries, setCountries] = useState<Country[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Filter states
  const [status, setStatus] = useState<BookingStatus | "all" | undefined>(
    searchParams.get("status") as BookingStatus | "all" | undefined
  );
  const [searchTerm, setSearchTerm] = useState(
    searchParams.get("search") || ""
  );
  const [fromDate, setFromDate] = useState<Date | undefined>(
    searchParams.get("from") 
      ? parseISO(searchParams.get("from") as string)
      : undefined
  );
  const [toDate, setToDate] = useState<Date | undefined>(
    searchParams.get("to") 
      ? parseISO(searchParams.get("to") as string)
      : undefined
  );
  const [country, setCountry] = useState<string>(
    searchParams.get("country") || "all_countries"
  );
  
  // Intersection observer for infinite scrolling
  const { ref, inView } = useInView();

  // Function to fetch bookings with current filters
  const fetchBookings = useCallback(async (pageNum: number, resetResults = false) => {
    try {
      setIsLoading(true);
      
      const filters: FilterBookingsPayload = {
        status: status !== 'all' ? status : undefined,
        search: searchTerm || undefined,
        startDate: fromDate ? format(fromDate, 'yyyy-MM-dd') : undefined,
        endDate: toDate ? format(toDate, 'yyyy-MM-dd') : undefined,
        country: country && country !== 'all_countries' ? country : undefined
      };
      
      const result = await getAdminBookingsWithFilters(pageNum, 20, filters);
      
      if (resetResults) {
        setBookings(result.bookings);
      } else {
        setBookings(prev => [...prev, ...result.bookings]);
      }
      
      setHasMore(result.hasMore);
      setTotalBookings(result.total);
    } catch (error) {
      console.error("Error fetching bookings:", error);
    } finally {
      setIsLoading(false);
    }
  }, [status, searchTerm, fromDate, toDate, country]);
  
  // Load countries for filter
  useEffect(() => {
    const loadCountries = async () => {
      try {
        const countriesList = await getCountries();
        setCountries(countriesList);
      } catch (error) {
        console.error("Error loading countries:", error);
      }
    };
    
    loadCountries();
  }, []);
  
  // Initial load and parameter-based filtering
  useEffect(() => {
    if (initialLoadRef.current) {
      fetchBookings(1, true);
      initialLoadRef.current = false;
    }
  }, [fetchBookings]);
  
  // Effect for infinite scrolling - modify to check manually inside scroll container
  useEffect(() => {
    // We only need to use inView for initial loads, after that we handle scrolling manually
    const tableContainer = document.querySelector('.bookings-table-container');
    const hasScrolled = tableContainer instanceof HTMLElement && tableContainer.scrollTop > 0;
    
    if ((inView || hasScrolled) && hasMore && !isLoading && !initialLoadRef.current) {
      setPage(prevPage => prevPage + 1);
      fetchBookings(page + 1);
    }
  }, [inView, hasMore, isLoading, fetchBookings, page]);

  // Add scroll event handler for our table container
  useEffect(() => {
    const handleScroll = (e: Event) => {
      const container = e.target as HTMLElement;
      const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;
      
      if (scrollBottom < 100 && hasMore && !isLoading) {
        setPage(prevPage => prevPage + 1);
        fetchBookings(page + 1);
      }
    };

    const container = document.querySelector('.bookings-table-container');
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [hasMore, isLoading, fetchBookings, page]);
  
  // Apply filters
  const applyFilters = () => {
    startTransition(() => {
      setPage(1);
      fetchBookings(1, true);
      
      // Update URL with filters
      const params = new URLSearchParams();
      if (status !== 'all') params.set('status', status as BookingStatus);
      if (searchTerm) params.set('search', searchTerm);
      if (fromDate && isValid(fromDate)) params.set('from', format(fromDate, 'yyyy-MM-dd'));
      if (toDate && isValid(toDate)) params.set('to', format(toDate, 'yyyy-MM-dd'));
      if (country) params.set('country', country);
      
      const queryString = params.toString();
      const url = queryString ? `/admin/bookings?${queryString}` : '/admin/bookings';
      router.push(url);
    });
  };
  
  // Reset filters
  const resetFilters = () => {
    setStatus('all');
    setSearchTerm('');
    setFromDate(undefined);
    setToDate(undefined);
    setCountry('all_countries');
    
    startTransition(() => {
      router.push('/admin/bookings');
      setPage(1);
      fetchBookings(1, true);
    });
  };
  
  // Navigate to booking details page
  const handleViewBooking = (bookingId: string) => {
    router.push(`/admin/bookings/${bookingId}`);
  };
  
  // Get status badge
  const getStatusBadge = (status: BookingStatus) => {
    switch (status) {
      case "confirmed":
        return <Badge className="bg-green-500 text-white">Confirmed</Badge>;
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>;
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      case "completed":
        return <Badge className="bg-blue-500 text-white">Completed</Badge>;
      case "ongoing":
        return <Badge className="bg-orange-500 text-white">Ongoing</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };
  
  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Bookings</h1>
          <p className="text-muted-foreground">
            {totalBookings > 0 ? `${totalBookings} total bookings` : "Manage all bookings"}
          </p>
        </div>
      </div>
      
      {/* Filters */}
      <div className="flex flex-row items-center justify-between bg-card border rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Status Filter */}
          <div className="flex flex-col space-y-1.5">
            <label htmlFor="status" className="text-sm font-medium">Status</label>
            <Select value={status} onValueChange={(value) => setStatus(value as BookingStatus | "all" | undefined)}>
              <SelectTrigger id="status">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="ongoing">Ongoing</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Search Input */}
          <div className="flex flex-col space-y-1.5">
            <label htmlFor="search" className="text-sm font-medium">Search</label>
            <div className="relative flex items-center">
              <Search className="absolute left-2.5 top-2.7 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Car, renter or booking ID"
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          {/* Date Range Filter */}
          {/* <div className="flex flex-col space-y-1.5">
            <label htmlFor="from-date" className="text-sm font-medium">From Date</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="from-date"
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !fromDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {fromDate ? format(fromDate, "PPP") : "Pick a date"}
                  {fromDate && (
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="ml-auto h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        setFromDate(undefined);
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={fromDate}
                  onSelect={setFromDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div> */}
          
          {/* <div className="flex flex-col space-y-1.5">
            <label htmlFor="to-date" className="text-sm font-medium">To Date</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="to-date"
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !toDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {toDate ? format(toDate, "PPP") : "Pick a date"}
                  {toDate && (
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="ml-auto h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        setToDate(undefined);
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={toDate}
                  onSelect={setToDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div> */}
          
          {/* Country Filter */}
          <div className="flex flex-col space-y-1.5">
            <label htmlFor="country" className="text-sm font-medium">Country</label>
            <Select value={country} onValueChange={setCountry}>
              <SelectTrigger id="country">
                <SelectValue placeholder="All Countries" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_countries">All Countries</SelectItem>
                {countries.map((country) => (
                  <SelectItem key={country.id} value={country.id}>
                    <div className="flex gap-2">
                      <Image src={country.flag_image} alt={country.name} width={20} height={20} className="rounded-md" />
                      {country.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="flex justify-end space-x-2 mt-4">
          <Button variant="outline" onClick={resetFilters} disabled={isPending}>
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset
          </Button>
          <Button onClick={applyFilters} disabled={isPending}>
            <Filter className="mr-2 h-4 w-4" />
            Apply Filters
          </Button>
        </div>
      </div>
      
      {/* Bookings Table */}
      <div className="bg-card border rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <div 
            className="bookings-table-container h-[calc(100vh-400px)] overflow-y-auto" 
            style={{ 
              scrollBehavior: 'smooth',
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(156, 163, 175, 0.5) transparent'
            }}
          >
            <table className="w-full">
              <thead className="sticky top-0 bg-muted/50 z-10">
                <tr>
                  <th className="px-4 py-3 text-left font-medium text-sm">Booking ID</th>
                  <th className="px-4 py-3 text-left font-medium text-sm">Host</th>
                  <th className="px-4 py-3 text-left font-medium text-sm">Car</th>
                  <th className="px-4 py-3 text-left font-medium text-sm">Dates</th>
                  <th className="px-4 py-3 text-left font-medium text-sm">Status</th>
                  <th className="px-4 py-3 text-left font-medium text-sm">Amount</th>
                  <th className="px-4 py-3 text-left font-medium text-sm">Created</th>
                  <th className="px-4 py-3 text-right font-medium text-sm">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {bookings.map((booking) => (
                  <tr 
                    key={booking.id} 
                    className="hover:bg-muted/50 cursor-pointer"
                    onClick={() => handleViewBooking(booking.id)}
                  >
                    <td className="px-4 py-3 text-sm">
                      <div className="font-mono">{booking.id.substring(0, 8)}</div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <div className="flex items-center gap-2">
                        {booking.host?.avatar && (
                          <Image 
                            src={booking.host.avatar} 
                            alt={booking.host?.first_name || ""} 
                            width={24} 
                            height={24} 
                            className="rounded-full"
                          />
                        )}
                        <div>
                          <div>{booking.host?.first_name || ""}</div>
                          <div className="text-muted-foreground text-xs">{booking.host?.last_name || ""}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <div className="font-medium">{booking.brand_name} {booking.model}</div>
                      <div className="text-muted-foreground text-xs">{booking.year}</div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <div>{booking.start_date ? format(new Date(booking.start_date), "MMM d, yyyy") : "N/A"}</div>
                      <div className="text-muted-foreground text-xs">
                        {booking.end_date ? format(new Date(booking.end_date), "MMM d, yyyy") : "N/A"}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {getStatusBadge(booking.status)}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <div>
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: booking.currency_details?.code || "UGX",
                        }).format(booking.total_amount || 0)}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {booking.created_at 
                        ? format(new Date(booking.created_at), "MMM d, yyyy") 
                        : "N/A"
                      }
                    </td>
                    <td className="px-4 py-3 text-sm text-right">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewBooking(booking.id);
                        }}
                      >
                        View
                      </Button>
                    </td>
                  </tr>
                ))}
                
                {/* Loading skeletons */}
                {isLoading && Array(3).fill(0).map((_, i) => (
                  <tr key={`skeleton-${i}`}>
                    <td className="px-4 py-3"><Skeleton className="h-5 w-20" /></td>
                    <td className="px-4 py-3">
                      <Skeleton className="h-5 w-32 mb-1" />
                      <Skeleton className="h-4 w-16" />
                    </td>
                    <td className="px-4 py-3">
                      <Skeleton className="h-5 w-32 mb-1" />
                      <Skeleton className="h-4 w-24" />
                    </td>
                    <td className="px-4 py-3">
                      <Skeleton className="h-5 w-24 mb-1" />
                      <Skeleton className="h-4 w-24" />
                    </td>
                    <td className="px-4 py-3"><Skeleton className="h-6 w-20" /></td>
                    <td className="px-4 py-3"><Skeleton className="h-5 w-16" /></td>
                    <td className="px-4 py-3"><Skeleton className="h-5 w-20" /></td>
                    <td className="px-4 py-3 text-right"><Skeleton className="h-8 w-16 ml-auto" /></td>
                  </tr>
                ))}
                
                {/* No bookings message */}
                {!isLoading && bookings.length === 0 && (
                  <tr>
                    <td colSpan={8} className="px-4 py-8 text-center text-muted-foreground">
                      No bookings found. Try adjusting your filters.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            
            {/* Move the load more trigger inside the scrollable container */}
            {hasMore && !isLoading && (
              <div 
                ref={ref} 
                className="py-4 text-center text-sm text-muted-foreground"
              >
                Loading more bookings...
              </div>
            )}
          </div>
        </div>
        
        {/* End of results banner - outside scrollable area */}
        {!hasMore && !isLoading && bookings.length > 0 && (
          <div className="py-4 text-center text-sm text-muted-foreground border-t">
            You've reached the end of the results
          </div>
        )}
      </div>
    </div>
  );
} 