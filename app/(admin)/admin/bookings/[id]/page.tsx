"use client"

import { use<PERSON>ara<PERSON> } from "next/navigation"
import { 
  getBookingById, 
  getRenterProfile, 
  getBookingIssues, 
  fetchBookingMoodificationRequests, 
  getBookingPayments
} from "@/app/actions/admin/admin-actions"
import { ArrowLeft, <PERSON>F<PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react"
import Link from "next/link"
import { QueryClient, QueryClientProvider, useQuery } from "@tanstack/react-query"

import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"

// External admin components still in use
import { BookingPayments } from "@/components/admin/bookings/details"

// New booking components
import { BookingModifications } from "@/app/components/booking/BookingModifications"
import { BookingIssues } from "@/app/components/booking/BookingIssues"
import BookingActions from "@/app/components/booking/BookingActions"
import { BookingInfo } from "@/app/components/booking/BookingInfo"
import { RenterInfo } from "@/app/components/booking/RenterInfo"
import { BookingNotFound } from "@/app/components/booking/BookingNotFound"
import { LoadingBookingDetails } from "@/app/components/booking/LoadingBookingDetails"
import { BookingResponse, BookingIssue, BookingModificationRequest } from "@/types/bookings"
import { PaymentSummary } from "@/components/admin/bookings/details/BookingSummary"

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
      refetchOnWindowFocus: false
    },
  },
})

const CarInfo = ({ car }: { car: any }) => (
  <Card>
    <CardHeader>
      <CardTitle className="text-lg">Car Details</CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="h-16 w-16 bg-muted rounded-md flex items-center justify-center">
          <CarFront className="h-8 w-8 text-muted-foreground" />
        </div>
        <div>
          <p className="font-medium text-lg">{car.brand_name} {car.model}</p>
          <p className="text-muted-foreground">{car.year} • {car.color || "Unknown color"}</p>
        </div>
      </div>
      
      <Separator />
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Daily Rate</p>
          <p className="font-medium">
            {new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: car.currency_details?.code || "USD"
            }).format(car.daily_charge || car.daily_rate || 0)}
            <span className="text-sm text-muted-foreground"> / day</span>
          </p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">License Plate</p>
          <p className="font-medium">{car.license_plate || "N/A"}</p>
        </div>
      </div>
      
      {car.features && car.features.length > 0 && (
        <div>
          <p className="text-sm text-muted-foreground mb-2">Features</p>
          <div className="flex flex-wrap gap-2">
            {car.features.map((feature: string, index: number) => (
              <Badge key={index} variant="outline">{feature}</Badge>
            ))}
          </div>
        </div>
      )}
      
      {car.description && (
        <div>
          <p className="text-sm text-muted-foreground">Description</p>
          <p className="text-sm">{car.description}</p>
        </div>
      )}
    </CardContent>
  </Card>
);

// Main component wrapper
export default function BookingDetailsPageWrapper() {
  return (
    <QueryClientProvider client={queryClient}>
      <BookingDetailsPage />
    </QueryClientProvider>
  )
}

// Separate components for progressive loading
const BookingSecondaryData = ({ 
  booking, 
  bookingIssues, 
  modificationRequests 
}: { 
  booking: BookingResponse; 
  bookingIssues: BookingIssue[]; 
  modificationRequests: BookingModificationRequest[]; 
}) => {
  // Fetch renter profile if booking data is available
  const {
    data: renterData,
    isLoading: renterLoading,
  } = useQuery({
    queryKey: ["renterProfile", booking?.renter_id],
    queryFn: () => getRenterProfile(booking?.renter_id || ""),
    enabled: !!booking?.renter_id,
  })

  // fetch booking payments
  const {
    data: bookingPayments = [],
    isLoading: paymentsLoading,
  } = useQuery({
    queryKey: ["bookingPayments", booking.id],
    queryFn: () => getBookingPayments({
        bookingId: booking.id,
    }),
    enabled: !!booking.id,
  })

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2 space-y-6">
        <div className="print-full-width">
          <BookingInfo booking={booking} />
        </div>
        <div>
          <CarInfo car={booking as any} />
        </div>
        {renterLoading ? (
          <Skeleton className="h-[200px] no-print" /> 
        ) : renterData ? (
          <div className="page-break-after">
            <RenterInfo renter={renterData} />
          </div>
        ) : (
          <Skeleton className="h-[200px] no-print" /> 
        )}
        <div className="page-break-after">
          {paymentsLoading ? (
            <Skeleton className="h-[200px] no-print" />
          ) : (
            <BookingPayments payments={bookingPayments} />
          )}
        </div>
      </div>
      <div className="space-y-6">
        <div>
          <PaymentSummary booking={booking} />
        </div>
        <div className="print-detailed">
          <BookingModifications requests={modificationRequests} booking={booking} />
        </div>
        <div className="print-detailed">
          <BookingIssues booking={booking} issues={bookingIssues} />
        </div>
      </div>
    </div>
  )
}

function BookingDetailsPage() {
  const { id } = useParams() as { id: string }
  const { toast } = useToast()

  // Function to copy booking ID to clipboard
  const copyBookingIdToClipboard = () => {
    navigator.clipboard.writeText(id);
    toast({
      title: "Booking ID copied",
      description: "The booking ID has been copied to the clipboard.",
      duration: 3000,
    });
  };

  // Fetch booking details - critical path data
  const {
    data: booking,
    isLoading: bookingLoading,
    error: bookingError,
  } = useQuery({
    queryKey: ["bookingDetails", id],
    queryFn: () => getBookingById(id),
    staleTime: 1000 * 60 * 10, // 10 minutes
  })

  // Fetch booking issues and modifications in parallel, but don't block initial render
  const {
    data: bookingIssues = [],
    isLoading: issuesLoading,
  } = useQuery({
    queryKey: ["bookingIssues", id],
    queryFn: () => getBookingIssues(id),
    enabled: !!id,
  })

  // Fetch booking modification requests
  const {
    data: modificationRequests = [],
    isLoading: modificationsLoading,
  } = useQuery({
    queryKey: ["bookingModifications", id],
    queryFn: () => fetchBookingMoodificationRequests(id),
    enabled: !!id,
  })

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return <Badge className="bg-green-500 text-white">Confirmed</Badge>;
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>;
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      case "completed":
        return <Badge className="bg-blue-500 text-white">Completed</Badge>;
      case "ongoing":
        return <Badge className="bg-orange-500 text-white">Ongoing</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  // Handle error state
  if (bookingError || (!bookingLoading && !booking)) {
    return <BookingNotFound />
  }

  return (
    <div className="container py-6">
      <div className="w-full max-w-7xl mx-auto">
        {/* Header with back button and booking ID */}
        <div className="flex items-center justify-between mb-6 no-print">
          <Button variant="ghost" size="sm" className="gap-2" asChild>
            <Link href="/admin/bookings">
              <ArrowLeft className="h-4 w-4" />
              Back to Bookings
            </Link>
          </Button>
          {!bookingLoading && booking && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Booking ID:</span>
              <div className="flex items-center">
                <span className="font-mono text-sm">{booking.id}</span>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="ml-1 h-7 w-7 rounded-full hover:bg-primary/10" 
                  onClick={copyBookingIdToClipboard}
                  title="Copy Booking ID"
                >
                  <Copy className="h-3.5 w-3.5" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {bookingLoading ? (
          <LoadingBookingDetails />
        ) : booking ? (
          <>
            {/* Main content */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  Booking Details
                </h1>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-muted-foreground">
                    ID: <span className="print-full-width">{booking.id}</span>
                  </p>
                  {getStatusBadge(booking.status)}
                </div>
              </div>
              <div className="no-print">
                <BookingActions booking={booking} />
              </div>
            </div>
            
            <BookingSecondaryData 
              booking={booking} 
              bookingIssues={bookingIssues} 
              modificationRequests={modificationRequests}
            />
          </>
        ) : (
          <LoadingBookingDetails />
        )}
      </div>
    </div>
  )
} 