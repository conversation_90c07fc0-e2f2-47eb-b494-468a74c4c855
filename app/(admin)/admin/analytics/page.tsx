import type { Metada<PERSON> } from "next"
import { AdminAnalyticsDashboard } from "@/components/admin/analytics/admin-analytics-dashboard"
import { getAdminAnalytics } from "@/app/actions/admin-analytics"

export const metadata: Metadata = {
  title: "Analytics | Travella Admin",
  description: "Platform analytics and insights",
}

export default async function AdminAnalyticsPage() {
  const analyticsData = await getAdminAnalytics()

  return (
    <div className="flex flex-col gap-4">
      <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
      <p className="text-muted-foreground">Comprehensive insights into platform performance and user activity.</p>
      <AdminAnalyticsDashboard analyticsData={analyticsData} />
    </div>
  )
}

