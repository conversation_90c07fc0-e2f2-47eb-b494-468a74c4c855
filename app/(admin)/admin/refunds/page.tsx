import React from 'react';
import type { Metadata } from "next"
import { getBookingRefunds } from '@/app/actions/admin/refunds';
import { BookingRefundRequest, RefundStatus } from '@/types/refunds';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import RefundStatsCards from '@/app/components/admin/refunds/RefundStatsCards';
import RefundsClientContainer from '@/app/components/admin/refunds/RefundsClientContainer';
import { formatCurrency } from '@/lib/utils';
// Calculate stats from the refund data
function calculateStats(refunds: BookingRefundRequest[], count: number, page: number, itemsPerPage: number) {
  const pendingCount = refunds.filter(r => r.status === 'pending').length;
  const completedCount = refunds.filter(r => r.status === 'completed').length;
  const totalAmount = refunds.reduce((sum, r) => sum + (r.status === 'completed' ? r.amount : 0), 0);
  
  return {
    totalRefunds: count,
    pendingRefunds: pendingCount,
    completedRefunds: completedCount,
    totalAmount: formatCurrency(totalAmount),
  };
}

// Default server-side props for initial page load
const defaultParams = {
  page: 1,
  items_per_page: 10,
  status: undefined,
  user_name: undefined,
};

export const metadata: Metadata = {
  title: "Booking Refunds | Travella Admin",
  description: "Manage and review all booking refund requests",
}

export default async function RefundsPage({
  searchParams,
}: {
  searchParams?: {
    page?: string;
    items_per_page?: string;
    status?: RefundStatus;
    user_name?: string;
  };
}) {
  const filterParams = (await searchParams);

  // Parse and validate search params
  const page = Number(filterParams?.page) || defaultParams.page;
  const itemsPerPage = Number(filterParams?.items_per_page) || defaultParams.items_per_page;
  const status = filterParams?.status as RefundStatus | undefined;
  const userName = filterParams?.user_name || undefined;

  // Fetch refunds data server-side
  const { data, count, has_next, total_pages } = await getBookingRefunds({
    page,
    items_per_page: itemsPerPage,
    status,
    user_name: userName,
  });

  // Calculate stats
  const stats = calculateStats(data || [], count, page, itemsPerPage);

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Booking Refunds</h1>
        <p className="text-muted-foreground mt-1">Manage and review all booking refund requests</p>
      </div>

      {/* Stats Cards */}
      <RefundStatsCards 
        totalRefunds={stats.totalRefunds}
        pendingRefunds={stats.pendingRefunds}
        completedRefunds={stats.completedRefunds}
        totalAmount={stats.totalAmount}
      />

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-xl">Refund Requests</CardTitle>
          <CardDescription>
            View and manage refund requests from cancelled bookings based on cancellation policies
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Client-side container for interactive elements */}
          <RefundsClientContainer 
            initialRefunds={data || []}
            totalItems={count}
            currentPage={page}
            itemsPerPage={itemsPerPage}
            statusFilter={status}
            userNameFilter={userName || ''}
            hasMore={has_next}
            totalPages={total_pages}
          />
        </CardContent>
      </Card>
    </div>
  );
} 