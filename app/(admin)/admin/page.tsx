import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AdminOverview } from "@/components/admin/admin-overview"
import { RecentUsers } from "@/components/admin/recent-users"
import { Button } from "@/components/ui/button"
import { AlertTriangle, BarChart3, Car, CheckCircle, Users } from "lucide-react"
import { getAdminAnalytics } from "@/app/actions/admin/admin-actions"
import { SourceDistributionChart } from "@/components/admin/source-distribution-chart"
import { getDashboardStats, getPlatformOverview } from "@/app/actions/admin/dashboard"
export const metadata: Metadata = {
  title: "Admin Dashboard | Travella",
  description: "Admin dashboard for Travella",
}

export default async function AdminDashboardPage() {
  const [analyticsData, dashboardStats, platformOverview] = await Promise.all([
    getAdminAnalytics(),
    getDashboardStats(),
    getPlatformOverview()
  ])
  const { booking_completion_rate, categorized_bookings, hosts, revenue, active_car_listings, bookings } = dashboardStats;
  const { monthly_bookings, recent_activities, recent_users: users } = platformOverview;

  // Device distribution data
  const bookingStatusDistribution = [
    { name: 'Pending', value: categorized_bookings.pending, color: '#8b5cf6' }, // purple
    { name: 'Rejected', value: categorized_bookings.rejected, color: '#06b6d4' }, // cyan
    { name: 'Cancelled', value: categorized_bookings.cancelled, color: '#ff0000' }, // red
    { name: 'Completed', value: categorized_bookings.completed, color: '#10b981' }, // green
    { name: 'Confirmed', value: categorized_bookings.confirmed, color: '#10b981' }, // blue
    { name: 'In Progress', value: categorized_bookings.in_progress, color: '#f59e0b' } // yellow
  ];

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <p className="text-muted-foreground">Welcome to your admin dashboard. Here's an overview of your platform.</p>
      </div>

      {analyticsData.overview.pendingApprovals > 0 && (
        <Card className="bg-amber-50 border-amber-200 dark:bg-amber-950/20 dark:border-amber-800/30">
          <CardContent className="p-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-500" />
              <p className="text-amber-800 dark:text-amber-400">
                <span className="font-medium">{analyticsData.overview.pendingApprovals}</span> car listings pending approval
              </p>
            </div>
            <Button asChild variant="outline" className="border-amber-300 hover:bg-amber-100 dark:border-amber-700 dark:hover:bg-amber-900/30">
              <Link href="/admin/cars">Review Now</Link>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Booking Completion Status Card */}
      <Card className="bg-gradient-to-r from-primary/10 to-primary/5">
        <CardContent className="p-6">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold">Booking Completion Status</h3>
              <p className="text-muted-foreground text-sm">{booking_completion_rate}% of total bookings have been confirmed</p>
              <div className="mt-2">
                <Button size="sm" variant="link" className="p-0 h-auto text-primary" asChild>
                  <Link href="/admin/bookings">View details</Link>
                </Button>
              </div>
            </div>
            <div className="relative h-16 w-16">
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-sm">{booking_completion_rate}%</span>
              </div>
              <svg className="h-16 w-16 -rotate-90" viewBox="0 0 100 100">
                <circle 
                  cx="50" cy="50" r="40" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="10" 
                  className="text-primary/10" 
                />
                <circle 
                  cx="50" cy="50" r="40" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="10" 
                  className="text-primary" 
                  strokeDasharray={`${booking_completion_rate * 2.51} 251`} 
                />
              </svg>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 h-24 w-24 bg-primary/5 rounded-bl-full"></div>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-muted-foreground">Total Hosts</h3>
              <div className="p-2 bg-primary/10 rounded-full">
                <Users className="h-5 w-5 text-primary" />
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold">{hosts.count}</p>
              <div className="flex items-center text-xs">
                <span className="text-emerald-500 flex items-center">
                  <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                  </svg>
                  {hosts.growth}%
                </span>
                <span className="text-muted-foreground ml-1">vs last month</span>
              </div>
            </div>
            <div className="mt-4">
              <Link href="/admin/hosts" className="text-xs text-primary font-medium hover:underline">
                View Details →
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 h-24 w-24 bg-primary/5 rounded-bl-full"></div>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-muted-foreground">Active Listings</h3>
              <div className="p-2 bg-primary/10 rounded-full">
                <Car className="h-5 w-5 text-primary" />
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold">{active_car_listings.count || 0}</p>
              <div className="flex items-center text-xs">
                <span className="text-emerald-500 flex items-center">
                  <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                  </svg>
                  {active_car_listings.growth}%
                </span>
                <span className="text-muted-foreground ml-1">vs last month</span>
              </div>
            </div>
            <div className="mt-4">
              <Link href="/admin/cars" className="text-xs text-primary font-medium hover:underline">
                View Details →
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 h-24 w-24 bg-primary/5 rounded-bl-full"></div>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-muted-foreground">Total Bookings</h3>
              <div className="p-2 bg-primary/10 rounded-full">
                <CheckCircle className="h-5 w-5 text-primary" />
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold">{bookings.count}</p>
              <div className="flex items-center text-xs">
                <span className="text-emerald-500 flex items-center">
                  <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                  </svg>
                  {bookings.growth}%
                </span>
                <span className="text-muted-foreground ml-1">vs last month</span>
              </div>
            </div>
            <div className="mt-4">
              <Link href="/admin/bookings" className="text-xs text-primary font-medium hover:underline">
                View Details →
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 h-24 w-24 bg-primary/5 rounded-bl-full"></div>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-muted-foreground">Total Revenue</h3>
              <div className="p-2 bg-primary/10 rounded-full">
                <BarChart3 className="h-5 w-5 text-primary" />
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold">{formatNumber(revenue.total)}</p>
              <div className="flex items-center text-xs">
                <span className="text-emerald-500 flex items-center">
                  <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                  </svg>
                  {revenue.growth}%
                </span>
                <span className="text-muted-foreground ml-1">vs last month</span>
              </div>
            </div>
            <div className="mt-4">
              <Link href="/admin/analytics" className="text-xs text-primary font-medium hover:underline">
                View Details →
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Platform Stats by Device */}
      <div className="grid gap-6 md:grid-cols-7">
        <Card className="md:col-span-4 border-border/60">
          <CardHeader>
            <CardTitle>Platform Overview</CardTitle>
            <CardDescription>Monthly booking trends (values in thousands)</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <AdminOverview revenueData={monthly_bookings} />
          </CardContent>
        </Card>
        
        <div className="md:col-span-3 border-border/60">
          <SourceDistributionChart 
            title="This month bookings overview" 
            items={bookingStatusDistribution}
            total={bookings.count}
          />
        </div>
      </div>

      {/* Recent Users and Activity */}
      <div className="grid gap-6 md:grid-cols-7">
        <Card className="md:col-span-3 border-border/60">
          <CardHeader>
            <CardTitle>Recent Users</CardTitle>
            <CardDescription>New users who joined recently</CardDescription>
          </CardHeader>
          <CardContent>
            <RecentUsers users={users} />
          </CardContent>
        </Card>

        <Card className="md:col-span-4 border-border/60">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest platform activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-5">
              {recent_activities.map(({ id, user, type, created_at, formatted_date, time_ago }, index) => (
                <div key={id} className="flex items-start gap-4">
                  <div className={`h-2 w-2 mt-2 rounded-full ${
                    ['bg-blue-500', 'bg-green-500', 'bg-amber-500', 'bg-purple-500', 'bg-rose-500'][index % 5]
                  }`}></div>
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-medium">{user.name}</span>
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {type.replaceAll('_', ' ').toLowerCase()}
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {time_ago}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

function formatNumber(value: number) {
  return new Intl.NumberFormat("en-US", {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value)
}

