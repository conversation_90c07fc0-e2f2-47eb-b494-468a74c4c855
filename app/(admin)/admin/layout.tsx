import type React from "react"
import { AdminSidebar } from "@/components/admin/sidebar"
import { AdminHeader } from "@/components/admin/header"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { requireAuth } from "@/lib/user-server"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Admin Dashboard | Travella",
  description: "Admin dashboard for Travella",
}

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Get authenticated user data with centralized utility - require admin
  const userData = await requireAuth({ adminOnly: true });

  return (
    <div className="flex min-h-screen">
      <SidebarProvider>
        <AdminSidebar />
        <SidebarInset>
          <div className="flex flex-col min-h-screen">
            <AdminHeader user={userData.user as any} />
            <main className="flex-1 p-4 md:p-6 animate-in">{children}</main>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </div>
  )
}

