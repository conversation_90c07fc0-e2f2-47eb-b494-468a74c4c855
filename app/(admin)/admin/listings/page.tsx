"use client";

import React, { useState, useEffect } from "react";
import { 
  getCarFeatures, 
  getCancellationPolicies, 
  getCarBrands
} from "@/app/actions/admin/listings";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CarFeature, CancellationPolicy, CarBrand } from "@/types/listings";
// Import tab content components
import BrandsTabContent from "@/app/components/admin/listings/BrandsTabContent";
import FeaturesTabContent from "@/app/components/admin/listings/FeaturesTabContent";
import PoliciesTabContent from "@/app/components/admin/listings/PoliciesTabContent";

export default function ListingSettingsPage() {
  // State for data
  const [carBrands, setCarBrands] = useState<CarBrand[]>([]);
  const [carFeatures, setCarFeatures] = useState<CarFeature[]>([]);
  const [cancellationPolicies, setCancellationPolicies] = useState<CancellationPolicy[]>([]);
  
  // Loading state
  const [loading, setLoading] = useState(true);
  
  // State for tab-specific search terms
  const [brandSearchTerm, setBrandSearchTerm] = useState("");
  const [featureSearchTerm, setFeatureSearchTerm] = useState("");
  const [policySearchTerm, setPolicySearchTerm] = useState("");
  
  // State for pagination and infinite scroll
  const [brandPage, setBrandPage] = useState(1);
  const [featurePage, setFeaturePage] = useState(1);
  const [policyPage, setPolicyPage] = useState(1);
  
  // State for whether there are more items to load
  const [brandsHasMore, setBrandsHasMore] = useState(true);
  const [featuresHasMore, setFeaturesHasMore] = useState(true);
  const [policiesHasMore, setPoliciesHasMore] = useState(true);
  
  // State for total counts
  const [totalCounts, setTotalCounts] = useState({
    brands: 0,
    features: 0,
    policies: 0
  });
  
  // Items per page
  const itemsPerPage = 10;

  // Load initial data
  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const [brandsData, featuresData, policiesData] = await Promise.all([
          getCarBrands(),
          getCarFeatures(),
          getCancellationPolicies()
        ]);
        
        setCarBrands(brandsData.data);
        setCarFeatures(featuresData.data);
        setCancellationPolicies(policiesData.data);
        
        setTotalCounts({
          brands: brandsData.count,
          features: featuresData.count,
          policies: policiesData.count
        });
        
        setBrandsHasMore(brandsData.count > brandsData.data.length);
        setFeaturesHasMore(featuresData.count > featuresData.data.length);
        setPoliciesHasMore(policiesData.count > policiesData.data.length);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  // Filter function for search
  const filterItems = <T extends Record<string, any>>(items: T[], term: string): T[] => {
    if (!term) return items;
    const lowerTerm = term.toLowerCase().trim();
    
    return items.filter(item => {
      // Recursively check if any value in the object or nested objects includes the search term
      const containsValue = (obj: any): boolean => {
        if (!obj) return false;
        
        if (typeof obj === 'object') {
          // Skip checking the 'id' and date fields directly to avoid filtering on them
          return Object.entries(obj).some(([key, value]) => {
            // Skip id fields and date strings
            if (key === 'id' || key === 'created_at' || key === 'updated_at') {
              return false;
            }
            return containsValue(value);
          });
        }
        
        // Convert to string and check if it includes the search term
        return String(obj).toLowerCase().includes(lowerTerm);
      };
      
      return containsValue(item);
    });
  };

  // Load more items for brands
  const loadMoreBrands = async () => {
    const nextPage = brandPage + 1;
    setLoading(true);
    try {
      const brandsData = await getCarBrands(nextPage);
      setCarBrands(prev => [...prev, ...brandsData.data]);
      setBrandsHasMore(carBrands.length + brandsData.data.length < brandsData.count);
      setBrandPage(nextPage);
    } catch (error) {
      console.error(`Error loading more brands:`, error);
    } finally {
      setLoading(false);
    }
  };

  // Load more items for features
  const loadMoreFeatures = async () => {
    const nextPage = featurePage + 1;
    setLoading(true);
    try {
      const featuresData = await getCarFeatures(nextPage);
      setCarFeatures(prev => [...prev, ...featuresData.data]);
      setFeaturesHasMore(carFeatures.length + featuresData.data.length < featuresData.count);
      setFeaturePage(nextPage);
    } catch (error) {
      console.error(`Error loading more features:`, error);
    } finally {
      setLoading(false);
    }
  };

  // Load more items for policies
  const loadMorePolicies = async () => {
    const nextPage = policyPage + 1;
    setLoading(true);
    try {
      const policiesData = await getCancellationPolicies(nextPage);
      setCancellationPolicies(prev => [...prev, ...policiesData.data]);
      setPoliciesHasMore(cancellationPolicies.length + policiesData.data.length < policiesData.count);
      setPolicyPage(nextPage);
    } catch (error) {
      console.error(`Error loading more policies:`, error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex-col gap-4 container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Listing Settings</h1>
      
      <Tabs defaultValue="carBrands" className="w-full">
        <TabsList className="w-full mb-6">
          <TabsTrigger value="carBrands" className="flex-1">
            Car Brands
          </TabsTrigger>
          <TabsTrigger value="carFeatures" className="flex-1">
            Car Features
          </TabsTrigger>
          <TabsTrigger value="cancellationPolicies" className="flex-1">
            Cancellation Policies
          </TabsTrigger>
        </TabsList>
        
        <div className="border rounded-lg p-6" style={{ height: '600px' }}>
          {/* Car Brands Tab */}
          <TabsContent value="carBrands" className="h-full">
            <BrandsTabContent 
              brands={carBrands}
              loading={loading}
              searchTerm={brandSearchTerm}
              setSearchTerm={setBrandSearchTerm}
              hasMore={brandsHasMore}
              loadMoreItems={loadMoreBrands}
              filterItems={filterItems}
            />
          </TabsContent>

          {/* Car Features Tab */}
          <TabsContent value="carFeatures" className="h-full">
            <FeaturesTabContent 
              features={carFeatures}
              loading={loading}
              searchTerm={featureSearchTerm}
              setSearchTerm={setFeatureSearchTerm}
              hasMore={featuresHasMore}
              loadMoreItems={loadMoreFeatures}
              filterItems={filterItems}
            />
          </TabsContent>

          {/* Cancellation Policies Tab */}
          <TabsContent value="cancellationPolicies" className="h-full">
            <PoliciesTabContent 
              policies={cancellationPolicies}
              loading={loading}
              searchTerm={policySearchTerm}
              setSearchTerm={setPolicySearchTerm}
              hasMore={policiesHasMore}
              loadMoreItems={loadMorePolicies}
              filterItems={filterItems}
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
