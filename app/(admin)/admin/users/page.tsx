import type { Metadata } from "next"
import { getAdminUsers } from "@/app/actions/admin/admin-actions"
import { UserManagementTable } from "@/components/admin/users/user-management-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { UserRole } from "@/types/users"
import { UserFilters } from "@/components/admin/users/user-filters"

export const metadata: Metadata = {
  title: "User Management | Travella Admin",
  description: "Manage users on the Travella platform",
}

interface AdminUsersPageProps {
  searchParams: {
    page?: string
    limit?: string
    search?: string
    role?: UserRole
  }
}

export default async function AdminUsersPage({ searchParams }: AdminUsersPageProps) {
  // Parse query parameters
  const page = searchParams?.page ? parseInt(searchParams.page) : 1
  const limit = searchParams?.limit ? parseInt(searchParams.limit) : 10
  const search = searchParams?.search
  const role = searchParams?.role
  
  // Get users with pagination and filters
  const { data, count, has_next, items_per_page, total_pages } = await getAdminUsers({
    page,
    limit,
    search,
    role
  })

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">{count} Platform Users</h1>
        <p className="text-muted-foreground">Manage platform users</p>
      </div>

      <Card className="border-border/60 shadow-sm">
        <CardHeader className="pb-3">
          
          {/* Add filters component */}
          <UserFilters currentSearch={search} currentRole={role} />
        </CardHeader>
        <CardContent>
          <UserManagementTable 
            users={data} 
            pagination={{
              currentPage: page,
              totalPages: total_pages,
              hasNextPage: has_next,
              totalItems: count,
              itemsPerPage: items_per_page
            }}
            searchParams={{
              search,
              role,
              limit: limit.toString()
            }}
          />
        </CardContent>
      </Card>
    </div>
  )
}

