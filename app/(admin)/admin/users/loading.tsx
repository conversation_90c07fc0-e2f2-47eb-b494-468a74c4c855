import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { UsersTableSkeleton } from "@/components/admin/users/users-table-skeleton"

export default function Loading() {
  return (
    <div className="flex-1 space-y-6">
      <div className="flex flex-col gap-2">
        <Skeleton className="h-10 w-[300px]" />
        <Skeleton className="h-5 w-[400px]" />
      </div>

      <Card>
        <CardHeader className="pb-3">
          <Skeleton className="h-6 w-[200px] mb-2" />
          <Skeleton className="h-4 w-[300px]" />
        </CardHeader>
        <CardContent>
          <UsersTableSkeleton />
        </CardContent>
      </Card>
    </div>
  )
}
