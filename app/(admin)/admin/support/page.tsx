"use client"

import { useState, useEffect, useTransition, useCallback } from "react";
import { format } from "date-fns";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Search,
  RotateCcw,
  MessageSquare,
  AlertTriangle, 
  X,
  ChevronLeft
} from "lucide-react";
import { 
  getSupportTicketsSummary,
} from "@/app/actions/admin/admin-actions";
import { SupportTicket } from "@/types/support-tickets";
import { SupportTicketChat } from "@/components/admin/SupportTicketChat";
import { createClient } from "@/lib/supabase-client";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast"

export default function SupportTicketsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();

  // State for support tickets
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<SupportTicket[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  
  // Replace dropdown filters with tab selection
  const [activeTab, setActiveTab] = useState<"open" | "closed">("open");
  const [searchTerm, setSearchTerm] = useState("");

  // State for selected ticket
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [isMobileView, setIsMobileView] = useState(false);

  // Get current user
  const [currentUserId, setCurrentUserId] = useState<string>("");
  
  // Check for mobile view
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 1024);
    };
    
    checkMobileView();
    window.addEventListener('resize', checkMobileView);
    
    return () => {
      window.removeEventListener('resize', checkMobileView);
    };
  }, []);
  
  // Initialize from URL if ticket ID is present
  useEffect(() => {
    const ticketId = searchParams.get('ticketId');
    if (ticketId && tickets.length > 0) {
      const ticket = tickets.find(t => t.id === ticketId);
      if (ticket) {
        setSelectedTicket(ticket);
        // Set the appropriate tab based on the ticket status
        setActiveTab(ticket.status === 'closed' ? 'closed' : 'open');
      }
    }
  }, [searchParams, tickets]);
  
  useEffect(() => {
    // Get current user ID
    const getUserId = async () => {
      try {
        const supabase = createClient();
        const { data } = await supabase.auth.getUser();
        if (data?.user) {
          setCurrentUserId(data.user.id);
        }
      } catch (error) {
        console.error("Error getting user:", error);
      }
    };
    
    getUserId();
  }, []);

  // Fetch all support tickets
  const fetchTickets = useCallback(async () => {
    try {
      setIsLoading(true);
      setLoadError(null);
      const data = await getSupportTicketsSummary();
      setTickets(data);
      
      // If there's a ticket ID in the URL, select that ticket
      const ticketId = searchParams.get('ticketId');
      if (ticketId) {
        const ticket = data.find(t => t.id === ticketId);
        if (ticket) {
          setSelectedTicket(ticket);
          // Set the appropriate tab based on the ticket status
          setActiveTab(ticket.status === 'closed' ? 'closed' : 'open');
        }
      }
    } catch (error) {
      console.error("Error fetching support tickets:", error);
      setLoadError("Failed to load support tickets. Please try again.");
      toast({
        title: "Error",
        description: "Failed to load support tickets",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast, searchParams]);

  // Initial fetch of tickets
  useEffect(() => {
    fetchTickets();
  }, [fetchTickets]);

  // Set up real-time listener for ticket updates
  useEffect(() => {
    const supabase = createClient();
    
    // Listen for new support tickets
    const newTicketsSubscription = supabase
      .channel('new-support-tickets')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'support_tickets',
      }, () => {
        // Refresh the tickets list
        fetchTickets();
        
        // Show a notification
        toast({
          title: "New Support Ticket",
          description: "A new support ticket has been created",
        });
      })
      .subscribe();
    
    // Listen for status updates
    const ticketUpdatesSubscription = supabase
      .channel('ticket-updates')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'support_tickets',
      }, () => {
        // Refresh the tickets list
        fetchTickets();
      })
      .subscribe();

    // Cleanup
    return () => {
      newTicketsSubscription.unsubscribe();
      ticketUpdatesSubscription.unsubscribe();
    };
  }, [fetchTickets, toast]);

  // Filter tickets based on tab and search term
  useEffect(() => {
    if (tickets.length === 0) return;

    let result = [...tickets];

    // Apply tab filter
    if (activeTab === "closed") {
      result = result.filter(ticket => ticket.status === "closed");
    } else {
      result = result.filter(ticket => ticket.status !== "closed");
    }

    // Apply search term
    if (searchTerm.trim() !== "") {
      const term = searchTerm.toLowerCase();
      result = result.filter(
        ticket =>
          ticket.reporter_first_name.toLowerCase().includes(term) ||
          ticket.reporter_last_name.toLowerCase().includes(term) ||
          ticket.description.toLowerCase().includes(term) ||
          ticket.brand.toLowerCase().includes(term) ||
          ticket.model.toLowerCase().includes(term) ||
          ticket.id.toLowerCase().includes(term)
      );
    }

    setFilteredTickets(result);
  }, [activeTab, searchTerm, tickets]);

  // Handle opening a ticket
  const handleOpenTicket = useCallback((ticket: SupportTicket) => {
    setSelectedTicket(ticket);
    // Update URL with ticket ID for shareable links
    router.push(`/admin/support?ticketId=${ticket.id}`, { scroll: false });
  }, [router]);

  // Close the ticket view
  const handleCloseTicket = useCallback(() => {
    setSelectedTicket(null);
    router.push('/admin/support', { scroll: false });
  }, [router]);

  // Handle ticket status change from the chat component
  const handleTicketStatusChange = useCallback((ticketId: string, status: 'open' | 'in_progress' | 'resolved' | 'closed') => {
    // Update the ticket in the local state
    setTickets(prev => 
      prev.map(ticket => 
        ticket.id === ticketId ? {...ticket, status} : ticket
      )
    );
    
    // Update selected ticket if it's the one being modified
    setSelectedTicket(prev => {
      if (prev?.id === ticketId) {
        return { ...prev, status };
      }
      return prev;
    });
    
    // If ticket is now closed and we're on the open tab, or vice versa, adjust the view
    // (We're intentionally not changing the tab to reduce unnecessary re-renders)
  }, []);

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "open":
        return <Badge className="bg-blue-500 text-white">Open</Badge>;
      case "in_progress":
        return <Badge className="bg-yellow-500 text-white">In Progress</Badge>;
      case "resolved":
        return <Badge className="bg-green-500 text-white">Resolved</Badge>;
      case "closed":
        return <Badge variant="outline" className="text-slate-500 border-slate-500">Closed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  // Get type badge
  const getTypeBadge = (type: string) => {
    switch (type) {
      case "technical":
        return <Badge variant="outline" className="text-purple-600 border-purple-600">Technical</Badge>;
      case "billing":
        return <Badge variant="outline" className="text-orange-600 border-orange-600">Billing</Badge>;
      case "account":
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Account</Badge>;
      default:
        return <Badge variant="outline">Other</Badge>;
    }
  };

  // Ticket Item Component
  const TicketItem = ({ ticket }: { ticket: SupportTicket }) => (
    <div 
      className={cn(
        "p-4 border-b cursor-pointer hover:bg-muted/50 transition-colors",
        selectedTicket?.id === ticket.id ? "bg-muted" : ""
      )}
      onClick={() => handleOpenTicket(ticket)}
    >
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-3">
          {ticket.reporter_avatar ? (
            <Image 
              src={ticket.reporter_avatar} 
              alt={ticket.reporter_first_name}
              width={40} 
              height={40}
              className="rounded-full" 
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center text-base font-medium">
              {ticket.reporter_first_name[0]?.toUpperCase() || '?'}
            </div>
          )}
          <div>
            <div className="font-medium">{ticket.reporter_first_name} {ticket.reporter_last_name}</div>
            <div className="text-sm text-muted-foreground">{ticket.brand} {ticket.model}</div>
          </div>
        </div>
        
        <div className="text-right">
          <div className="flex items-center justify-end gap-2 text-xs text-muted-foreground mb-1">
            {format(new Date(ticket.created_at), "MMM d, h:mm a")}
          </div>
          <div className="flex gap-2">
            {getStatusBadge(ticket.status)}
            {getTypeBadge(ticket.type)}
          </div>
        </div>
      </div>
      
      <div className="mt-2 text-sm line-clamp-2 text-muted-foreground">
        {ticket.description}
      </div>
      
      <div className="mt-2 flex justify-between items-center">
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <MessageSquare className="h-3.5 w-3.5" />
          <span>{ticket.message_count} messages</span>
        </div>
        
        {ticket.unread_admin_messages_count > 0 && (
          <Badge className="bg-red-500 text-white">
            {ticket.unread_admin_messages_count} unread
          </Badge>
        )}
      </div>
    </div>
  );

  // Count tickets for tabs
  const openTicketsCount = tickets.filter(ticket => ticket.status !== "closed").length;
  const closedTicketsCount = tickets.filter(ticket => ticket.status === "closed").length;

  return (
    <div className="h-[calc(100vh-60px)] flex flex-col">
      {/* Main layout - split view */}
      <div className="flex flex-1 h-full overflow-hidden">
        {/* Left sidebar - tickets list */}
        <div 
          className={cn(
            "h-full flex flex-col border-r overflow-hidden transition-all duration-300",
            isMobileView && selectedTicket ? "w-0" : "w-full lg:w-1/3 xl:w-2/5"
          )}
        >
          <div className="p-6 border-b">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Support Tickets</h1>
              <Button 
                variant="outline" 
                size="sm"
                onClick={fetchTickets}
                disabled={isLoading}
              >
                <RotateCcw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
            
            {/* Search box */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tickets..."
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1 h-8 w-8"
                  onClick={() => setSearchTerm("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            
            {/* Tab-based filtering */}
            <Tabs 
              value={activeTab} 
              onValueChange={(value) => setActiveTab(value as "open" | "closed")}
              className="w-full"
            >
              <TabsList className="w-full">
                <TabsTrigger value="open" className="flex-1">
                  Open ({openTicketsCount})
                </TabsTrigger>
                <TabsTrigger value="closed" className="flex-1">
                  Closed ({closedTicketsCount})
                </TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="mt-4 text-sm text-muted-foreground">
              {filteredTickets.length} {filteredTickets.length === 1 ? 'ticket' : 'tickets'} found
            </div>
          </div>
          
          {/* Tickets list */}
          <div className="flex-1 overflow-auto bg-card/40">
            {loadError && (
              <div className="flex items-center justify-center p-6 text-red-500 gap-2">
                <AlertTriangle className="h-5 w-5" />
                <p>{loadError}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="ml-4"
                  onClick={fetchTickets}
                >
                  Retry
                </Button>
              </div>
            )}
            
            {isLoading ? (
              // Loading skeletons
              Array(5).fill(0).map((_, i) => (
                <div key={`skeleton-${i}`} className="p-4 border-b">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div>
                        <Skeleton className="h-5 w-32 mb-1" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                    </div>
                    <div className="text-right">
                      <Skeleton className="h-4 w-20 mb-1 ml-auto" />
                      <div className="flex gap-2 justify-end">
                        <Skeleton className="h-6 w-16" />
                        <Skeleton className="h-6 w-20" />
                      </div>
                    </div>
                  </div>
                  <Skeleton className="h-4 w-full mt-2" />
                  <Skeleton className="h-4 w-3/4 mt-1" />
                  <div className="flex justify-between items-center mt-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-5 w-12" />
                  </div>
                </div>
              ))
            ) : filteredTickets.length === 0 ? (
              <div className="p-6 text-center text-muted-foreground">
                {tickets.length === 0 
                  ? "No support tickets found. Waiting for users to create tickets." 
                  : activeTab === "open"
                    ? "No open tickets found. All tickets may be closed." 
                    : "No closed tickets found."}
              </div>
            ) : (
              // Ticket list
              filteredTickets.map((ticket) => (
                <TicketItem key={ticket.id} ticket={ticket} />
              ))
            )}
          </div>
        </div>
        
        {/* Right content area - ticket conversation */}
        <div 
          className={cn(
            "h-full flex flex-col bg-background overflow-hidden transition-all duration-300",
            !selectedTicket ? "w-0" : (isMobileView ? "w-full" : "w-2/3 xl:w-3/5")
          )}
        >
          {selectedTicket ? (
            <>
              {isMobileView && (
                <div className="p-4 border-b flex items-center">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={handleCloseTicket}
                    className="mr-2"
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Back
                  </Button>
                  <h2 className="text-lg font-semibold">
                    Ticket #{selectedTicket.id.substring(0, 8)}
                  </h2>
                </div>
              )}
              
              <SupportTicketChat 
                ticket={selectedTicket}
                isOpen={true}
                onOpenChange={(open) => !open && handleCloseTicket()}
                onStatusChange={handleTicketStatusChange}
                currentUserId={currentUserId}
                inlineMode={true}
              />
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground p-6">
              <div className="bg-muted/50 p-6 rounded-lg text-center max-w-md">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">No ticket selected</h3>
                <p>Select a ticket from the list to view the conversation and reply to the customer.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 