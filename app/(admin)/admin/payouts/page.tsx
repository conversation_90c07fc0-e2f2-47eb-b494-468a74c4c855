"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import PayoutsTab from "@/components/admin/payouts/payouts-tab"
// import TransfersTab from "@/components/admin/payouts/transfers-tab"

export default function PayoutsPage() {
  const [activeTab, setActiveTab] = useState("payouts")

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Host Payments</h1>
      
      <Tabs defaultValue="payouts" onValueChange={setActiveTab} value={activeTab}>
        <TabsList className="grid w-[400px] grid-cols-2">
          <TabsTrigger value="payouts">Payouts</TabsTrigger>
          <TabsTrigger value="transfers">Transfers</TabsTrigger>
        </TabsList>
        
        <TabsContent value="payouts" className="mt-6">
          <PayoutsTab />
        </TabsContent>
        
        <TabsContent value="transfers" className="mt-6">
          {/* <TransfersTab /> */}
        </TabsContent>
      </Tabs>
    </div>
  )
} 