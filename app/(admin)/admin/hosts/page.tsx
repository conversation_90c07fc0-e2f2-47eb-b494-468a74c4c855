"use client"

import { useEffect, useState, useRef, useCallback } from "react"
import { getHosts } from "@/app/actions/admin/hosts"
import { Host } from "@/types/hosts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Search, CreditCard, Eye } from "lucide-react"
import { formatDate } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import Image from "next/image"
import debounce from "lodash-es/debounce"
import HostPaymentDetailsModal from "@/components/admin/hosts/host-payment-details-modal"

export default function HostsPage() {
  const [hosts, setHosts] = useState<Host[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const tableContainerRef = useRef<HTMLDivElement>(null)
  const [page, setPage] = useState(0)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [totalHosts, setTotalHosts] = useState(0)
  const [selectedHostId, setSelectedHostId] = useState<string | null>(null)
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)

  const fetchHosts = useCallback(async () => {
    try {
      setLoading(true)
      const { data, hasNextPage, count } = await getHosts({
        itemsPerPage: 20,
        page,
        searchTerm,
        sortBy: "",
        sortOrder: ""
      })
      
      setHosts(data)
      setHasNextPage((prev) => prev || hasNextPage)
      setTotalHosts(count)
    } catch (error) {
      console.error("Failed to fetch hosts:", error)
    } finally {
      setLoading(false)
    }
  }, [page])

  useEffect(() => {
    fetchHosts()
  }, [fetchHosts])

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (tableContainerRef.current) {
      const { scrollTop, clientHeight, scrollHeight } = e.currentTarget
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        if (hasNextPage) {
          setPage(page + 1)
        }
      }
    }
  }

  useEffect(() => debounce(() => {
    try {
      setPage(0)
      setHosts([])
      fetchHosts()
    } catch (error) {
      console.error("Failed to search hosts:", error)
    }
  }, 500), [searchTerm])

  const openPaymentDetails = (hostId: string) => {
    setSelectedHostId(hostId)
    setIsPaymentModalOpen(true)
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Host Management</h1>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{totalHosts} Hosts</CardTitle>
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search hosts..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </CardHeader>
        <CardContent>
          <div 
            ref={tableContainerRef} 
            className="border rounded-md overflow-auto" 
            style={{ height: "calc(100vh - 300px)" }}
            onScroll={handleScroll}
          >
            <Table>
              <TableHeader className="sticky top-0 bg-card z-10">
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Contact Number</TableHead>
                  <TableHead>Country</TableHead>
                  <TableHead>Cars</TableHead>
                  <TableHead>Trips</TableHead>
                  <TableHead>Avg Rating</TableHead>
                  <TableHead>Joined</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array(10).fill(0).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-48" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                    </TableRow>
                  ))
                ) : hosts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      {searchTerm ? "No hosts found matching your search" : "No hosts found"}
                    </TableCell>
                  </TableRow>
                ) : (
                  hosts.map((host) => (
                    <TableRow key={host.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          {host.avatar_url && (
                            <Image 
                              src={host.avatar_url} 
                              alt={host.first_name || ""} 
                              width={32} 
                              height={32} 
                              className="rounded-full"
                            />
                          )}
                          <div>
                            <div>{host.first_name || ""}</div>
                            <div className="text-muted-foreground text-xs">{host.last_name || ""}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{host.phone}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {host.country_details && (
                            <Image 
                              src={host.country_details.flag_image} 
                              alt={host.country_details.name || ""} 
                              width={30} 
                              height={20} 
                              className="rounded-full"
                            />
                          )}
                          <div>
                            <div>{host.country_details.name || ""}</div>
                            <div className="text-muted-foreground text-xs">{host.country_details.code || ""}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{host.cars}</TableCell>
                      <TableCell>{host.trips}</TableCell>
                      <TableCell>{host.avg_rating}</TableCell>
                      <TableCell>{formatDate(host.created_at)}</TableCell>
                      <TableCell>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => openPaymentDetails(host.id)}
                        >
                          <CreditCard className="h-4 w-4 mr-1" />
                          Payments
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Host Payment Details Modal */}
      <HostPaymentDetailsModal
        isOpen={isPaymentModalOpen}
        onOpenChange={setIsPaymentModalOpen}
        hostId={selectedHostId}
      />
    </div>
  )
}
