@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --primary-50: 72 89% 97%;
    --primary-100: 72 89% 90%;
    --primary-200: 72 89% 80%;
    --primary-700: 72 89% 40%;
    --primary-800: 72 89% 35%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --secondary-50: 48 100% 96%;
    --secondary-100: 48 96% 89%;
    --secondary-600: 35 91% 44%;
    --secondary-700: 32 94% 38%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --success: 160 84% 39%;
    --success-foreground: 210 40% 98%;
    --info: 217 91% 60%;
    --info-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    /* Sidebar colors */
    --sidebar: 210 40% 98%;
    --sidebar-foreground: 215 25% 27%;
    --sidebar-border: 214 32% 91%;
    --sidebar-accent: 214 32% 91%;
    --sidebar-accent-foreground: 215 25% 27%;
    --sidebar-ring: 72 89% 47%;

    /* Border radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --primary-50: 72 89% 97%;
    --primary-100: 72 89% 90%;
    --primary-200: 72 89% 80%;
    --primary-700: 72 89% 40%;
    --primary-800: 72 89% 35%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --secondary-50: 48 100% 96%;
    --secondary-100: 48 96% 89%;
    --secondary-600: 35 91% 44%;
    --secondary-700: 32 94% 38%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --success: 160 84% 39%;
    --success-foreground: 210 40% 98%;
    --info: 217 91% 60%;
    --info-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Sidebar colors */
    --sidebar: 222 47% 11%;
    --sidebar-foreground: 214 32% 91%;
    --sidebar-border: 217 33% 17%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 214 32% 91%;
    --sidebar-ring: 72 89% 47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    color: #000;
    font-weight: 400;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
  }
  h1 {
    @apply text-3xl md:text-4xl;
  }
  h2 {
    @apply text-2xl md:text-3xl;
  }
  h3 {
    @apply text-xl md:text-2xl;
  }
  h4 {
    @apply text-lg md:text-xl;
  }
  h5 {
    @apply text-base md:text-lg;
  }
  h6 {
    @apply text-sm md:text-base;
  }
}

@layer components {
  .card-hover {
    @apply transition-all duration-200 hover:shadow-md hover:border-primary/50;
  }

  .btn-hover {
    @apply transition-all duration-200 hover:shadow-sm;
  }

  .nav-link {
    @apply transition-colors duration-200 hover:text-primary;
  }

  .form-input {
    @apply rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  .badge-outline {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-solid {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .section-padding {
    @apply py-8 md:py-12 lg:py-16;
  }

  .container-padding {
    @apply px-4 md:px-6 lg:px-8;
  }

  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  .animate-in {
    @apply animate-fade-in;
  }

  .animate-out {
    @apply animate-fade-out;
  }

  @media print {
    /* Hide elements that shouldn't be printed */
    nav, 
    button,
    .no-print,
    [data-state="open"] > [role="dialog"] {
      display: none !important;
    }
    
    /* Special handling for components that should be simplified in print mode */
    .print-detailed {
      max-width: 100% !important;
      page-break-inside: avoid;
    }
    
    /* Print-detailed components should show expanded content */
    .print-detailed [data-state="closed"] > [data-state] {
      display: block !important;
    }
    
    /* Hide accordion triggers in print */
    .print-detailed button[data-state] {
      display: none !important;
    }
    
    /* Make accordion content visible */
    .print-detailed [data-state="closed"] > [role="region"] {
      display: block !important;
      height: auto !important;
      overflow: visible !important;
    }
    
    /* Remove background colors and shadows */
    * {
      box-shadow: none !important;
      background-color: white !important;
      color: black !important;
    }
    
    /* Main content layout */
    .container {
      max-width: 100% !important;
      padding: 0 !important;
      margin: 0 !important;
    }
    
    /* Better table printing */
    table {
      width: 100% !important;
      border-collapse: collapse !important;
    }
    
    th, td {
      border: 1px solid #ddd !important;
      padding: 0.25rem 0.5rem !important;
      text-align: left !important;
      font-size: 0.85rem !important;
    }
    
    th {
      font-weight: bold !important;
      background-color: #f5f5f5 !important;
    }
    
    /* Ensure badges are visible in print */
    .badge, [class*="Badge"] {
      border: 1px solid #000 !important;
      padding: 0.1rem 0.3rem !important;
      border-radius: 0.25rem !important;
      font-size: 0.7rem !important;
      font-weight: bold !important;
      display: inline-block !important;
    }
    
    /* Add page break for sections */
    .page-break-after {
      page-break-after: always;
    }
    
    /* Expand the grid for better print layout */
    .grid {
      display: block !important;
    }
    
    .grid > * {
      margin-bottom: 1rem !important;
      width: 100% !important;
    }
    
    /* Show full booking ID and other important details */
    .print-full-width {
      width: 100% !important;
    }
    
    /* Add a heading for the printed document */
    body::before {
      content: "Booking Details";
      display: block;
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 1rem;
      text-align: center;
    }
    
    /* Use more space-efficient typography */
    p, h1, h2, h3, h4, span {
      margin: 0 0 0.25rem 0 !important;
    }

    /* Add the date to printed pages */
    @page {
      margin: 2cm;
      @top-right {
        content: "Printed on " attr(date);
      }
      @bottom-center {
        content: "Page " counter(page) " of " counter(pages);
      }
    }
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted/50;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full transition-colors hover:bg-muted-foreground/50;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion) {
  html {
    scroll-behavior: auto;
  }

  *,
  ::before,
  ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles for keyboard navigation */
:focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2;
}

/* Improved touch targets for mobile */
@media (max-width: 640px) {
  button,
  a,
  input,
  select,
  textarea {
    @apply min-h-[44px] min-w-[44px];
  }
}

