import type { Metadata } from "next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card-new"
import { Overview } from "@/components/dashboard/overview-new"
import { RecentBookings } from "@/components/dashboard/recent-bookings-new"
import { DashboardStats } from "@/components/dashboard/dashboard-stats-new"
import { createClient } from "@/lib/supabase-server"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert-new"
import { Info } from "lucide-react"

export const metadata: Metadata = {
  title: "Dashboard | Travella",
  description: "Manage your car listings and bookings",
}

export default async function DashboardPage() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return (
      <Alert variant="destructive">
        <Info className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>You must be logged in to view this page.</AlertDescription>
      </Alert>
    )
  }

  // Get car count
  const { count: carCount } = await supabase
    .from("car_listings")
    .select("*", { count: "exact", head: true })
    .eq("owner_id", user.id)

  // Get active car count
  const { count: activeCarCount } = await supabase
    .from("car_listings")
    .select("*", { count: "exact", head: true })
    .eq("owner_id", user.id)

  // Get booking count
  const { count: bookingCount } = await supabase
    .from("bookings")
    .select("bookings.id, car_listings!inner(renter_id)", { count: "exact", head: true })
    .eq("car_listings.owner_id", user.id)

  // Get total revenue
  const { data: transactions } = await supabase
    .from("host_earnings_summary")
    .select("total_earnings")
    .eq("user_id", user.id)

  const totalRevenue = transactions?.reduce((sum, transaction) => sum + (transaction.total_earnings ?? 0), 0) || 0
  // Get recent bookings
  const { data: recentBookings } = await supabase
    .from("bookings")
    .select(`
      *,
      car_listings!inner (
        owner_id,
        title,
        make,
        model
      ),
      profiles!bookings_renter_id_fkey (
        full_name,
        email,
        avatar_url
      )
    `)
    .eq("car_listings.owner_id", user.id)
    .order("created_at", { ascending: false })
    .limit(4)

  // Get monthly revenue data for chart
  const { data: monthlyRevenue } = await supabase
    .from("host_earnings_summary")
    .select("total_earnings, created_at")
    .eq("host_id", user.id)

  // Process monthly revenue data for the chart
  const monthlyData = Array(12)
    .fill(0)
    .map((_, i) => ({
      name: new Date(0, i).toLocaleString("default", { month: "short" }),
      revenue: 0,
      bookings: 0,
    }))

  if (transactions) {
    transactions.forEach((transaction) => {
      const date = new Date(transaction.created_at)
      const monthIndex = date.getMonth()
      monthlyData[monthIndex].revenue += transaction.total_earnings || 0
    })
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">Welcome back! Here's an overview of your car rental business.</p>
      </div>

      {carCount === 0 && (
        <Alert variant="info" className="animate-in" icon={<Info className="h-4 w-4" />}>
          <AlertTitle>Get started with Travella</AlertTitle>
          <AlertDescription>
            You haven't listed any cars yet. Add your first car to start receiving bookings.
          </AlertDescription>
        </Alert>
      )}

      <DashboardStats
        carCount={carCount || 0}
        activeCarCount={activeCarCount || 0}
        bookingCount={bookingCount || 0}
        totalRevenue={totalRevenue}
      />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4" hover>
          <CardHeader>
            <CardTitle>Revenue Overview</CardTitle>
            <CardDescription>View your booking and revenue statistics</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <Overview data={monthlyData} />
          </CardContent>
        </Card>
        <Card className="col-span-3" hover>
          <CardHeader>
            <CardTitle>Recent Bookings</CardTitle>
            <CardDescription>Your most recent booking requests</CardDescription>
          </CardHeader>
          <CardContent>
            <RecentBookings bookings={recentBookings || []} />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

