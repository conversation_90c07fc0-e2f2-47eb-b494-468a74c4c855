import { notFound } from "next/navigation"
import type { Metadata } from "next"
import { PerformanceMetrics } from "@/components/dashboard/analytics/performance-metrics"
import { getCarPerformanceMetrics } from "@/app/actions/analytics-actions"

interface CarPerformancePageProps {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: CarPerformancePageProps): Promise<Metadata> {
  try {
    const { carDetails } = await getCarPerformanceMetrics(params.id)
    return {
      title: `${carDetails.title} Performance | Travella`,
      description: `Performance metrics for ${carDetails.title}`,
    }
  } catch (error) {
    return {
      title: "Car Performance | Travella",
      description: "Performance metrics for your car",
    }
  }
}

export default async function CarPerformancePage({ params }: CarPerformancePageProps) {
  try {
    const metrics = await getCarPerformanceMetrics(params.id)

    return (
      <div className="flex flex-col gap-4">
        <h1 className="text-3xl font-bold">{metrics.carDetails.title} Performance</h1>
        <p className="text-muted-foreground">
          {metrics.carDetails.make} {metrics.carDetails.model} ({metrics.carDetails.year})
        </p>
        <PerformanceMetrics carId={params.id} metrics={metrics} />
      </div>
    )
  } catch (error) {
    notFound()
  }
}

