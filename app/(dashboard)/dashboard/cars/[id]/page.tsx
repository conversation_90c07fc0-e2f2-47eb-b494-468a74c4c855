import { notFound } from "next/navigation"
import type { Metadata } from "next"
import { createClient } from "@/lib/supabase-server"
import { CarForm } from "@/components/dashboard/car-form"

interface CarDetailPageProps {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: CarDetailPageProps): Promise<Metadata> {
  const supabase = createClient()

  const { data: car } = await supabase.from("cars").select("title").eq("id", params.id).single()

  return {
    title: car ? `Edit ${car.title} | Travella` : "Edit Car | Travella",
    description: "Edit your car listing",
  }
}

export default async function CarDetailPage({ params }: CarDetailPageProps) {
 const supabase = await createClient()
 c

  const { data: car, error } = await supabase
    .from("cars")
    .select(`
      *,
      car_features (*),
      car_images (*)
    `)
    .eq("id", params.id)
    .single()

  if (error || !car) {
    notFound()
  }

  // Check if the user owns this car
  if (car.owner_id !== session?.user.id) {
    notFound()
  }

  return (
    <div className="flex flex-col gap-4">
      <h1 className="text-3xl font-bold">Edit Car</h1>
      <CarForm car={car} />
    </div>
  )
}

