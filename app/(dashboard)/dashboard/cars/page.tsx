import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { Plus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { CarListings } from "@/components/dashboard/car-listings"
import { createClient } from "@/lib/supabase-server"
import { redirect } from "next/navigation"

export const metadata: Metadata = {
  title: "My Cars | Travella",
  description: "Manage your car listings",
}

export default async function CarsPage() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()
  
  // If no user is authenticated, redirect to login
  if (!user) {
    redirect("/login")
  }

  const { data: cars, error } = await supabase
    .from("cars")
    .select(`
      *,
      car_features (*),
      car_images (*)
    `)
    .eq("owner_id", user.id)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching cars:", error)
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">My Cars</h1>
        <Button asChild>
          <Link href="/dashboard/cars/new">
            <Plus className="mr-2 h-4 w-4" />
            Add New Car
          </Link>
        </Button>
      </div>
      <CarListings cars={cars || []} />
    </div>
  )
}

