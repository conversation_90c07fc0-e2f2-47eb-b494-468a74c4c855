import type { Metadata } from "next"
import { EnhancedBookingTable } from "@/components/dashboard/bookings/enhanced-booking-table"
import { createClient } from "@/lib/supabase-server"

export const metadata: Metadata = {
  title: "Bookings | Travella",
  description: "Manage your bookings",
}

export default async function BookingsPage() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  const { data: bookings, error } = await supabase
    .from("bookings")
    .select(`
      *,
      cars!bookings_car_id_fkey (
        id,
        owner_id,
        title,
        make,
        model,
        year,
        price
      ),
      profiles!bookings_renter_id_fkey (
        id,
        full_name,
        email,
        avatar_url,
        phone
      )
    `)
    .eq("cars.owner_id", user?.id)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching bookings:", error)
  }

  return (
    <div className="flex flex-col gap-4">
      <h1 className="text-3xl font-bold">Bookings</h1>
      <EnhancedBookingTable bookings={bookings || []} />
    </div>
  )
}

