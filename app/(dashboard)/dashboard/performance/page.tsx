import type { Metadata } from "next"
import { PerformanceMetrics } from "@/components/dashboard/analytics/performance-metrics"
import { getOverallPerformanceMetrics } from "@/app/actions/analytics-actions"

export const metadata: Metadata = {
  title: "Performance | Travella",
  description: "Track your car rental performance metrics",
}

export default async function PerformancePage() {
  const metrics = await getOverallPerformanceMetrics()

  return (
    <div className="flex flex-col gap-4">
      <h1 className="text-3xl font-bold">Performance Dashboard</h1>
      <PerformanceMetrics metrics={metrics} />
    </div>
  )
}

