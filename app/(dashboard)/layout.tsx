import type React from "react"
import { DashboardSidebar } from "@/components/dashboard/sidebar-new"
import { DashboardHeader } from "@/components/dashboard/header-new"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { requireAuth } from "@/lib/user-server"

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Get authenticated user data with centralized utility
  const userData = await requireAuth();

  return (
    <div className="flex min-h-screen">
      <SidebarProvider>
      <DashboardSidebar />
      <SidebarInset>
        <div className="flex flex-col min-h-screen">
          <DashboardHeader user={userData.user as any}/>
          <main className="flex-1 p-4 md:p-6 animate-in">{children}</main>
        </div>
      </SidebarInset>
      </SidebarProvider>
    </div>
  )
}

