"use server"

import { createClient } from "@/lib/supabase-server"
import { revalidatePath } from "next/cache"

export async function updateBookingStatus(
  bookingId: string,
  status: "pending" | "confirmed" | "completed" | "cancelled",
) {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    throw new Error("Unauthorized")
  }

  const userId = user.id

  try {
    // First, check if the user owns the car associated with this booking
    const { data: booking, error: bookingError } = await supabase
      .from("bookings")
      .select(`
        *,
        cars!inner (
          owner_id
        )
      `)
      .eq("id", bookingId)
      .single()

    if (bookingError || !booking) {
      throw new Error("Booking not found")
    }

    if (booking.cars.owner_id !== userId) {
      throw new Error("You do not have permission to update this booking")
    }

    // Update the booking status
    const { error: updateError } = await supabase
      .from("bookings")
      .update({
        status,
        updated_at: new Date().toISOString(),
      })
      .eq("id", bookingId)

    if (updateError) {
      throw new Error(updateError.message)
    }

    // If the status is completed, create a transaction record
    if (status === "completed") {
      const { error: transactionError } = await supabase.from("transactions").insert({
        booking_id: bookingId,
        user_id: userId,
        amount: booking.total_amount,
        type: "booking",
        status: "completed",
        description: `Payment for booking ${bookingId}`,
      })

      if (transactionError) {
        console.error("Error creating transaction:", transactionError)
        // We don't throw here because the booking status was updated successfully
      }
    }

    revalidatePath("/dashboard/bookings")
    return { success: true }
  } catch (error) {
    console.error("Error updating booking status:", error)
    throw error
  }
}

