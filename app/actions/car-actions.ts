"use server"

import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase-server"
import { z } from "zod"

const carSchema = z.object({
  title: z.string().min(2, { message: "Title must be at least 2 characters." }),
  description: z.string().optional(),
  make: z.string().min(1, { message: "Make is required" }),
  model: z.string().min(1, { message: "Model is required" }),
  year: z.coerce.number().min(1900, { message: "Year must be at least 1900" }),
  price: z.coerce.number().positive({ message: "Price must be positive" }),
  location: z.string().min(2, { message: "Location is required" }),
  status: z.enum(["active", "maintenance", "unavailable"]).default("active"),
  features: z.array(z.string()).optional(),
  images: z.array(z.string()).min(1, { message: "At least one image is required" }),
})

export type CarFormData = z.infer<typeof carSchema>

export async function createCar(formData: FormData) {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { error: "Unauthorized" }
  }

  const userId = user.id

  try {
    const rawData = {
      title: formData.get("title"),
      description: formData.get("description"),
      make: formData.get("make"),
      model: formData.get("model"),
      year: Number(formData.get("year")),
      price: Number(formData.get("price")),
      location: formData.get("location"),
      status: formData.get("status"),
      features: formData.getAll("features"),
      images: formData.getAll("images"),
    }

    const validatedData = carSchema.parse(rawData)

    const supabase = await createClient()

    // Insert car
    const { data: car, error: carError } = await supabase
      .from("car_listings")
      .insert({
        owner_id: userId,
        title: validatedData.title,
        description: validatedData.description || null,
        make: validatedData.make,
        model: validatedData.model,
        year: validatedData.year,
        price: validatedData.price,
        location: validatedData.location,
        status: validatedData.status,
      })
      .select()
      .single()

    if (carError) {
      return { error: carError.message }
    }

    // Add features
    if (validatedData.features && validatedData.features.length > 0) {
      const featuresData = validatedData.features.map((feature) => ({
        car_id: car.id,
        feature,
      }))

      const { error: featuresError } = await supabase.from("car_features").insert(featuresData)

      if (featuresError) {
        return { error: featuresError.message }
      }
    }

    // Add images
    if (validatedData.images && validatedData.images.length > 0) {
      const imagesData = validatedData.images.map((url) => ({
        car_id: car.id,
        url,
      }))

      const { error: imagesError } = await supabase.from("car_images").insert(imagesData)

      if (imagesError) {
        return { error: imagesError.message }
      }
    }

    revalidatePath("/dashboard/cars")
    redirect("/dashboard/cars")
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { error: error.errors.map((e) => `${e.path}: ${e.message}`).join(", ") }
    }
    return { error: "Failed to create car listing" }
  }
}

export async function updateCar(carId: string, formData: FormData) {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { error: "Unauthorized" }
  }

  const userId = user.id

  try {
    const rawData = {
      title: formData.get("title"),
      description: formData.get("description"),
      make: formData.get("make"),
      model: formData.get("model"),
      year: Number(formData.get("year")),
      price: Number(formData.get("price")),
      location: formData.get("location"),
      status: formData.get("status"),
      features: formData.getAll("features"),
      images: formData.getAll("images"),
    }

    const validatedData = carSchema.parse(rawData)

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    // Check if user owns the car
    const { data: existingCar, error: fetchError } = await supabase
      .from("cars")
      .select("owner_id")
      .eq("id", carId)
      .single()

    if (fetchError) {
      return { error: fetchError.message }
    }

    if (existingCar.owner_id !== userId) {
      return { error: "You do not have permission to update this car" }
    }

    // Update car
    const { error: carError } = await supabase
      .from("cars")
      .update({
        title: validatedData.title,
        description: validatedData.description || null,
        make: validatedData.make,
        model: validatedData.model,
        year: validatedData.year,
        price: validatedData.price,
        location: validatedData.location,
        status: validatedData.status,
        updated_at: new Date().toISOString(),
      })
      .eq("id", carId)

    if (carError) {
      return { error: carError.message }
    }

    // Delete existing features and add new ones
    await supabase.from("car_features").delete().eq("car_id", carId)

    if (validatedData.features && validatedData.features.length > 0) {
      const featuresData = validatedData.features.map((feature) => ({
        car_id: carId,
        feature,
      }))

      await supabase.from("car_features").insert(featuresData)
    }

    // Handle images (more complex - we don't want to delete all images if not all are changed)
    // For simplicity, we'll just add new images if they're provided
    if (validatedData.images && validatedData.images.length > 0) {
      // Get existing images
      const { data: existingImages } = await supabase.from("car_images").select("url").eq("car_id", carId)

      const existingUrls = existingImages?.map((img) => img.url) || []

      // Find new images that don't exist yet
      const newImages = validatedData.images.filter((url) => !existingUrls.includes(url))

      if (newImages.length > 0) {
        const imagesData = newImages.map((url) => ({
          car_id: carId,
          url,
        }))

        await supabase.from("car_images").insert(imagesData)
      }
    }

    revalidatePath("/dashboard/cars")
    revalidatePath(`/dashboard/cars/${carId}`)
    redirect("/dashboard/cars")
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { error: error.errors.map((e) => `${e.path}: ${e.message}`).join(", ") }
    }
    return { error: "Failed to update car listing" }
  }
}

export async function deleteCar(carId: string) {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { error: "Unauthorized" }
  }

  const userId = user.id

  try {

    // Check if user owns the car
    const { data: existingCar, error: fetchError } = await supabase
      .from("cars")
      .select("owner_id")
      .eq("id", carId)
      .single()

    if (fetchError) {
      return { error: fetchError.message }
    }

    if (existingCar.owner_id !== userId) {
      return { error: "You do not have permission to delete this car" }
    }

    // Delete car (cascade will handle related records)
    const { error: deleteError } = await supabase.from("cars").delete().eq("id", carId)

    if (deleteError) {
      return { error: deleteError.message }
    }

    revalidatePath("/dashboard/cars")
    return { success: true }
  } catch (error) {
    return { error: "Failed to delete car listing" }
  }
}

