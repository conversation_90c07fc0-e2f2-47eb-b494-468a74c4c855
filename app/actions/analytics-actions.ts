"use server"

import { createClient } from "@/lib/supabase-server"
import { format, subDays } from "date-fns"

// Generate sample data for now - in a real app, this would come from your database
function generateSampleData(days: number, min: number, max: number, trend: "up" | "down" | "stable" = "stable") {
  const data = []
  const now = new Date()
  let value = Math.floor(Math.random() * (max - min) + min)

  for (let i = days; i >= 0; i--) {
    const date = format(subDays(now, i), "yyyy-MM-dd")

    // Add some randomness but follow the trend
    const change = Math.random() * 0.2 - (trend === "up" ? -0.1 : trend === "down" ? 0.1 : 0)
    value = Math.max(min, Math.min(max, value * (1 + change)))

    data.push({
      date,
      value: Math.round(value),
    })
  }

  return data
}

export async function getCarPerformanceMetrics(carId?: string) {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    throw new Error("Unauthorized")
  }

  const userId = user.id

  try {

    // In a real application, you would query your database for actual metrics
    // For now, we'll generate sample data

    // Get car details if carId is provided
    let carDetails = null
    if (carId) {
      const { data: car, error } = await supabase
        .from("cars")
        .select("*")
        .eq("id", carId)
        .eq("owner_id", userId)
        .single()

      if (error || !car) {
        throw new Error("Car not found or you do not have permission to access it")
      }

      carDetails = car
    }

    // Generate sample metrics data
    // In a real app, you would query your analytics tables
    const days = 90

    return {
      views: generateSampleData(days, 5, 50, "up"),
      bookings: generateSampleData(days, 0, 5, "stable"),
      revenue: generateSampleData(days, 50, 500, "up").map((item) => ({
        ...item,
        value: Math.round(item.value * 10), // Make revenue larger
      })),
      occupancyRate: generateSampleData(days, 20, 80, "up"),
      carDetails,
    }
  } catch (error) {
    console.error("Error fetching performance metrics:", error)
    throw error
  }
}

export async function getOverallPerformanceMetrics() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    throw new Error("Unauthorized")
  }

  const userId = user.id

  try {
    const supabase = await createClient()

    // Get all cars owned by the user
    const { data: cars, error } = await supabase.from("cars").select("id").eq("owner_id", userId)

    if (error) {
      throw new Error("Failed to fetch cars")
    }

    // In a real application, you would aggregate metrics across all cars
    // For now, we'll generate sample data
    const days = 90

    return {
      views: generateSampleData(days, cars.length * 10, cars.length * 50, "up"),
      bookings: generateSampleData(days, cars.length * 1, cars.length * 5, "stable"),
      revenue: generateSampleData(days, cars.length * 100, cars.length * 500, "up"),
      occupancyRate: generateSampleData(days, 30, 70, "stable"),
      carCount: cars.length,
    }
  } catch (error) {
    console.error("Error fetching overall performance metrics:", error)
    throw error
  }
}

