import { decode } from "base64-arraybuffer";
import { createClient } from "@/lib/supabase-server"

/**
 * UploadImage
 * @param uri Local path of the image
 * @param path
 * @param bucket
 * @returns Image Public Url
 */
export async function uploadImage(
    {
        uri,
        path,
        bucket = "booking-photos",
    }: {
        uri: string,
        path: string,
        bucket?: string,
    }
): Promise<string> {
	const supabase = await createClient();

	try {
		// Convert URI to Blob
		const response = await fetch(uri);
		const blob = await response.blob();

		// Convert Blob to Base64
		const reader = new FileReader();
		const base64Promise = new Promise<string>((resolve) => {
			reader.onload = () => {
				const base64 = reader.result as string;
				resolve(base64.split(",")[1]);
			};
		});
		reader.readAsDataURL(blob);
		const base64 = await base64Promise;

		// Generate unique filename
		const filename = `${path}/${Date.now()}.jpg`;

		// Upload to Supabase Storage
		const { data, error: uploadError } = await supabase.storage
			.from(bucket)
			.upload(filename, decode(base64), {
				contentType: "image/jpeg",
				upsert: false,
			});

		if (uploadError) {
			throw new Error(uploadError.message);
		}

		// Get public URL
		const {
			data: { publicUrl },
		} = supabase.storage.from(bucket).getPublicUrl(filename);

		return publicUrl;
	} catch (error) {
		throw error;
	}
}