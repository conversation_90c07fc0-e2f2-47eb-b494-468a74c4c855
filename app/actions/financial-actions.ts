"use server"

import { auth } from "@/lib/auth"
import { createServerSupabaseClient } from "@/lib/supabase-server"
import { format, subDays } from "date-fns"

// Generate sample data for now - in a real app, this would come from your database
function generateSampleFinancialData(userId: string) {
  const now = new Date()
  const earnings = []
  const expenses = []
  const transactions = []

  // Generate daily data for the past 90 days
  for (let i = 90; i >= 0; i--) {
    const date = format(subDays(now, i), "yyyy-MM-dd")

    // Add some randomness to the data
    const earningAmount = Math.round(Math.random() * 300 + 100) // Between $100 and $400
    const expenseAmount = Math.round(Math.random() * 100 + 50) // Between $50 and $150

    earnings.push({
      date,
      amount: earningAmount,
    })

    expenses.push({
      date,
      amount: expenseAmount,
    })

    // Add transactions (not every day)
    if (Math.random() > 0.7) {
      transactions.push({
        id: `trans-${date}-${Math.random().toString(36).substring(2, 9)}`,
        date,
        description: `Booking payment for ${date}`,
        amount: earningAmount,
        type: "booking",
        status: "completed",
      })
    }

    if (Math.random() > 0.8) {
      transactions.push({
        id: `exp-${date}-${Math.random().toString(36).substring(2, 9)}`,
        date,
        description: `Maintenance expense`,
        amount: expenseAmount,
        type: "expense",
        status: "completed",
      })
    }
  }

  // Add some pending payouts
  for (let i = 0; i < 3; i++) {
    const date = format(subDays(now, i), "yyyy-MM-dd")
    transactions.push({
      id: `payout-${date}-${Math.random().toString(36).substring(2, 9)}`,
      date,
      description: `Payout to bank account`,
      amount: Math.round(Math.random() * 500 + 500),
      type: "payout",
      status: "pending",
    })
  }

  // Calculate summary
  const totalEarnings = earnings.reduce((sum, item) => sum + item.amount, 0)
  const totalExpenses = expenses.reduce((sum, item) => sum + item.amount, 0)
  const netIncome = totalEarnings - totalExpenses
  const pendingPayouts = transactions
    .filter((t) => t.type === "payout" && t.status === "pending")
    .reduce((sum, t) => sum + t.amount, 0)

  // Generate car performance data
  const carPerformance = [
    {
      carId: "car-1",
      carName: "Tesla Model 3",
      revenue: Math.round(totalEarnings * 0.4),
      bookings: Math.round(Math.random() * 20 + 10),
    },
    {
      carId: "car-2",
      carName: "BMW X5",
      revenue: Math.round(totalEarnings * 0.3),
      bookings: Math.round(Math.random() * 15 + 5),
    },
    {
      carId: "car-3",
      carName: "Toyota Camry",
      revenue: Math.round(totalEarnings * 0.2),
      bookings: Math.round(Math.random() * 10 + 5),
    },
    {
      carId: "car-4",
      carName: "Honda Civic",
      revenue: Math.round(totalEarnings * 0.1),
      bookings: Math.round(Math.random() * 5 + 2),
    },
  ]

  return {
    earnings,
    expenses,
    transactions,
    summary: {
      totalEarnings,
      totalExpenses,
      netIncome,
      pendingPayouts,
    },
    carPerformance,
  }
}

export async function getFinancialData() {
  const session = await auth()

  if (!session) {
    throw new Error("Unauthorized")
  }

  const userId = session.user.id

  try {
    const supabase = createServerSupabaseClient()

    // In a real application, you would query your database for actual financial data
    // For now, we'll generate sample data

    // Get all transactions for this user
    const { data: realTransactions, error } = await supabase.from("transactions").select("*").eq("user_id", userId)

    if (error) {
      console.error("Error fetching transactions:", error)
    }

    // If we have real transaction data, we could use it here
    // For now, we'll use the sample data

    return generateSampleFinancialData(userId)
  } catch (error) {
    console.error("Error fetching financial data:", error)
    throw error
  }
}

