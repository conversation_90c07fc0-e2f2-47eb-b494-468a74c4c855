"use server"

import { createClient } from "@/lib/supabase-server"
import { GetBookingPaymentsPayload, GetBookingPaymentsResponse } from "@/types/payments";
import { SupabaseResponse } from "@/types/supabase";

export const getBookingPayments = async ({
    page = 1,
    items_per_page = 20,
    status = null,
    payment_method = null,
    car_owner_id = null,
    renter_id = null,
    start_date = null,
    end_date = null,
}: GetBookingPaymentsPayload): Promise<GetBookingPaymentsResponse> => {
    try {
        const supabase = await createClient();
        const { data, error } = (await supabase
            .rpc('get_paginated_booking_payments', {
                p_page: page,
                p_items_per_page: items_per_page,
                p_payment_status: status,
                p_payment_method: payment_method,
                p_car_owner_id: car_owner_id,
                p_renter_id: renter_id,
                p_start_date: start_date,
                p_end_date: end_date,
            })) as unknown as SupabaseResponse<GetBookingPaymentsResponse>

        return data;
    } catch (error) {
        console.error(error);
        throw new Error("Failed to get booking payments");
    }
};