"use server"

import { createClient } from "@/lib/supabase-server";
import { GetPayoutsPayload, Host, HostPaymentAccount, HostPayoutStats, Payout, UpdateHostPayoutStatusPayload, GetHostTransfersPayload, PaymentTransfer, CreateTransferPayload, UpdateTransferPayload, GetHostsPayload, GetHostsResponse, HostResponseData } from "@/types/hosts";
import { SupabaseResponse, SupabaseErrorCodes } from "@/types/supabase";

export async function getHosts({
    itemsPerPage,
    page,
    searchTerm,
}: GetHostsPayload): Promise<HostResponseData> {
  try {
    const supabase = await createClient();

    const { data, error } = (await supabase
        .rpc('get_paginated_hosts', {
            page_number: page,
            items_per_page: itemsPerPage,
            search_key: searchTerm
        })
    ) as unknown as GetHostsResponse;

    return data;
  } catch (error) {
    throw new Error("Failed to get hosts");
  }
}

export async function getHostPaymentAccounts({
  hostId,
}: { 
  hostId: string 
}): Promise<HostPaymentAccount[]> {
  try {
    const supabase = await createClient();

    const { data, error } = (await supabase
      .from("host_payout_settings")
      .select("*")
      .eq("user_id", hostId)
      .order("created_at", { ascending: false })) as unknown as SupabaseResponse<HostPaymentAccount[]>;

    if (error && error.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) throw error;
    return data;
  } catch (error) {
    console.error('error fetching host payment accounts', error);
    throw new Error("Failed to get host payment accounts");
  }
}

export async function getHostPayouts({
  itemsPerPage,
  page,
  status = null,
  hostId = null,
  booking_status = null,
  host_name = null
}: GetPayoutsPayload) {
    try {
        const supabase = await createClient();

        let query = supabase.rpc('get_all_host_payouts', {
            limit_val: itemsPerPage,
            offset_val: (page - 1) * itemsPerPage,
            status_filter: status,
            host_id_filter: hostId
        })

        if (booking_status) {
            query = query.filter('booking_status', 'eq', booking_status)
        }

        if (host_name) {
            query = query.filter('host_first_name', 'ilike', `%${host_name}%`).filter('host_last_name', 'ilike', `%${host_name}%`)
        }

        const { data, error } = (await query) as unknown as SupabaseResponse<Payout[]>

        if (error && error.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) throw error;
        return data;
    } catch (error) {
        console.error('error fetching host payouts', error);
        throw new Error("Failed to get host payouts");
    }
}

/**
 * Get the payout status for a host
 * @param hostId - The id of the host
 * @returns The payout status for the host showing pending, completed, failed and processing
 */
export async function getHostPayoutStatus(hostId: string): Promise<HostPayoutStats> {
    try {
        const supabase = await createClient();

        const { data, error } = await supabase.rpc('get_host_payout_stats', {
            host_uuid: hostId
        }) as unknown as SupabaseResponse<HostPayoutStats[]>

        if (error && error.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) throw error;
        return data[0];
    } catch (error) {
        console.error('error fetching host payout status', error);
        throw new Error("Failed to get host payout status");
    }
}

/**
 * Update the status of a host payout. We will set this up as a trigger later.
 * @param payload - The payload containing the payout id and status
 * @returns The updated payout
 */
export async function updateHostPayoutStatus(payload: UpdateHostPayoutStatusPayload) {
    try {
        const supabase = await createClient();
        console.log("Update status payload", payload.payoutStatus)

        const { data, error } = await supabase.rpc('update_host_payout_status', {
            p_payout_id: payload.payoutId,
            p_status: payload.payoutStatus
        }) as unknown as SupabaseResponse<Payout>

        if (error && error.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) throw error;
        return data;

    } catch (error) {
        console.error('error updating host payout status', error);
        throw new Error("Failed to update host payout status");
    }
}

export async function getHostTransfers(payload: GetHostTransfersPayload) {
    try {
        const { itemsPerPage = 20, page = 1, status = null, hostId = null } = payload;
        const supabase = await createClient();

        const { data, error } = await supabase.rpc('get_filtered_host_payout_transfers', {
            p_limit: itemsPerPage,
            p_offset: (page - 1) * itemsPerPage,
            p_status: status,
            p_user_id: hostId
        }) as unknown as SupabaseResponse<PaymentTransfer[]>

        if (error && error.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) throw error;
        return data;

    } catch (error) {
        console.error('error fetching host transfers', error);
        throw new Error("Failed to get host transfers");
    }
}

export async function createTransfer(payload: CreateTransferPayload) {
    try {
        const supabase = await createClient();

        const { hostId, payout_id, amount, currency = 'UGX', description = null, reference_id = null } = payload;

        const { data, error } = await supabase.rpc('create_host_payout_transfer', {
            p_user_id: hostId,
            p_payout_setting_id: payout_id,
            p_amount: amount,
            p_currency: currency,
            p_description: description,
            p_reference_id: reference_id
        }) as unknown as SupabaseResponse<PaymentTransfer>

        if (error) throw error;
        return data;
    } catch (error) {
        console.error('error creating transfer', error);
        throw new Error("Failed to create transfer");
    }
}

export async function updateTransfer(payload: UpdateTransferPayload) {
    try {
        const supabase = await createClient();

        const { transferId, status, reference_id, description, transaction_date } = payload;

        const { data, error } = await supabase.rpc('update_host_payout_transfer', {
            p_transfer_id: transferId,
            p_status: status,
            p_reference_id: reference_id,
            p_description: description,
            p_transaction_date: transaction_date
        }) as unknown as SupabaseResponse<PaymentTransfer>

        if (error) throw error;
        return data;
    } catch (error) {
        console.error('error updating transfer', error);
        throw new Error("Failed to update transfer");
    }
}

export async function deleteTransfer(transferId: string) {
    try {
        const supabase = await createClient();

        const { data, error } = await supabase.rpc('delete_host_payout_transfer', {
            p_transfer_id: transferId
        }) as unknown as SupabaseResponse<PaymentTransfer>

        if (error) throw error;
        return data;
    } catch (error) {
        console.error('error deleting transfer', error);
        throw new Error("Failed to delete transfer");
    }
}