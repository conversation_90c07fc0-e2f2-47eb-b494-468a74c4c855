"use server"

import { createClient } from "@/lib/supabase-server"
import { 
  CarFeature, 
  CancellationPolicy, 
  CarBrand,
  CreateCarBrandInput,
  CreateCarFeatureInput,
  CreateCancellationPolicyInput,
  UpdateCarBrandInput,
  UpdateCarFeatureInput,
  UpdateCancellationPolicyInput
} from "@/types/listings";
import { SupabaseResponse } from "@/types/supabase";

// GET methods
export const getCarFeatures = async (
  page = 1, 
  limit = 10
): Promise<{ data: CarFeature[], count: number }> => {
  const supabase = await createClient();
  const start = (page - 1) * limit;
  try {
    // Get total count
    const { count } = await supabase
      .from("car_features")
      .select("*", { count: "exact", head: true });

    // Get paginated data
    const { data: carFeatures, error: carFeaturesError } = (await supabase
      .from("car_features")
      .select("*")
      .range(start, start + limit - 1)
      .order('name')) as unknown as SupabaseResponse<CarFeature[]>;

    if (carFeaturesError) {
      throw new Error(carFeaturesError.message || "Error fetching car features");
    }

    return { 
      data: carFeatures || [], 
      count: count || 0
    };
  } catch (error) {
    console.error("Error in getCarFeatures:", error);
    throw error;
  }
}

export const getCancellationPolicies = async (
  page = 1, 
  limit = 10
): Promise<{ data: CancellationPolicy[], count: number }> => {
  const supabase = await createClient();
  const start = (page - 1) * limit;
  try {
    // Get total count
    const { count } = await supabase
      .from("cancellation_policies")
      .select("*", { count: "exact", head: true });

    // Get paginated data
    const { data: cancellationPolicies, error: cancellationPoliciesError } = (await supabase
      .from("cancellation_policies")
      .select("*")
      .range(start, start + limit - 1)
      .order('name')) as unknown as SupabaseResponse<CancellationPolicy[]>;

    if (cancellationPoliciesError) {
      throw new Error(cancellationPoliciesError.message || "Error fetching cancellation policies");
    }

    return { 
      data: cancellationPolicies || [], 
      count: count || 0 
    };
  } catch (error) {
    console.error("Error in getCancellationPolicies:", error);
    throw error;
  }
}

export const getCarBrands = async (
  page = 1, 
  limit = 10
): Promise<{ data: CarBrand[], count: number }> => {
  const supabase = await createClient();
  const start = (page - 1) * limit;
  try {
    // Get total count
    const { count } = await supabase
      .from("car_brands")
      .select("*", { count: "exact", head: true });

    // Get paginated data
    const { data: carBrands, error: carBrandsError } = (await supabase
      .from("car_brands")
      .select("*")
      .range(start, start + limit - 1)
      .order('name')) as unknown as SupabaseResponse<CarBrand[]>;

    if (carBrandsError) {
      throw new Error(carBrandsError.message || "Error fetching car brands");
    }

    return { 
      data: carBrands || [], 
      count: count || 0 
    };
  } catch (error) {
    console.error("Error in getCarBrands:", error);
    throw error;
  }
}

// CREATE methods
export const createCarBrand = async (
  brand: CreateCarBrandInput
): Promise<CarBrand> => {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from("car_brands")
      .insert(brand)
      .select()
      .single();

    if (error) {
      throw new Error(error.message || "Error creating car brand");
    }

    return data;
  } catch (error) {
    console.error("Error in createCarBrand:", error);
    throw error;
  }
}

export const createCarFeature = async (
  feature: CreateCarFeatureInput
): Promise<CarFeature> => {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from("car_features")
      .insert(feature)
      .select()
      .single();

    if (error) {
      throw new Error(error.message || "Error creating car feature");
    }

    return data;
  } catch (error) {
    console.error("Error in createCarFeature:", error);
    throw error;
  }
}

export const createCancellationPolicy = async (
  policy: CreateCancellationPolicyInput
): Promise<CancellationPolicy> => {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from("cancellation_policies")
      .insert(policy)
      .select()
      .single();

    if (error) {
      throw new Error(error.message || "Error creating cancellation policy");
    }

    return data;
  } catch (error) {
    console.error("Error in createCancellationPolicy:", error);
    throw error;
  }
}

// UPDATE methods
export const updateCarBrand = async (
  id: string,
  updates: UpdateCarBrandInput
): Promise<CarBrand> => {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from("car_brands")
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message || "Error updating car brand");
    }

    return data;
  } catch (error) {
    console.error("Error in updateCarBrand:", error);
    throw error;
  }
}

export const updateCarFeature = async (
  id: string,
  updates: UpdateCarFeatureInput
): Promise<CarFeature> => {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from("car_features")
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message || "Error updating car feature");
    }

    return data;
  } catch (error) {
    console.error("Error in updateCarFeature:", error);
    throw error;
  }
}

export const updateCancellationPolicy = async (
  id: string,
  updates: UpdateCancellationPolicyInput
): Promise<CancellationPolicy> => {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from("cancellation_policies")
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message || "Error updating cancellation policy");
    }

    return data;
  } catch (error) {
    console.error("Error in updateCancellationPolicy:", error);
    throw error;
  }
}

// DELETE methods
export const deleteCarBrand = async (id: string): Promise<void> => {
  const supabase = await createClient();
  try {
    const { error } = await supabase
      .from("car_brands")
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(error.message || "Error deleting car brand");
    }
  } catch (error) {
    console.error("Error in deleteCarBrand:", error);
    throw error;
  }
}

export const deleteCarFeature = async (id: string): Promise<void> => {
  const supabase = await createClient();
  try {
    const { error } = await supabase
      .from("car_features")
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(error.message || "Error deleting car feature");
    }
  } catch (error) {
    console.error("Error in deleteCarFeature:", error);
    throw error;
  }
}

export const deleteCancellationPolicy = async (id: string): Promise<void> => {
  const supabase = await createClient();
  try {
    const { error } = await supabase
      .from("cancellation_policies")
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(error.message || "Error deleting cancellation policy");
    }
  } catch (error) {
    console.error("Error in deleteCancellationPolicy:", error);
    throw error;
  }
}