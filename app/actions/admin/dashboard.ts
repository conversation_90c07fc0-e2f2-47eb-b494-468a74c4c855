"use server"

import { createClient } from "@/lib/supabase-server"
import { DashboardStats, PlatformOverview } from "@/types/dashboard";
import { SupabaseResponse } from "@/types/supabase";

export const getDashboardStats = async (): Promise<DashboardStats> => {
    try {
        const supabase = await createClient();
        const { data, error } = (await supabase.rpc("get_admin_dashboard_stats")) as unknown as SupabaseResponse<DashboardStats>
        if (error) {
            throw error;
        }
    
        return data;
    } catch (error) {
        console.error("Error fetching dashboard stats:", error);
        throw error;
    }
};

export const getPlatformOverview = async (): Promise<PlatformOverview> => {
    try {
        const supabase = await createClient();
        const { data, error } = (await supabase.rpc("admin_platform_overview")) as unknown as SupabaseResponse<PlatformOverview>
        if (error) {
            throw error;
        }
        return data;
    } catch (error) {
        console.error("Error fetching platform overview:", error);
        throw error;
    }
}
