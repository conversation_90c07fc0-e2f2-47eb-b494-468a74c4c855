"use server";

import { createClient } from "@/lib/supabase-server";
import { GetBookingRefundsPayload, GetBookingRefundResquestsResponse, UpdateRefundStatusPayload } from "@/types/refunds";
import { SupabaseResponse, Database } from "@/types/supabase";
import { revalidatePath } from "next/cache";

export const getBookingRefunds = async (payload: GetBookingRefundsPayload) => {
    try {
        const supabase = await createClient();
        
        // Make the RPC call to get paginated refunds
        const { data, error } = (await supabase.rpc("get_paginated_refunds", {
            p_page: payload.page,
            p_items_per_page: payload.items_per_page,
            p_status: payload.status,
            p_user_name: payload.user_name,
            p_sort_direction: "desc", // Default sort direction
        })) as unknown as SupabaseResponse<GetBookingRefundResquestsResponse>

        if (error) {
            console.error("Error fetching booking refunds:", error);
            // Return empty result set with passed parameters to avoid breaking the UI
            return {
                data: [],
                count: 0,
                has_next: false,
                page: payload.page,
                items_per_page: payload.items_per_page,
                total_pages: 0,
                sort_direction: "desc"
            };
        }

        return data;
    } catch (error) {
        console.error("Error in getBookingRefunds:", error);
        // Return empty result set with passed parameters to avoid breaking the UI
        return {
            data: [],
            count: 0,
            has_next: false,
            page: payload.page,
            items_per_page: payload.items_per_page,
            total_pages: 0,
            sort_direction: "desc"
        };
    }
}

export const updateRefundStatus = async (payload: UpdateRefundStatusPayload) => {
    try {
        const supabase = await createClient();

        // Validate input parameters
        if (!payload.refund_id || !payload.status) {
            return {
                success: false,
                message: "Invalid parameters: refund ID and status are required"
            };
        }

        // Call the RPC function to update refund status
        const { data, error } = await supabase.rpc("update_refund_status", {
            p_refund_id: payload.refund_id,
            p_status: payload.status,
            p_notes: payload.notes || null,
        });

        if (error) {
            console.error("Error updating refund status:", error);
            return {
                success: false,
                message: error.message || "Error updating refund status"
            };
        }

        // Revalidate the refunds page to refresh the data
        revalidatePath('/admin/refunds');

        // Return success response
        return {
            success: true,
            message: `Refund status updated to ${payload.status} successfully`
        };
    } catch (error) {
        console.error("Error in updateRefundStatus:", error);
        return {
            success: false,
            message: "An unexpected error occurred while updating refund status"
        };
    }
}