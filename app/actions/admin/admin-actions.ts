"use server"

import { revalidate<PERSON>ath } from "next/cache"
import { createClient } from "@/lib/supabase-server"
import { BookingStatus, PaymentType, SupabaseErrorCodes, SupabaseResponse, UserRole } from "@/types/supabase";
import { CarListingResponse, GetCarlistingsPayload } from "@/types/listings";
import { BookingIssue, BookingModificationRequest, BookingPayment, BookingResponse, FilterBookingsPayload } from "@/types/bookings";
import { getUserData } from "@/lib/user-server";
import { Country } from "@/types/settings";
import { GetAdminUsersPayload, GetAdminUsersResponse, UpdateUserRolePayload, UpdateUserRoleResponse, UserProfile } from "@/types/users";
import { SupportTicket } from "@/types/support-tickets";
import { SupportTicketMessage, SupportTicketMessageResponse } from "@/types/support-messages";

// Create a connection pool for Postgres
let pool: any = null;

// Function to get a connection pool
export async function getConnectionPool() {
  if (!pool) {
    const { Pool } = await import('pg');
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: { rejectUnauthorized: false }
    });
  }
  return pool;
}

// Function to get the authenticated user session
export async function auth() {
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();
  return session;
}

export async function getAdminAnalytics() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    throw new Error("Unauthorized")
  }

  try {
    // Get total users count
    const { count: totalUsers } = await supabase
      .from("profiles")
      .select("*", { count: "exact", head: true })

    // Get active users count (users with status = active or no status)
    const { count: activeUsers } = await supabase
      .from("profiles")
      .select("*", { count: "exact", head: true })
      .or("status.eq.active,status.is.null")

    // Get total cars count
    const { count: totalCars } = await supabase
      .from("car_listings")
      .select("*", { count: "exact", head: true })

    // Get pending approvals count
    const { count: pendingApprovals } = await supabase
      .from("car_listings")
      .select("*", { count: "exact", head: true })
      .eq("status", "pending")

    // Get total bookings count
    const { count: totalBookings } = await supabase
      .from("bookings")
      .select("*", { count: "exact", head: true })

    // Get total revenue with currency information
    const { data: payments } = await supabase
      .from("booking_payments")
      .select("amount, currency")
      .eq("payment_status", "successful")

    // Group payments by currency
    const revenueByCurrency: Record<string, number> = {}
    payments?.forEach(payment => {
      const currency = payment.currency || "USD"
      revenueByCurrency[currency] = (revenueByCurrency[currency] || 0) + (payment.amount || 0)
    })

    // Calculate total revenue (for display purposes, we'll use the sum of all currencies)
    const totalRevenue = payments?.reduce((sum, payment) => sum + (payment.amount || 0), 0) || 0

    // Get monthly booking stats for the current year
    const currentYear = new Date().getFullYear()
    const { data: monthlyBookings } = await supabase
      .from("bookings")
      .select("created_at, total_amount")
      .gte("created_at", `${currentYear}-01-01`)
      .lte("created_at", `${currentYear}-12-31`)

    // Process monthly data
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    const bookingStats = Array(12).fill(0).map((_, i) => ({
      date: `${currentYear}-${String(i + 1).padStart(2, '0')}-01`,
      count: 0,
      revenue: 0
    }))

    const userGrowth = Array(12).fill(0).map((_, i) => ({
      date: `${currentYear}-${String(i + 1).padStart(2, '0')}-01`,
      count: 0
    }))

    const carStats = Array(12).fill(0).map((_, i) => ({
      date: `${currentYear}-${String(i + 1).padStart(2, '0')}-01`,
      count: 0,
      active: 0,
      pending: 0
    }))

    // Process monthly bookings
    monthlyBookings?.forEach(booking => {
      if (booking.created_at) {
        const date = new Date(booking.created_at)
        const monthIndex = date.getMonth()

        bookingStats[monthIndex].count++
        bookingStats[monthIndex].revenue += booking.total_amount || 0
      }
    })

    // Get user types distribution
    const { data: userTypesData } = await supabase
      .from("profiles")
      .select("*")

    const userTypes = [
      { type: "Car Owners", count: 0 },
      { type: "Renters", count: 0 },
      { type: "Admins", count: 0 }
    ]

    userTypesData?.forEach(user => {
      if ((user as any).role === "admin") userTypes[2].count++
      else if ((user as any).role === "car_owner") userTypes[0].count++
      else userTypes[1].count++ // Default to renter
    })

    // Format monthly revenue data
    const revenueByMonth = months.map((month, index) => ({
      month,
      revenue: bookingStats[index].revenue
    }))

    return {
      overview: {
        totalUsers: totalUsers || 0,
        totalCars: totalCars || 0,
        totalBookings: totalBookings || 0,
        totalRevenue: totalRevenue,
        revenueByCurrency,
        activeUsers: activeUsers || 0,
        pendingApprovals: pendingApprovals || 0,
      },
      userGrowth,
      bookingStats,
      carStats,
      userTypes,
      topLocations: [
        { location: "New York", count: 245 },
        { location: "Los Angeles", count: 198 },
        { location: "Chicago", count: 156 },
        { location: "Miami", count: 132 },
        { location: "San Francisco", count: 124 },
      ],
      popularCars: [
        { make: "Tesla", model: "Model 3", bookings: 342 },
        { make: "BMW", model: "X5", bookings: 287 },
        { make: "Toyota", model: "Camry", bookings: 245 },
        { make: "Honda", model: "Civic", bookings: 198 },
        { make: "Mercedes", model: "C-Class", bookings: 176 },
      ],
      revenueByMonth,
    }
  } catch (error) {
    throw error
  }
}

export async function getAdminUsers(payload: GetAdminUsersPayload = { page: 1, limit: 10 }): Promise<GetAdminUsersResponse> {
  const supabase = await createClient();
  const { page = 1, limit = 20, search, role } = payload;

  try {
    const { data, error } = (await supabase.rpc("get_paginated_users", {
      p_page: page,
      p_items_per_page: limit,
      p_role: role,
      p_search: search
    })) as unknown as SupabaseResponse<GetAdminUsersResponse>;

    if (error) {
      throw new Error(error.message)
    }

    return data;
  } catch (error) {
    throw error;
  }
}

export const updateUserRole = async ({
  user_id,
  role,
}: UpdateUserRolePayload) => {
  const supabase = await createClient();

  try {
    const { error, data } = (await supabase.rpc("update_user_role", {
      p_user_id: user_id,
      p_new_role: role
    })) as unknown as SupabaseResponse<UpdateUserRoleResponse>;

    if (error) {
      throw new Error(error.message)
    }

    return data;
  } catch (error) {
    throw error;
  }
}

export const getUserProfileById = async (user_id: string) => {
  const supabase = await createClient();

  try {
    const { data, error } = (await supabase.rpc('get_user_profile', {
      p_user_id: user_id
    })) as unknown as SupabaseResponse<UserProfile>;

    if (error) {
      throw new Error(error.message)
    }

    return data;
  } catch (error) {
    throw error;
  }
}

export async function updateUserStatus(userId: string, status: "active" | "suspended" | "pending") {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    throw new Error("Unauthorized")
  }

  // Check if user is admin
  const { data: profile } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", user.id)
    .single();

  if (!profile || profile.role !== "admin") {
    throw new Error("Unauthorized")
  }

  try {
    // Update the user's status using a raw query
    // Since the profiles table doesn't have a status field in the TypeScript types,
    // we'll use a more generic approach with raw SQL
    const { error } = await supabase
      .from("profiles")
      .update({ "status": status } as any)
      .eq("id", userId)

    if (error) {
      throw new Error(error.message)
    }

    revalidatePath("/admin/users")
    return { success: true }
  } catch (error) {
    throw error
  }
}

export async function getCarDetails(carId: string): Promise<CarListingResponse> {
  const supabase = await createClient();
  const { data: car, error } = (await supabase.from("filtered_cars").select("*").eq("id", carId).single()) as unknown as SupabaseResponse<CarListingResponse>

  if (error && error?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) {
    throw new Error(error.message)
  }

  return car;
}

export async function getAllCarListings(payload: GetCarlistingsPayload): Promise<CarListingResponse> {
  const supabase = await createClient();

  try {
    const { items_per_page = 20, page = 1, status, country_id, search } = payload;
    console.log("Fetch cars filters", payload)
    const { data, error } = (await supabase.rpc("get_paginated_car_listings", {
      p_page: page,
      p_items_per_page: items_per_page,
      p_country_id: country_id,
      p_status: status,
      p_owner_name: search,
      p_sort_by: "created_at",
      p_sort_direction: "desc"
    })) as unknown as SupabaseResponse<CarListingResponse>

    if (error) {
      throw new Error(error.message)
    }

    return data;
  } catch (error) {
    throw error;
  }
}

// This function is kept for backward compatibility
// We filter listings in the page component instead
export async function getPendingCarListings() {
  return await getAllCarListings({ status: 'draft', items_per_page: 10, page: 1 })
}

/**
 * Updates a booking's status
 */
export async function updateBookingStatus(
  bookingId: string, 
  status: BookingStatus,
  notes?: string
) {
  const session = await auth();
  
  // Verify admin user
  if (!session?.user?.email) {
    throw new Error("Authentication required");
  }

  const isAdmin = await verifyAdminUser(session.user.email);
  if (!isAdmin) {
    throw new Error("Admin privileges required");
  }

  try {
    const pool = await getConnectionPool();
    const client = await pool.connect();

    try {
      // Begin transaction
      await client.query('BEGIN');

      // Update booking status
      const updateQuery = `
        UPDATE bookings 
        SET 
          status = $1, 
          updated_at = NOW(),
          ${status === 'cancelled' ? 'cancellation_reason = $3, canceled_at = NOW(),' : ''} 
          admin_notes = CASE WHEN $3 IS NOT NULL THEN 
            COALESCE(admin_notes, '') || E'\n' || $3 
          ELSE admin_notes END
        WHERE id = $2
        RETURNING *
      `;

      const result = await client.query(updateQuery, [status, bookingId, notes]);

      if (result.rowCount === 0) {
        throw new Error("Booking not found or could not be updated");
      }

      // Commit transaction
      await client.query('COMMIT');
      return result.rows[0];
    } catch (error) {
      // Rollback transaction on error
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error("Error updating booking status:", error);
    throw new Error(`Failed to update booking status: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

export async function getAdminBookings(): Promise<BookingResponse[]> {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser()

  try {
    // Get all bookings from the filtered view
    const { data: bookings, error } = (await supabase
      .from("vw_filtered_bookings")
      .select('*')
      .order('created_at', { ascending: false })) as unknown as SupabaseResponse<BookingResponse[]>

    if (error && error?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) {
      throw new Error(error.message)
    }

    return bookings;
  } catch (error) {
    throw error
  }
}

export async function approveCarListing(carId: string) {
  const supabase = await createClient();

  try {
    // Update car listing status
    const { error: updateError, data } = await supabase.rpc(
      "update_car_status" as any, 
      {
        car_id: carId,
        new_status: "approved",
        reason: "Car listing approved by admin"
      }
    );

    if (updateError) {
      throw new Error(`Error updating car listing: ${updateError.message}`)
    }

    revalidatePath("/admin/cars")
    return { success: true }
  } catch (error) {
    throw error
  }
}

export async function getAdminSettings() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    throw new Error("Unauthorized")
  }

  try {
    // Get settings from the database
    const { data: settings, error } = await supabase
      .from("settings")
      .select("*")
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (error) {
      console.error("Error fetching settings:", error)
      // Return default settings
      return {
        site_name: "Travella",
        site_description: "Car rental platform",
        contact_email: "<EMAIL>",
        support_phone: "+1234567890",
        currency: "USD",
        booking_fee_percentage: 10,
        smtp_host: "",
        smtp_port: 587,
        smtp_user: "",
        smtp_password: "",
        smtp_from_email: "<EMAIL>",
        smtp_from_name: "Travella",
        payment_gateway: "stripe",
        stripe_public_key: "",
        stripe_secret_key: "",
        paypal_client_id: "",
        paypal_secret_key: "",
        flutterwave_public_key: "",
        flutterwave_secret_key: "",
        flutterwave_encryption_key: "",
        enable_email_notifications: true,
        enable_sms_notifications: false,
        enable_push_notifications: false,
        notify_admin_on_new_booking: true,
        notify_admin_on_new_user: true,
        notify_admin_on_new_car: true,
        notify_owner_on_new_booking: true,
        notify_renter_on_booking_status: true,
        booking_confirmation_template: "Dear {{renter_name}},\n\nYour booking for {{car_name}} has been confirmed. Your booking ID is {{booking_id}}.\n\nPickup: {{pickup_date}} at {{pickup_time}}\nReturn: {{return_date}} at {{return_time}}\n\nThank you for choosing our service.\n\nBest regards,\nThe Team",
        booking_cancellation_template: "Dear {{renter_name}},\n\nYour booking for {{car_name}} has been cancelled. Your booking ID was {{booking_id}}.\n\nReason: {{cancellation_reason}}\n\nIf you have any questions, please contact our support team.\n\nBest regards,\nThe Team",
        welcome_email_template: "Dear {{user_name}},\n\nWelcome to our platform! We're excited to have you on board.\n\nYou can now browse and book cars for your next trip.\n\nIf you have any questions, please don't hesitate to contact our support team.\n\nBest regards,\nThe Team",
        allow_user_registration: true,
        default_user_role: "renter",
        require_email_verification: true,
        maintenance_mode: false,
        maintenance_message: "The site is currently under maintenance. Please check back later.",
        max_cars_per_owner: 10,
        max_bookings_per_user: 5,
      }
    }

    return settings
  } catch (error) {
    console.error("Error fetching settings:", error)
    // Return default settings in case of error
    return {
      site_name: "Travella",
      site_description: "Car rental platform",
      contact_email: "<EMAIL>",
      support_phone: "+1234567890",
      currency: "UGX",
      booking_fee_percentage: 10,
      smtp_host: "",
      smtp_port: 587,
      smtp_user: "",
      smtp_password: "",
      smtp_from_email: "<EMAIL>",
      smtp_from_name: "Travella",
      payment_gateway: "flutterwave",
      stripe_public_key: "",
      stripe_secret_key: "",
      paypal_client_id: "",
      paypal_secret_key: "",
      flutterwave_public_key: "",
      flutterwave_secret_key: "",
      flutterwave_encryption_key: "",
      enable_email_notifications: true,
      enable_sms_notifications: false,
      enable_push_notifications: false,
      notify_admin_on_new_booking: true,
      notify_admin_on_new_user: true,
      notify_admin_on_new_car: true,
      notify_owner_on_new_booking: true,
      notify_renter_on_booking_status: true,
      booking_confirmation_template: "Dear {{renter_name}},\n\nYour booking for {{car_name}} has been confirmed. Your booking ID is {{booking_id}}.\n\nPickup: {{pickup_date}} at {{pickup_time}}\nReturn: {{return_date}} at {{return_time}}\n\nThank you for choosing our service.\n\nBest regards,\nThe Team",
      booking_cancellation_template: "Dear {{renter_name}},\n\nYour booking for {{car_name}} has been cancelled. Your booking ID was {{booking_id}}.\n\nReason: {{cancellation_reason}}\n\nIf you have any questions, please contact our support team.\n\nBest regards,\nThe Team",
      welcome_email_template: "Dear {{user_name}},\n\nWelcome to our platform! We're excited to have you on board.\n\nYou can now browse and book cars for your next trip.\n\nIf you have any questions, please don't hesitate to contact our support team.\n\nBest regards,\nThe Team",
      allow_user_registration: true,
      default_user_role: "renter",
      require_email_verification: true,
      maintenance_mode: false,
      maintenance_message: "The site is currently under maintenance. Please check back later.",
      max_cars_per_owner: 10,
      max_bookings_per_user: 5,
    }
  }
}

export async function updateAdminSettings(settings: any) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    throw new Error("Unauthorized")
  }

  try {
    // Get existing settings
    const { data: existingSettings } = await supabase
      .from("settings")
      .select("id")
      .limit(1)

    if (existingSettings && existingSettings.length > 0) {
      // Update existing settings
      const { error } = await supabase
        .from("settings")
        .update({
          ...settings,
          updated_at: new Date().toISOString(),
        })
        .eq("id", existingSettings[0].id)

      if (error) throw new Error(error.message)
    } else {
      // Insert new settings
      const { error } = await supabase
        .from("settings")
        .insert({
          ...settings,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })

      if (error) throw new Error(error.message)
    }

    revalidatePath("/admin/settings")
    return { success: true }
  } catch (error) {
    console.error("Error updating settings:", error)
    throw error
  }
}

export async function rejectCarListing(carId: string, reason: string) {
  const supabase = await createClient();

  try {
    // Update car listing status
    const { error: updateError } = await supabase.rpc(
      "update_car_status" as any,
      {
        car_id: carId,
        new_status: "rejected",
        reason
      }
    );

    if (updateError) {
      throw new Error(`Error updating car listing: ${updateError.message}`)
    }

    revalidatePath("/admin/cars")
    return { success: true }
  } catch (error) {
    throw error
  }
}

// Helper function to verify user is an admin
async function verifyAdminUser(email: string) {
  try {
    // Use the getUserData function from user-server to check admin status
    const userData = await getUserData();
    
    // Verify user is admin and has the same email
    return userData?.isAdmin && userData.user.email === email;
  } catch (error) {
    throw new Error("Unauthorized: Admin privileges required");
  }
}

export async function getCountries(): Promise<Country[]> {
  try {
    const supabase = await createClient();
    // Get all countries
    const { data: countries, error } = (await supabase
      .from("supported_countries")
      .select("*")
      .eq("is_active", true)
      .order('name', { ascending: true })) as unknown as SupabaseResponse<Country[]>

    if (error) {
      throw new Error(error.message)
    }

    return countries || []
  } catch (error) {
    console.error("Error fetching countries:", error)
    throw error
  }
}

export async function createCountry(countryData: {
  name: string;
  code: string;
  currency_code: string;
  currency_prefix: string;
  is_active?: boolean;
  flag_image?: string;
}) {
  try {
    const supabase = await createClient();
    // Insert new country
    const { error } = await supabase
      .from("supported_countries")
      .insert({
        name: countryData.name,
        code: countryData.code.toUpperCase(),
        currency_code: countryData.currency_code.toUpperCase(),
        currency_prefix: countryData.currency_prefix,
        is_active: countryData.is_active !== undefined ? countryData.is_active : true,
        flag_image: countryData.flag_image || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })

    if (error) {
      throw new Error(error.message)
    }

    revalidatePath("/admin/settings")
    return { success: true }
  } catch (error) {
    console.error("Error creating country:", error)
    throw error
  }
}

export async function updateCountry(
  countryId: string,
  countryData: Partial<{
    name: string;
    code: string;
    currency_code: string;
    currency_prefix: string;
    is_active: boolean;
    flag_image: string;
  }>
) {
  try {
    const supabase = await createClient();
    // Prepare update data
    const updateData: any = {
      ...countryData,
      updated_at: new Date().toISOString(),
    }

    // Ensure codes are uppercase if provided
    if (updateData.code) {
      updateData.code = updateData.code.toUpperCase()
    }
    if (updateData.currency_code) {
      updateData.currency_code = updateData.currency_code.toUpperCase()
    }

    // Update country
    const { error } = await supabase
      .from("supported_countries")
      .update(updateData)
      .eq("id", countryId)

    if (error) {
      throw new Error(error.message)
    }

    revalidatePath("/admin/settings")
    return { success: true }
  } catch (error) {
    console.error("Error updating country:", error)
    throw error
  }
}

export async function deleteCountry(countryId: string) {
  try {
    const supabase = await createClient();
    // Delete country
    const { error } = await supabase
      .from("supported_countries")
      .delete()
      .eq("id", countryId)

    if (error) {
      throw new Error(error.message)
    }

    revalidatePath("/admin/settings")
    return { success: true }
  } catch (error) {
    console.error("Error deleting country:", error)
    throw error
  }
}

export async function getCarListingUpdates(carId: string) {
  try {
    const supabase = await createClient();
    // Get car update history using type assertion to bypass TypeScript limitations with dynamic tables
    const { data: updates, error } = await (supabase
      .from("car_listing_updates" as any)
      .select(`
        id,
        car_id,
        new_status,
        updated_by,
        reason,
        updated_at,
        profiles:updated_by (
          id,
          first_name,
          last_name,
          avatar_url
        )
      `)
      .eq("car_id", carId)
      .order('updated_at', { ascending: false }));

    if (error) {
      throw new Error(`Error fetching car listing updates: ${error.message}`);
    }

    // Format the data for better usability in the UI
    return updates.map((update: any) => ({
      id: update.id,
      car_id: update.car_id,
      status: update.new_status,
      reason: update.reason || '',
      updated_at: update.updated_at,
      admin: {
        id: update.profiles?.id || '',
        name: `${update.profiles?.first_name || ''} ${update.profiles?.last_name || ''}`.trim() || 'Unknown Admin',
        avatar_url: update.profiles?.avatar_url || ''
      }
    }));
  } catch (error) {
    console.error("Error fetching car listing updates:", error);
    throw error;
  }
}

export async function getAdminBookingsWithFilters(
  page = 1,
  limit = 20,
  filters: {
    status?: BookingStatus | "all";
    search?: string;
    startDate?: string;
    endDate?: string;
    country?: string;
  } = {}
): Promise<{ bookings: BookingResponse[]; hasMore: boolean; total: number }> {
  try {
    const supabase = await createClient();
    
    // Prepare query
    let bookingsQuery = supabase.from("vw_filtered_bookings").select(`
      id,
      host,
      year,
      brand_name,
      start_date,
      end_date,
      status,
      currency_details,
      total_amount,
      created_at
    `)

    let countQuery = supabase.from("vw_filtered_bookings").select('*', { count: 'exact', head: true })
    
    // Apply filters
    if (filters.status && ["pending", "confirmed", "completed", "cancelled"].includes(filters.status)) {
      countQuery = countQuery.eq('status', filters.status as BookingStatus);
      bookingsQuery = bookingsQuery.eq('status', filters.status as BookingStatus);
    }
    
    // TODO: Add search functionality
    if (filters.search?.trim() !== '' && filters.search !== undefined) {
      // TODO: Add search functionality
      console.log("Add search filter", filters.search)
    }
    
    if (filters.country) {
      countQuery = countQuery.eq('currency_details->>countryid', filters.country);
      bookingsQuery = bookingsQuery.eq('currency_details->>countryid', filters.country);
    }

    // Get count first
    const { count, error: countError } = await countQuery
    
    if (countError && countError?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) {
      throw new Error(`Error fetching bookings count`);
    }
    
    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    
    // Get paginated data
    const { data: bookings, error } = (await bookingsQuery.order('created_at', { ascending: false }).range(from, to)) as unknown as SupabaseResponse<BookingResponse[]>

    if (error && error?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) {
      throw new Error(`Error fetching bookings: ${error.message}`);
    }
    
    return {
      bookings: bookings || [],
      hasMore: count ? from + bookings?.length < count : false,
      total: count || 0
    };
  } catch (error) {
    console.error("Error fetching bookings with filters:", error);
    throw error;
  }
}

export async function getBookingById(bookingId: string) {
  try {
    const supabase = await createClient();
    // Get booking details
    const { data: booking, error } = await supabase
      .from("vw_filtered_bookings")
      .select('*')
      .eq('id', bookingId)
      .single();
    
    if (error && error?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) {
      throw new Error(`Error fetching booking: ${error.message}`);
    }
    
    if (!booking) {
      return null;
    }
    
    return booking;
  } catch (error) {
    console.error("Error fetching booking by ID:", error);
    throw error;
  }
}

export async function getBookingPayments({
  bookingId,
  paymentType
}: {
  bookingId: string,
  paymentType?: PaymentType
}): Promise<BookingPayment[]> {
  try {
    const supabase = await createClient();
    if (paymentType) {
			// Use the database function to get payments of a specific type
			const { data, error } = await supabase  
				.rpc('get_booking_payments_by_type', {
					p_booking_id: bookingId,
					p_payment_type: paymentType
				}) as unknown as SupabaseResponse<BookingPayment[]>

			if (error && error?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) throw error;
			return data || [];
		} else {
			// Use the database function to get all payments
			const { data, error } = await supabase
				.rpc('get_booking_payments_by_type', {
					p_booking_id: bookingId,
					p_payment_type: undefined
				}) as unknown as SupabaseResponse<BookingPayment[]>

			if (error && error?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) throw error;
			return data || [];
		}
  } catch (error) {
    console.error("Error fetching booking payments:", error);
    throw error;
  }
};

/**
 * Gets a renter profile by ID - for admin use only
 */
export async function getRenterProfile(renterId: string): Promise<UserProfile | null> {
  try {
    const supabase = await createClient();
    // Get renter details
    const { data: renterProfile, error } = (await supabase
      .from("user_profiles_view")
      .select(`*`)
      .eq("id", renterId)
      .single()) as unknown as SupabaseResponse<UserProfile>
    
    if (error && error?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) {
      throw new Error(`Error fetching renter profile: ${error.message}`);
    }
    
    if (!renterProfile) {
      return null;
    }
    
    return renterProfile;
  } catch (error) {
    console.error("Error fetching renter profile:", error);
    throw error;
  }
}

export async function getBookingIssues(bookingId: string): Promise<BookingIssue[]> {
  try {
    const supabase = await createClient();
    // Get booking issues
    const { data: bookingIssues, error } = (await supabase
      .from("booking_issues")
      .select(`id, reporter_id, issue_type, description, created_at, updated_at, resolved_at, status`)
      .eq("booking_id", bookingId)) as unknown as SupabaseResponse<BookingIssue[]>

    if (error && error?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) {
      throw new Error(`Error fetching booking issues: ${error.message}`);
    }

    return bookingIssues;
  } catch (error) {
    console.error("Error fetching booking issues:", error);
    throw error;
  }
}

export async  function fetchBookingMoodificationRequests(bookingId: string): Promise<BookingModificationRequest[]> {
  try {
    const supabase = await createClient();
    // Get booking modification requests
    const { data: bookingModificationRequests, error } = (await supabase
      .from("booking_modification_requests_view")
      .select('*')
      .eq("booking_id", bookingId)
      .order('created_at', { ascending: false })) as unknown as SupabaseResponse<BookingModificationRequest[]>

    if (error && error?.code !== SupabaseErrorCodes.NO_ITEMS_FOUND) {
      throw new Error(`Error fetching booking modification requests: ${error.message}`);
    }

    return bookingModificationRequests;
  } catch (error) {
    console.error("Error fetching booking modification requests:", error);
    throw error;
  }
}

export async function getSupportTicketsSummary(): Promise<SupportTicket[]> {
  try {
    const supabase = await createClient();
    // Get support tickets summary
    const { data: supportTickets, error } = await supabase
      .from('support_tickets_summary').select('*');
    
    if (error) {
      console.log("Error fetching support tickets summary:", error);
      throw new Error(`Error fetching support tickets: ${error.message}`);
    }
    
    return supportTickets || [];
  } catch (error) {
    console.error("Error fetching support tickets summary:", error);
    throw error;
  }
}

export async function getSupportTicketMessageById(messageId: string): Promise<SupportTicketMessage> {
  try {
    const supabase = await createClient();
    // Get support ticket message by ID
    const { data, error } = await supabase
      .rpc('get_support_message_by_id', { p_message_id: messageId}) as unknown as SupabaseResponse<SupportTicketMessage>

    if (error) {
      console.log("Error fetching support ticket message by ID:", error);
      throw new Error(`Error fetching support ticket message: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Error fetching support ticket message by ID:", error);
    throw error;
  }
}

export async function getSupportTicketMessages(
  ticketId: string, 
  limit: number = 20, 
  offset: number = 0
): Promise<{ messages: SupportTicketMessage[], hasMore: boolean, total: number }> {
  try {
    const supabase = await createClient();
    // Get support ticket messages
    const { data, error } = await supabase
      .rpc('get_support_messages', {
        p_ticket_id: ticketId,
        p_limit: limit,
        p_offset: offset,
      }) as unknown as SupabaseResponse<SupportTicketMessageResponse>
    
    if (error) {
      console.log("Error fetching support ticket messages:", error);
      throw new Error(`Error fetching support ticket messages: ${error.message}`);
    }

    const { messages, hasMore, total } = data;

    console.log("Messages:", messages);

    return {
      messages: messages || [],
      hasMore: messages?.length === limit,
      total: total || 0
    };
  } catch (error) {
    console.error("Error fetching support ticket messages:", error);
    throw error;
  }
}

export async function getSupprtTicketMessageById(messageId: string): Promise<SupportTicketMessage> {
  try {
    const supabase = await createClient();

    const response = await supabase
      .rpc('get_support_message_by_id', messageId) as unknown as SupabaseResponse<SupportTicketMessage>;

    console.log("Found message details:", response);

    return response.data;

  } catch (error) {
    console.error("Error fetching support ticket message by ID:", error);
    throw error;
  }
}

export async function sendSupportTicketMessage(
  ticketId: string,
  message: string
) {
  try {
    const supabase = await createClient();
    // Insert new message
    const response = await supabase
      .rpc('add_support_message', {
        p_ticket_id: ticketId,
        p_content: message,
        p_is_support: true
      })
      
    if (response.error) {
      throw new Error(`Error sending support ticket message: ${response.error?.message}`);
    }
    
    return { success: true };
  } catch (error) {
    console.error("Error sending support ticket message:", error);
    throw error;
  }
}

export async function updateSupportTicketStatus(
  ticketId: string,
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
) {
  try {
    const supabase = await createClient();
    // Update ticket status
    const { error } = await supabase
      .from('support_tickets')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', ticketId)
    
    if (error) {
      throw new Error(`Error updating support ticket status: ${error.message}`);
    }
    
    return { success: true };
  } catch (error) {
    console.error("Error updating support ticket status:", error);
    throw error;
  }
}
