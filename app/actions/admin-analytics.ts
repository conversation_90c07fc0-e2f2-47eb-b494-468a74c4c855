"use server"

// Server-side analytics functions
import { createClient } from "@/lib/supabase-server"

export async function getAdminAnalytics() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    throw new Error("Unauthorized")
  }

  // Get user profile to check role
  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", user.id)
    .single();

  // Check if the user is an admin
  const isAdmin = profile && profile.role === "admin";

  if (!isAdmin) {
    throw new Error("Unauthorized")
  }

  try {
    // ==================== USER ANALYTICS ====================
    // Get total users count
    const { count: totalUsers, error: usersError } = await supabase
      .from("profiles")
      .select("*", { count: "exact", head: true })

    if (usersError) {
      console.error("Error fetching total users:", usersError)
    }

    // For active users, we'll simply count users who have logged in at least once
    // Since we don't have reliable last_sign_in data, we'll estimate based on profiles with names
    let activeUsers = 0;
    try {
      const { data: activeProfilesData } = await supabase
        .from("profiles")
        .select("id")
        .not("first_name", "is", null)

      activeUsers = activeProfilesData?.length || 0;
    } catch (error) {
      console.error("Error fetching active users:", error)
    }

    // ==================== CAR ANALYTICS ====================
    // Get total cars count
    const { count: totalCars, error: carsError } = await supabase
      .from("car_listings")
      .select("*", { count: "exact", head: true })

    if (carsError) {
      console.error("Error fetching total cars:", carsError)
    }

    // Get cars by status
    const { data: carStatusData, error: carStatusError } = await supabase
      .from("car_listings")
      .select("status")

    if (carStatusError) {
      console.error("Error fetching car status data:", carStatusError)
    }

    // Count cars by status
    const carStatusCount = {
      active: 0,
      pending: 0,
      rejected: 0,
      other: 0
    }

    carStatusData?.forEach(car => {
      if (car.status === "active") carStatusCount.active++
      else if (car.status === "pending") carStatusCount.pending++
      else if (car.status === "rejected") carStatusCount.rejected++
      else carStatusCount.other++
    })

    // Get popular car brands
    const { data: carBrandsData, error: carBrandsError } = await supabase
      .from("filtered_cars")
      .select("brand")
      .not("brand", "is", null)

    if (carBrandsError) {
      console.error("Error fetching car brands:", carBrandsError)
    }

    // Count car brands
    const carBrandsCount: Record<string, number> = {}
    carBrandsData?.forEach(car => {
      if (car.brand) {
        carBrandsCount[car.brand] = (carBrandsCount[car.brand] || 0) + 1
      }
    })

    // Format popular car brands for chart (top 5)
    const popularCarBrands = Object.entries(carBrandsCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([brand, count]) => ({
        brand,
        count,
      }))

    // Get pending approvals count
    const { count: pendingApprovals, error: pendingError } = await supabase
      .from("car_listings")
      .select("*", { count: "exact", head: true })
      .eq("status", "pending")

    if (pendingError) {
      console.error("Error fetching pending approvals:", pendingError)
    }

    // ==================== BOOKING ANALYTICS ====================
    // Get total bookings count
    const { count: totalBookings, error: bookingsError } = await supabase
      .from("bookings")
      .select("*", { count: "exact", head: true })

    if (bookingsError) {
      console.error("Error fetching total bookings:", bookingsError)
    }

    // Get bookings by status
    const { data: bookingStatusData, error: bookingStatusError } = await supabase
      .from("bookings")
      .select("status")

    if (bookingStatusError) {
      console.error("Error fetching booking status data:", bookingStatusError)
    }

    // Count bookings by status
    const bookingStatusCount = {
      pending: 0,
      confirmed: 0,
      completed: 0,
      cancelled: 0,
      ongoing: 0
    }

    bookingStatusData?.forEach(booking => {
      if (booking.status === "pending") bookingStatusCount.pending++
      else if (booking.status === "confirmed") bookingStatusCount.confirmed++
      else if (booking.status === "completed") bookingStatusCount.completed++
      else if (booking.status === "cancelled") bookingStatusCount.cancelled++
      else if (booking.status === "ongoing") bookingStatusCount.ongoing++
    })

    // ==================== REVENUE ANALYTICS ====================
    // Get total revenue from bookings using the filtered view
    const { data: bookingsData, error: bookingsDataError } = await supabase
      .from("vw_filtered_bookings")
      .select("total_amount")
      .in("status", ["confirmed", "completed"])

    if (bookingsDataError) {
      console.error("Error fetching bookings data:", bookingsDataError)
    }

    // For simplicity, we'll use USD as the default currency
    const revenueByCurrency: Record<string, number> = { "USD": 0 }
    bookingsData?.forEach(booking => {
      revenueByCurrency["USD"] = (revenueByCurrency["USD"] || 0) + (booking.total_amount || 0)
    })

    // Calculate total revenue
    const totalRevenue = bookingsData?.reduce((sum, booking) => sum + (booking.total_amount || 0), 0) || 0

    // ==================== MONTHLY DATA ====================
    // Get monthly booking stats for the current year
    const currentYear = new Date().getFullYear()
    const { data: monthlyBookings, error: monthlyBookingsError } = await supabase
      .from("vw_filtered_bookings")
      .select("created_at, total_amount, status")
      .gte("created_at", `${currentYear}-01-01`)
      .lte("created_at", `${currentYear}-12-31`)

    if (monthlyBookingsError) {
      console.error("Error fetching monthly bookings:", monthlyBookingsError)
    }

    // Get monthly user registrations for the current year
    const { data: monthlyUsers, error: monthlyUsersError } = await supabase
      .from("profiles")
      .select("created_at")
      .gte("created_at", `${currentYear}-01-01`)
      .lte("created_at", `${currentYear}-12-31`)

    if (monthlyUsersError) {
      console.error("Error fetching monthly users:", monthlyUsersError)
    }

    // Get monthly car listings for the current year
    const { data: monthlyCars, error: monthlyCarsError } = await supabase
      .from("car_listings")
      .select("created_at, status")
      .gte("created_at", `${currentYear}-01-01`)
      .lte("created_at", `${currentYear}-12-31`)

    if (monthlyCarsError) {
      console.error("Error fetching monthly cars:", monthlyCarsError)
    }

    // Process monthly data
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    const bookingStats = Array(12).fill(0).map((_, i) => ({
      date: `${currentYear}-${String(i + 1).padStart(2, '0')}-01`,
      count: 0,
      revenue: 0
    }))

    const userGrowth = Array(12).fill(0).map((_, i) => ({
      date: `${currentYear}-${String(i + 1).padStart(2, '0')}-01`,
      count: 0
    }))

    const carStats = Array(12).fill(0).map((_, i) => ({
      date: `${currentYear}-${String(i + 1).padStart(2, '0')}-01`,
      count: 0,
      active: 0,
      pending: 0
    }))

    // Process monthly bookings
    monthlyBookings?.forEach(booking => {
      if (booking.created_at) {
        const date = new Date(booking.created_at)
        const monthIndex = date.getMonth()

        bookingStats[monthIndex].count++

        // Only count revenue for confirmed or completed bookings
        if (booking.status === 'confirmed' || booking.status === 'completed') {
          bookingStats[monthIndex].revenue += booking.total_amount || 0
        }
      }
    })

    // Process monthly user registrations
    monthlyUsers?.forEach(user => {
      if (user.created_at) {
        const date = new Date(user.created_at)
        const monthIndex = date.getMonth()
        userGrowth[monthIndex].count++
      }
    })

    // Process monthly car listings
    monthlyCars?.forEach(car => {
      if (car.created_at) {
        const date = new Date(car.created_at)
        const monthIndex = date.getMonth()

        carStats[monthIndex].count++

        if (car.status === 'active') {
          carStats[monthIndex].active++
        } else if (car.status === 'pending') {
          carStats[monthIndex].pending++
        }
      }
    })

    // ==================== USER TYPES ANALYTICS ====================
    // Get user types distribution
    const { data: userTypesData, error: userTypesError } = await supabase
      .from("profiles")
      .select("role")

    if (userTypesError) {
      console.error("Error fetching user types:", userTypesError)
    }

    // Count user types
    const userTypes = [
      { type: "Car Owners", count: 0 },
      { type: "Renters", count: 0 },
      { type: "Admins", count: 0 }
    ]

    userTypesData?.forEach(user => {
      if (user.role === "admin") userTypes[2].count++
      else if (user.role === "car_owner") userTypes[0].count++
      else userTypes[1].count++ // Default to renter
    })

    // ==================== LOCATION ANALYTICS ====================
    // Get top locations from filtered_cars view
    // Try to get location data from the view
    let locationsData: any[] = [];
    try {
      // First try with main_location field
      const { data, error } = await supabase
        .from("filtered_cars")
        .select("main_location")
        .not("main_location", "is", null)

      if (error) {
        // If that fails, try with location field
        const { data: altData, error: altError } = await supabase
          .from("filtered_cars")
          .select("location")
          .not("location", "is", null)

        if (altError) {
          console.error("Error fetching locations:", altError)
        } else {
          locationsData = altData
        }
      } else {
        locationsData = data
      }
    } catch (error) {
      console.error("Error fetching locations:", error)
    }

    // Count locations
    const locationsCount: Record<string, number> = {}
    locationsData.forEach(car => {
      // Check if we have main_location or location
      const locationField = car.main_location || car.location

      if (locationField) {
        let locationName = "Unknown"

        try {
          // Try to parse the location if it's a JSON string
          if (typeof locationField === 'string') {
            const locationObj = JSON.parse(locationField)
            locationName = locationObj.place_name || locationObj.formatted_address || "Unknown"
          } else if (typeof locationField === 'object') {
            // Handle object type safely
            const locationObj = locationField as any
            locationName = locationObj.place_name || locationObj.formatted_address || "Unknown"
          }
        } catch (e) {
          // If parsing fails, use the raw value
          locationName = typeof locationField === 'string' ? locationField : "Unknown"
        }

        locationsCount[locationName] = (locationsCount[locationName] || 0) + 1
      }
    })

    // Format locations for chart (top 5)
    const topLocations = Object.entries(locationsCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([location, count]) => ({
        location,
        count,
      }))

    // ==================== POPULAR CARS ANALYTICS ====================
    // Get popular car models from vw_filtered_bookings view
    const { data: popularCarsData, error: popularCarsError } = await supabase
      .from("vw_filtered_bookings")
      .select("car_id, brand, model")
      .not("car_id", "is", null)

    if (popularCarsError) {
      console.error("Error fetching popular cars:", popularCarsError)
    }

    // Count bookings per car model
    const carModelBookingsCount: Record<string, number> = {}
    const carModelDetails: Record<string, { brand: string, model: string }> = {}

    popularCarsData?.forEach(booking => {
      if (booking.car_id && booking.brand && booking.model) {
        const carKey = `${booking.brand} ${booking.model}`
        carModelBookingsCount[carKey] = (carModelBookingsCount[carKey] || 0) + 1
        carModelDetails[carKey] = { brand: booking.brand, model: booking.model }
      }
    })

    // Format popular cars for chart (top 5)
    const popularCars = Object.entries(carModelBookingsCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([carKey, bookings]) => ({
        make: carModelDetails[carKey]?.brand || "Unknown",
        model: carModelDetails[carKey]?.model || "Unknown",
        bookings,
      }))

    // Format revenue by month for chart
    const revenueByMonth = months.map((month, index) => ({
      month,
      revenue: bookingStats[index].revenue,
    }))

    // ==================== RATINGS ANALYTICS ====================
    // Get ratings data
    const { data: ratingsData, error: ratingsError } = await supabase
      .from("ratings")
      .select("accuracy_rating, communication_rating, convenience_rating, maintenance_rating")

    if (ratingsError) {
      console.error("Error fetching ratings:", ratingsError)
    }

    // Calculate average ratings
    let totalAccuracy = 0
    let totalCommunication = 0
    let totalConvenience = 0
    let totalMaintenance = 0
    let ratingsCount = 0

    ratingsData?.forEach(rating => {
      totalAccuracy += rating.accuracy_rating || 0
      totalCommunication += rating.communication_rating || 0
      totalConvenience += rating.convenience_rating || 0
      totalMaintenance += rating.maintenance_rating || 0
      ratingsCount++
    })

    const averageRatings = {
      accuracy: ratingsCount > 0 ? totalAccuracy / ratingsCount : 0,
      communication: ratingsCount > 0 ? totalCommunication / ratingsCount : 0,
      convenience: ratingsCount > 0 ? totalConvenience / ratingsCount : 0,
      maintenance: ratingsCount > 0 ? totalMaintenance / ratingsCount : 0,
      overall: ratingsCount > 0 ? (totalAccuracy + totalCommunication + totalConvenience + totalMaintenance) / (ratingsCount * 4) : 0
    }

    // Format ratings for chart
    const ratingsChart = [
      { category: "Accuracy", value: averageRatings.accuracy },
      { category: "Communication", value: averageRatings.communication },
      { category: "Convenience", value: averageRatings.convenience },
      { category: "Maintenance", value: averageRatings.maintenance },
      { category: "Overall", value: averageRatings.overall }
    ]

    // Calculate growth rates
    const userGrowthRate = calculateGrowthRate(userGrowth)
    const bookingGrowthRate = calculateGrowthRate(bookingStats, 'count')
    const revenueGrowthRate = calculateGrowthRate(bookingStats, 'revenue')
    const carGrowthRate = calculateGrowthRate(carStats)

    return {
      overview: {
        totalUsers: totalUsers || 0,
        totalCars: totalCars || 0,
        totalBookings: totalBookings || 0,
        totalRevenue: totalRevenue,
        revenueByCurrency,
        activeUsers: activeUsers || 0,
        pendingApprovals: pendingApprovals || 0,
        userGrowthRate,
        bookingGrowthRate,
        revenueGrowthRate,
        carGrowthRate,
        averageRatings
      },
      userAnalytics: {
        userGrowth,
        userTypes,
        userGrowthRate
      },
      carAnalytics: {
        carStats,
        carStatusTypes: [
          { type: "Active", count: carStatusCount.active },
          { type: "Pending", count: carStatusCount.pending },
          { type: "Rejected", count: carStatusCount.rejected },
          { type: "Other", count: carStatusCount.other }
        ],
        carGrowthRate,
        popularCarBrands: popularCarBrands || []
      },
      bookingAnalytics: {
        bookingStats,
        bookingStatusTypes: [
          { type: "Pending", count: bookingStatusCount.pending },
          { type: "Confirmed", count: bookingStatusCount.confirmed },
          { type: "Completed", count: bookingStatusCount.completed },
          { type: "Cancelled", count: bookingStatusCount.cancelled },
          { type: "Ongoing", count: bookingStatusCount.ongoing }
        ],
        bookingGrowthRate
      },
      revenueAnalytics: {
        revenueByMonth,
        revenueByCurrency,
        revenueGrowthRate
      },
      locationAnalytics: {
        topLocations: topLocations.length > 0 ? topLocations : [
          { location: "No location data", count: 0 }
        ]
      },
      popularCars: popularCars.length > 0 ? popularCars : [
        { make: "No car data", model: "", bookings: 0 }
      ],
      ratingsAnalytics: {
        ratingsChart,
        averageRatings
      }
    }
  } catch (error) {
    console.error("Error in getAdminAnalytics:", error)
    // Return default data in case of error
    return {
      overview: {
        totalUsers: 0,
        totalCars: 0,
        totalBookings: 0,
        totalRevenue: 0,
        revenueByCurrency: {},
        activeUsers: 0,
        pendingApprovals: 0,
        userGrowthRate: 0,
        bookingGrowthRate: 0,
        revenueGrowthRate: 0,
        carGrowthRate: 0,
        averageRatings: {
          accuracy: 0,
          communication: 0,
          convenience: 0,
          maintenance: 0,
          overall: 0
        }
      },
      userAnalytics: {
        userGrowth: [],
        userTypes: [],
        userGrowthRate: 0
      },
      carAnalytics: {
        carStats: [],
        carStatusTypes: [],
        carGrowthRate: 0,
        popularCarBrands: []
      },
      bookingAnalytics: {
        bookingStats: [],
        bookingStatusTypes: [],
        bookingGrowthRate: 0
      },
      revenueAnalytics: {
        revenueByMonth: [],
        revenueByCurrency: {},
        revenueGrowthRate: 0
      },
      locationAnalytics: {
        topLocations: []
      },
      popularCars: [],
      ratingsAnalytics: {
        ratingsChart: [],
        averageRatings: {
          accuracy: 0,
          communication: 0,
          convenience: 0,
          maintenance: 0,
          overall: 0
        }
      }
    }
  }
}

// Helper function to calculate growth rate
function calculateGrowthRate(data: any[], valueKey: string = 'count') {
  if (!data || data.length < 2) return 0

  // Get the sum of the last 3 months
  const lastThreeMonths = data.slice(-3)
  const lastThreeMonthsSum = lastThreeMonths.reduce((sum, item) => sum + (valueKey ? item[valueKey] : item), 0)

  // Get the sum of the 3 months before that
  const previousThreeMonths = data.slice(-6, -3)
  const previousThreeMonthsSum = previousThreeMonths.reduce((sum, item) => sum + (valueKey ? item[valueKey] : item), 0)

  // Calculate growth rate
  if (previousThreeMonthsSum === 0) return lastThreeMonthsSum > 0 ? 100 : 0

  return Math.round(((lastThreeMonthsSum - previousThreeMonthsSum) / previousThreeMonthsSum) * 100)
}
