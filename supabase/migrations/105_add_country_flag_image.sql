-- Add flag_image column to supported_countries table
ALTER TABLE IF EXISTS public.supported_countries
ADD COLUMN flag_image TEXT;

-- Check if countries bucket exists and create it if it doesn't
DO $$
BEGIN
  -- Check if the bucket exists
  IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'countries') THEN
    -- Create the bucket
    INSERT INTO storage.buckets (id, name)
    VALUES ('countries', 'countries');
  END IF;
END $$;

-- Create a function to check if a user is an admin
CREATE OR REPLACE FUNCTION public.is_admin() 
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policy to allow only admins to upload country flags
CREATE POLICY "Allow admins to upload country flags"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'countries' AND
  public.is_admin()
);

-- Update RLS policy to allow admins to update country flags
CREATE POLICY "Allow admins to update country flags"
ON storage.objects
FOR UPDATE
TO authenticated
USING (bucket_id = 'countries' AND public.is_admin())
WITH CHECK (bucket_id = 'countries' AND public.is_admin());

-- Update RLS policy to allow admins to delete country flags
CREATE POLICY "Allow admins to delete country flags"
ON storage.objects
FOR DELETE
TO authenticated
USING (bucket_id = 'countries' AND public.is_admin());

-- Keep public read access for flags
CREATE POLICY "Allow public to view country flags"
ON storage.objects
FOR SELECT
TO public
USING (bucket_id = 'countries'); 