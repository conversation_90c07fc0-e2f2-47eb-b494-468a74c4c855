-- Create settings table
CREATE TABLE IF NOT EXISTS public.settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  site_name TEXT NOT NULL DEFAULT 'Travella',
  site_description TEXT,
  contact_email TEXT,
  support_phone TEXT,
  currency TEXT DEFAULT 'USD',
  booking_fee_percentage NUMERIC DEFAULT 10,
  
  -- Email settings
  smtp_host TEXT,
  smtp_port INTEGER DEFAULT 587,
  smtp_user TEXT,
  smtp_password TEXT,
  smtp_from_email TEXT,
  smtp_from_name TEXT,
  enable_email_notifications BOOLEAN DEFAULT TRUE,
  
  -- Payment settings
  payment_gateway TEXT DEFAULT 'stripe',
  stripe_public_key TEXT,
  stripe_secret_key TEXT,
  paypal_client_id TEXT,
  paypal_secret_key TEXT,
  flutterwave_public_key TEXT,
  flutterwave_secret_key TEXT,
  flutterwave_encryption_key TEXT,
  
  -- Notification settings
  enable_sms_notifications BOOLEAN DEFAULT FALSE,
  enable_push_notifications BOOLEAN DEFAULT FALSE,
  notify_admin_on_new_booking BOOLEAN DEFAULT TRUE,
  notify_admin_on_new_user BOOLEAN DEFAULT TRUE,
  notify_admin_on_new_car BOOLEAN DEFAULT TRUE,
  notify_owner_on_new_booking BOOLEAN DEFAULT TRUE,
  notify_renter_on_booking_status BOOLEAN DEFAULT TRUE,
  booking_confirmation_template TEXT,
  booking_cancellation_template TEXT,
  welcome_email_template TEXT,
  
  -- User settings
  allow_user_registration BOOLEAN DEFAULT TRUE,
  default_user_role TEXT DEFAULT 'renter',
  require_email_verification BOOLEAN DEFAULT TRUE,
  maintenance_mode BOOLEAN DEFAULT FALSE,
  maintenance_message TEXT,
  max_cars_per_owner INTEGER DEFAULT 10,
  max_bookings_per_user INTEGER DEFAULT 5,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Only allow admins to read settings
CREATE POLICY "Allow admins to read settings" 
  ON public.settings 
  FOR SELECT 
  USING (
    auth.uid() IN (
      SELECT id FROM public.profiles WHERE role = 'admin'
    )
  );

-- Only allow admins to insert settings
CREATE POLICY "Allow admins to insert settings" 
  ON public.settings 
  FOR INSERT 
  WITH CHECK (
    auth.uid() IN (
      SELECT id FROM public.profiles WHERE role = 'admin'
    )
  );

-- Only allow admins to update settings
CREATE POLICY "Allow admins to update settings" 
  ON public.settings 
  FOR UPDATE 
  USING (
    auth.uid() IN (
      SELECT id FROM public.profiles WHERE role = 'admin'
    )
  );

-- Create a function to create the settings table if it doesn't exist
CREATE OR REPLACE FUNCTION public.create_settings_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- This function is a no-op since we're creating the table in the migration
  -- But we keep it for compatibility with the code that calls it
  RETURN;
END;
$$;

-- Insert default settings if the table is empty
INSERT INTO public.settings (
  site_name, 
  site_description, 
  contact_email, 
  currency,
  booking_confirmation_template,
  booking_cancellation_template,
  welcome_email_template
)
SELECT 
  'Travella', 
  'Car rental platform', 
  '<EMAIL>', 
  'USD',
  'Dear {{renter_name}},\n\nYour booking for {{car_name}} has been confirmed. Your booking ID is {{booking_id}}.\n\nPickup: {{pickup_date}} at {{pickup_time}}\nReturn: {{return_date}} at {{return_time}}\n\nThank you for choosing our service.\n\nBest regards,\nThe Team',
  'Dear {{renter_name}},\n\nYour booking for {{car_name}} has been cancelled. Your booking ID was {{booking_id}}.\n\nReason: {{cancellation_reason}}\n\nIf you have any questions, please contact our support team.\n\nBest regards,\nThe Team',
  'Dear {{user_name}},\n\nWelcome to our platform! We''re excited to have you on board.\n\nYou can now browse and book cars for your next trip.\n\nIf you have any questions, please don''t hesitate to contact our support team.\n\nBest regards,\nThe Team'
WHERE NOT EXISTS (SELECT 1 FROM public.settings);
