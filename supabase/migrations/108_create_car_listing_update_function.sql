BEGIN;

DROP TABLE IF EXISTS public.car_listing_updates;

-- Create a new table to track car listing update history
CREATE TABLE public.car_listing_updates (
    id SERIAL PRIMARY KEY,
    car_id UUID NOT NULL,
    new_status TEXT NOT NULL,
    updated_by UUID NOT NULL,
    reason TEXT,
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create a function to update car status and log the update
CREATE OR REPLACE FUNCTION public.update_car_status(
    car_id UUID,
    new_status TEXT::car_listing_status,
    reason TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    -- Update the car status
    UPDATE public.car_listings
    SET status = new_status::car_listing_status, updated_at = now()
    WHERE id = car_id;

    -- Insert a record into car_listing_updates
    INSERT INTO public.car_listing_updates (car_id, new_status, updated_by, reason)
    VALUES (car_id, new_status::car_listing_status, auth.uid(), reason);
END;
$$ LANGUAGE plpgsql;

COMMIT; 