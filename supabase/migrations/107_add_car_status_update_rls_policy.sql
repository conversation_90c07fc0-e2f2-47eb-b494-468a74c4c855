BEGIN;

-- First, ensure R<PERSON> is enabled on the car_listings table
ALTER TABLE public.car_listings ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies with similar names to avoid conflicts
DROP POLICY IF EXISTS restrict_car_status_update_policy ON public.car_listings;

-- Create a policy that restricts updates to the status field to admin users only
CREATE POLICY restrict_car_status_update_policy
ON public.car_listings
FOR UPDATE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = (select auth.uid())
        AND role = 'admin'
    )
);

-- Add a clear comment to explain the policy
COMMENT ON POLICY restrict_car_status_update_policy ON public.car_listings IS 'Only users with admin role can update the status field of car listings';

-- Add a comment to the status field in car_listings to indicate admin-only updates
COMMENT ON COLUMN public.car_listings.status IS 'Car status - can only be updated by admin users';

COMMIT;

-- Note: This complements the trigger approach in migration 092 by adding RLS protection
-- R<PERSON> is evaluated before triggers, providing an additional layer of security
-- This ensures that even if the trigger is accidentally dropped, the policy will still be in effect