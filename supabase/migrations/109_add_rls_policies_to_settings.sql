BEGIN;

-- Enable R<PERSON> on the settings table
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS allow_admin_manage_settings ON public.settings;
DROP POLICY IF EXISTS allow_authenticated_read_settings ON public.settings;

-- Create a policy to allow only admins to manage settings
CREATE POLICY allow_admin_manage_settings
ON public.settings
FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = (select auth.uid())
        AND role = 'admin'
    )
);

-- Create a policy to allow authenticated users to read settings
CREATE POLICY allow_authenticated_read_settings
ON public.settings
FOR SELECT
TO authenticated
USING (true);

COMMIT; 