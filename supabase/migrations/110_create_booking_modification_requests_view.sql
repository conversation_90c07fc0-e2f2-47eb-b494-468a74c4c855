CREATE VIEW public.booking_modification_requests_view 
WITH (security_invoker = on) AS 
SELECT 
    bmr.*,
    jsonb_build_object(
        'first_name', requester.first_name,
        'last_name', requester.last_name,
        'avatar_url', requester.avatar_url
    ) AS requester_details,
    jsonb_build_object(
        'first_name', host.first_name,
        'last_name', host.last_name,
        'avatar_url', host.avatar_url
    ) AS host_details
FROM 
    public.booking_modification_requests bmr
JOIN 
    public.profiles requester ON bmr.requester_id = requester.id
JOIN 
    public.profiles host ON bmr.host_id = host.id;
    

DROP VIEW IF EXISTS public.user_profiles_view;

CREATE VIEW public.user_profiles_view AS 
SELECT 
    p.*,
    u.email,
    u.phone AS phone_number
FROM 
    public.profiles p
JOIN 
    auth.users u ON p.id = u.id;
