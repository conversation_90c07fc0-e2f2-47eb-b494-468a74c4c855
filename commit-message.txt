Improve analytics dashboard with real database data

This commit enhances the analytics dashboard by:

1. Refactoring the analytics dashboard into smaller, more maintainable components:
   - Created separate components for each tab (Overview, Users, Cars, Bookings, Revenue, Ratings)
   - Improved code organization and readability
   - Enhanced maintainability

2. Updating the analytics data fetching to use real database data:
   - Fixed queries to use the correct tables and views
   - Added proper error handling
   - Implemented fallbacks for missing data

3. Adding new analytics features:
   - Added ratings analytics
   - Enhanced location analytics
   - Improved revenue tracking
   - Added growth rate calculations

4. Improving the user experience:
   - Added loading states
   - Enhanced visualizations
   - Improved data formatting

These changes provide a more accurate and comprehensive view of the platform's performance using real data from the database.
