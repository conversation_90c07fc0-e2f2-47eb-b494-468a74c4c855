'use client'

import { createClient as createSupabaseClient } from '@supabase/supabase-js'

// Create a singleton Supabase client for use in the browser
let client: ReturnType<typeof createSupabaseClient> | null = null

export function createClient() {
  if (client) return client

  client = createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
      },
      // Configure real-time settings explicitly
      realtime: {
        params: {
          eventsPerSecond: 10,
        },
      },
      // Explicitly enable global auto-connect for realtime
      global: {
        headers: {
          'x-application-name': 'travella-admin',
        },
      },
    }
  )

  // Log when client is created in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Supabase client initialized with real-time support')
  }

  return client
} 