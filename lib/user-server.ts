import { redirect } from 'next/navigation';
import { createClient } from '@/lib/supabase-server';
import { cache } from 'react';

// Define the profile type
type Profile = {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  avatar_url?: string;
  role: 'admin' | 'car_owner' | 'renter';
  [key: string]: any;
};

// Type for the full user data
type UserData = {
  user: {
    id: string;
    email?: string;
    [key: string]: any;
  };
  profile: Profile | null;
  isAdmin: boolean;
  isCarOwner: boolean;
};

// Cache the user data fetching to reduce database calls
export const getUserData = cache(async (): Promise<UserData | null> => {
  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    return null;
  }
  
  // Get the user's profile with role information
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();
  
  // Check if user is admin
  const isAdmin = profile && profile.role === 'admin';
  
  // Check if user is car owner
  const isCarOwner = profile && profile.role === 'car_owner';
  
  return {
    user,
    profile,
    isAdmin,
    isCarOwner
  };
});

// Redirect based on authentication and role
export async function requireAuth(options?: { 
  adminOnly?: boolean,
  redirectTo?: string 
}) {
  const userData = await getUserData();
  
  // If not authenticated, redirect to login
  if (!userData) {
    redirect('/login');
  }
  
  // If admin only and user is not admin, redirect
  if (options?.adminOnly && !userData.isAdmin) {
    redirect(options.redirectTo || '/dashboard');
  }
  
  return userData;
}

// Redirect if authenticated (for login/signup pages)
export async function redirectIfAuthenticated() {
  const userData = await getUserData();
  
  if (userData) {
    // If admin, redirect to admin dashboard
    if (userData.isAdmin) {
      redirect('/admin');
    }
    
    // Otherwise, redirect to user dashboard
    redirect('/dashboard');
  }
  
  return null;
} 