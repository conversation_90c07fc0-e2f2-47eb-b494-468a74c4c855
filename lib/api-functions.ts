import { getRenterProfile as fetchRenterProfile } from '@/app/actions/admin/admin-actions';
import { User } from '@/types/users';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.travella.site';

/**
 * Get renter profile by ID
 * @param id - Renter ID
 * @returns User profile data
 */
export async function getRenterProfile(id: string): Promise<User> {
  try {
    const renterData = await fetchRenterProfile(id);
    return renterData as User;
  } catch (error) {
    console.error('Error fetching renter profile:', error);
    throw error;
  }
} 