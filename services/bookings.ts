import { getBookingById } from '@/app/actions/admin/admin-actions';
import { BookingResponse } from '@/types/bookings';

/**
 * Get booking details by ID
 * @param id - Booking ID
 * @returns Booking details with related data
 */
export async function getBookingDetails(id: string): Promise<{ booking: BookingResponse }> {
  try {
    const bookingData = await getBookingById(id);
    
    if (!bookingData) {
      throw new Error("Booking not found");
    }
    
    // Convert to unknown first to avoid TypeScript errors about missing fields
    return { booking: bookingData as unknown as BookingResponse };
  } catch (error) {
    console.error('Error fetching booking details:', error);
    throw error;
  }
} 