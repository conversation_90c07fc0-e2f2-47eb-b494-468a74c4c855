"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { AlertCircle, CheckCircle, Info, X } from "lucide-react"

import { cn } from "@/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive:
          "border-destructive/50 text-destructive bg-destructive/10 dark:border-destructive [&>svg]:text-destructive",
        success: "border-success/50 text-success bg-success/10 dark:border-success [&>svg]:text-success",
        info: "border-info/50 text-info bg-info/10 dark:border-info [&>svg]:text-info",
        warning:
          "border-secondary/50 text-secondary-700 bg-secondary-50 dark:border-secondary [&>svg]:text-secondary-600",
      },
      dismissible: {
        true: "pr-10",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      dismissible: false,
    },
  },
)

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> &
    VariantProps<typeof alertVariants> & {
      icon?: React.ReactNode
      onDismiss?: () => void
    }
>(({ className, variant, dismissible, icon, onDismiss, children, ...props }, ref) => {
  const [dismissed, setDismissed] = React.useState(false)

  if (dismissed) {
    return null
  }

  const handleDismiss = () => {
    setDismissed(true)
    onDismiss?.()
  }

  // Default icons based on variant
  let defaultIcon = null
  if (!icon) {
    if (variant === "destructive") {
      defaultIcon = <AlertCircle className="h-4 w-4" />
    } else if (variant === "success") {
      defaultIcon = <CheckCircle className="h-4 w-4" />
    } else if (variant === "info" || variant === "warning") {
      defaultIcon = <Info className="h-4 w-4" />
    }
  }

  return (
    <div ref={ref} role="alert" className={cn(alertVariants({ variant, dismissible }), className)} {...props}>
      {icon || defaultIcon}
      {children}
      {dismissible && (
        <button
          type="button"
          onClick={handleDismiss}
          className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          aria-label="Close alert"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </button>
      )}
    </div>
  )
})
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h5 ref={ref} className={cn("mb-1 font-medium leading-none tracking-tight", className)} {...props} />
  ),
)
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("text-sm [&_p]:leading-relaxed", className)} {...props} />
  ),
)
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription }

