"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

const PolicyNav = () => {
  const pathname = usePathname();
  
  const navItems = [
    { name: "Terms of Service", path: "/policies/terms" },
    { name: "Cancellation Policy", path: "/policies/cancellation" },
    { name: "Privacy Policy", path: "/policies/privacy" },
    { name: "Community Guidelines", path: "/policies/community-guidelines" },
    { name: "Nondiscrimination Policy", path: "/policies/nondiscrimination" },
    { name: "Support", path: "/policies/support" },
  ];

  return (
    <div className="space-y-2">
      {navItems.map((item) => (
        <Link
          key={item.path}
          href={item.path}
          className={cn(
            "block py-2 px-3 text-sm hover:bg-muted rounded-md transition-colors",
            pathname === item.path ? "bg-muted font-medium" : "text-muted-foreground"
          )}
        >
          {item.name}
        </Link>
      ))}
    </div>
  );
};

export default PolicyNav; 