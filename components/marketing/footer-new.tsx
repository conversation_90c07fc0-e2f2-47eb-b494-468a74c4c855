"use client"

import Link from "next/link"
import { Car, Facebook, Instagram, Twitter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button-new"
import Image from "next/image"
import { useTheme } from "next-themes"

export function MarketingFooter() {
  const currentYear = new Date().getFullYear()
  const { theme } = useTheme()
  const isDark = theme === "dark"

  return (<footer className="w-full py-12 md:py-16 lg:py-20 border-t">
    <div className="container px-4 md:px-6">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-8 lg:gap-12">
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Image src={isDark ? "/svg/travella_logo_light.svg" : "/svg/travella_logo.svg"} alt="Travella Logo" width={28} height={24} className="text-primary" />
            <span className="text-xl font-bold">Travella</span>
          </div>
          <p className="text-sm text-muted-foreground">
            Quality car rentals at affordable prices. Making car rental simple and accessible for everyone.
          </p>
          <div className="flex space-x-4">
            <Link href="https://www.facebook.com/detravella/" target="_blank" className="text-muted-foreground hover:text-foreground">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
              </svg>
            </Link>
            <Link href="#" className="text-muted-foreground hover:text-foreground">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
              </svg>
            </Link>
            <Link href="#" className="text-muted-foreground hover:text-foreground">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                <rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect>
                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                <line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line>
              </svg>
            </Link>
          </div>
        </div>
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Company</h4>
          <ul className="space-y-2 text-sm">
            <li><Link href="/about" className="text-muted-foreground hover:text-foreground">About Us</Link></li>
            <li><Link href="/careers" className="text-muted-foreground hover:text-foreground">Careers</Link></li>
            <li><Link href="/blog" className="text-muted-foreground hover:text-foreground">Blog</Link></li>
            <li><Link href="/press" className="text-muted-foreground hover:text-foreground">Press</Link></li>
          </ul>
        </div>
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Services</h4>
          <ul className="space-y-2 text-sm">
            <li><Link href="/car-rental" className="text-muted-foreground hover:text-foreground">Car Rental</Link></li>
            <li><Link href="/list-your-car" className="text-muted-foreground hover:text-foreground">List Your Car</Link></li>
            <li><Link href="/airport-pickup" className="text-muted-foreground hover:text-foreground">Airport Pickup</Link></li>
            <li><Link href="/corporate" className="text-muted-foreground hover:text-foreground">Corporate Services</Link></li>
          </ul>
        </div>
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Legal</h4>
          <ul className="space-y-2 text-sm">
            <li><Link href="/policies/terms" className="text-muted-foreground hover:text-foreground">Terms of Service</Link></li>
            <li><Link href="/policies/privacy" className="text-muted-foreground hover:text-foreground">Privacy Policy</Link></li>
            <li><Link href="/policies/cancellation" className="text-muted-foreground hover:text-foreground">Cancellation Policy</Link></li>
            <li><Link href="/policies/community-guidelines" className="text-muted-foreground hover:text-foreground">Community Guidelines</Link></li>
            <li><Link href="/policies/nondiscrimination" className="text-muted-foreground hover:text-foreground">Nondiscrimination Policy</Link></li>
          </ul>
        </div>
      </div>
      <div className="mt-12 border-t pt-6 flex flex-col md:flex-row justify-between items-center gap-4">
        <p className="text-xs text-muted-foreground">© {currentYear} Travella. All rights reserved.</p>
        <div className="flex items-center gap-4">
          <Link href="/policies/terms" className="text-xs text-muted-foreground hover:text-foreground">Terms</Link>
          <Link href="/policies/privacy" className="text-xs text-muted-foreground hover:text-foreground">Privacy</Link>
          <Link href="/policies/cancellation" className="text-xs text-muted-foreground hover:text-foreground">Cancellation</Link>
          <Link href="/policies/community-guidelines" className="text-xs text-muted-foreground hover:text-foreground">Community</Link>
        </div>
      </div>
    </div>
  </footer>);
}

