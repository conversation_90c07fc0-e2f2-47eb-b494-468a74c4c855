"use client"

import Link from "next/link"
import { Car, Facebook, Instagram, Twitter, Sparkles, Heart, Globe, Mail, Phone, MapPin, ArrowRight } from "lucide-react"
import { useTheme } from "next-themes"
import Image from "next/image"

export function MarketingFooter() {
  const currentYear = new Date().getFullYear()
  const { theme } = useTheme()
  const isDark = theme === "dark"

  return (
    <footer className="relative overflow-hidden">
      {/* Background with Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-indigo-950 to-purple-950">
        <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/10 via-transparent to-purple-500/10"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(99,102,241,0.15),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(168,85,247,0.15),transparent_50%)]"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-cyan-400/10 to-blue-600/10 rounded-full blur-2xl animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-pink-400/10 to-purple-600/10 rounded-full blur-3xl animate-pulse-slow" style={{animationDelay: '1s'}}></div>
      </div>

      <div className="container relative z-10 px-4 md:px-6 py-16 md:py-20">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 lg:gap-16">
          {/* Brand Section */}
          <div className="lg:col-span-2 space-y-6">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="absolute -inset-2 bg-gradient-to-r from-[#adbf3b]/20 to-black/20 rounded-xl blur opacity-75"></div>
                <div className="relative">
                  <Image
                    src="/svg/travella_logo_light.svg"
                    alt="Travella Logo"
                    width={40}
                    height={36}
                    className="transition-all duration-300"
                    priority
                  />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-[#adbf3b] rounded-full animate-pulse"></div>
              </div>
              <span className="text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                Travella
              </span>
            </div>

            <p className="text-lg text-gray-300 leading-relaxed max-w-md">
              Revolutionizing car rental with seamless technology, premium vehicles,
              and unforgettable experiences. Your journey to freedom starts here.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-gray-400">
                <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center">
                  <Mail className="w-4 h-4" />
                </div>
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center">
                  <Phone className="w-4 h-4" />
                </div>
                <span>+****************</span>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center">
                  <MapPin className="w-4 h-4" />
                </div>
                <span>San Francisco, CA</span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex items-center gap-4 pt-4">
              <Link
                href="https://www.facebook.com/detravella/"
                target="_blank"
                className="group w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-blue-600 transition-all duration-300 hover:scale-110"
              >
                <Facebook className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
              </Link>
              <Link
                href="#"
                className="group w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-sky-500 transition-all duration-300 hover:scale-110"
              >
                <Twitter className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
              </Link>
              <Link
                href="#"
                className="group w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-pink-600 transition-all duration-300 hover:scale-110"
              >
                <Instagram className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
              </Link>
            </div>
          </div>
          {/* Company Links */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-white flex items-center gap-2">
              <Globe className="w-5 h-5 text-[#adbf3b]" />
              Company
            </h4>
            <ul className="space-y-3">
              <li>
                <Link href="/about" className="group flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300">
                  <ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/careers" className="group flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300">
                  <ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Careers
                </Link>
              </li>
              <li>
                <Link href="/blog" className="group flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300">
                  <ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/press" className="group flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300">
                  <ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Press
                </Link>
              </li>
            </ul>
          </div>

          {/* Services Links */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-white flex items-center gap-2">
              <Car className="w-5 h-5 text-white" />
              Services
            </h4>
            <ul className="space-y-3">
              <li>
                <Link href="/car-rental" className="group flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300">
                  <ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Car Rental
                </Link>
              </li>
              <li>
                <Link href="/list-your-car" className="group flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300">
                  <ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  List Your Car
                </Link>
              </li>
              <li>
                <Link href="/airport-pickup" className="group flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300">
                  <ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Airport Pickup
                </Link>
              </li>
              <li>
                <Link href="/corporate" className="group flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300">
                  <ArrowRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Corporate Services
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="mt-16 pt-12 border-t border-white/10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-bold text-white mb-4">
                Stay in the loop
              </h3>
              <p className="text-gray-400 leading-relaxed">
                Get the latest updates on new features, exclusive offers, and travel inspiration
                delivered straight to your inbox.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <div className="flex-1 relative">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#adbf3b] focus:border-transparent transition-all duration-300"
                />
              </div>
              <button className="px-6 py-3 bg-[#adbf3b] text-white rounded-xl font-medium hover:scale-105 transition-all duration-300 hover:shadow-lg hover:bg-[#9db034] flex items-center gap-2">
                <span>Subscribe</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-16 pt-8 border-t border-white/10">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            <div className="flex items-center gap-2 text-gray-400">
              <Heart className="w-4 h-4 text-red-400" />
              <span>© {currentYear} Travella. Made with love in San Francisco.</span>
            </div>

            <div className="flex flex-wrap items-center gap-6">
              <Link href="/policies/terms" className="text-gray-400 hover:text-white transition-colors duration-300 text-sm">
                Terms of Service
              </Link>
              <Link href="/policies/privacy" className="text-gray-400 hover:text-white transition-colors duration-300 text-sm">
                Privacy Policy
              </Link>
              <Link href="/policies/cancellation" className="text-gray-400 hover:text-white transition-colors duration-300 text-sm">
                Cancellation Policy
              </Link>
              <Link href="/policies/community-guidelines" className="text-gray-400 hover:text-white transition-colors duration-300 text-sm">
                Community Guidelines
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

