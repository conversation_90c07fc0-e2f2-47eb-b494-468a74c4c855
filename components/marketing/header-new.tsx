"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { useRouter, usePathname } from "next/navigation"
import { Menu, X, Sun, Moon } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button-new"
import { useTheme } from "next-themes"
import { User } from "@supabase/supabase-js"
import Image from "next/image"

export function MarketingHeader({ user }: { user: User | null }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { setTheme, theme, resolvedTheme } = useTheme()
  const router = useRouter()
  const pathname = usePathname()

  // Memoize the theme toggle to prevent unnecessary rerenders
  const toggleTheme = useCallback(() => {
    setTheme(resolvedTheme === "light" ? "dark" : "light")
  }, [resolvedTheme, setTheme])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  // Navigate to section with smooth scrolling for improved UX and performance
  const navigateToSection = (e: React.MouseEvent<HTMLAnchorElement>, section: string) => {
    e.preventDefault()
    setIsMenuOpen(false)
    
    // Only apply smooth scrolling if we're on the homepage
    if (pathname === "/") {
      const element = document.getElementById(section)
      if (element) {
        element.scrollIntoView({ behavior: "smooth" })
      }
    } else {
      // If not on homepage, navigate to homepage with hash
      router.push(`/#${section}`)
    }
  }

  // Handle scroll event to add shadow to header
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // After mounting, we can safely show the UI that depends on the theme
  useEffect(() => {
    setMounted(true)
  }, [])

  // Prefetch important routes for faster navigation
  useEffect(() => {
    router.prefetch('/login')
    router.prefetch('/signup')
    router.prefetch('/dashboard')
  }, [router])

  return (
    <header
      className={`sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-shadow ${isScrolled ? "shadow-sm" : ""}`}
    >
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/" className="flex items-center space-x-2" prefetch={true}>
            <Image 
              src="/svg/travella_logo.svg" 
              alt="Travella Logo" 
              width={28} 
              height={24} 
              className="text-primary"
              priority 
            />
            <span className="text-xl font-bold">Travella</span>
          </Link>
          <nav className="hidden md:flex gap-6 ml-6">
            <a 
              href="/#features" 
              onClick={(e) => navigateToSection(e, 'features')}
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Features
            </a>
            <a 
              href="/#how-it-works" 
              onClick={(e) => navigateToSection(e, 'how-it-works')}
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              How It Works
            </a>
            <a 
              href="/#about-us" 
              onClick={(e) => navigateToSection(e, 'about-us')}
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              About Us
            </a>
            <a 
              href="/#testimonials" 
              onClick={(e) => navigateToSection(e, 'testimonials')}
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Testimonials
            </a>
          </nav>
        </div>
        <div className="flex items-center gap-2">
          {mounted && (
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="hidden md:flex"
              aria-label="Toggle theme"
            >
              {resolvedTheme === "light" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
              <span className="sr-only">Toggle theme</span>
            </Button>
          )}
          <div className="hidden md:flex gap-2">
            {user ? (
              <Button asChild>
                <Link href="/dashboard" prefetch={true}>Dashboard</Link>
              </Button>
            ) : (
              <>
                <Button variant="outline" asChild>
                  <Link href="/login" prefetch={true}>Log In</Link>
                </Button>
                <Button asChild>
                  <Link href="/signup" prefetch={true}>Sign Up</Link>
                </Button>
              </>
            )}
          </div>
          <div className="flex md:hidden items-center gap-2">
            {mounted && (
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleTheme}
                aria-label="Toggle theme"
              >
                {resolvedTheme === "light" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
              </Button>
            )}
            <button className="text-foreground" onClick={toggleMenu} aria-label="Toggle menu">
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </div>
      {isMenuOpen && (
        <div className="container md:hidden py-4 pb-6 animate-in">
          <nav className="flex flex-col gap-4">
            <a
              href="/#features"
              className="text-sm font-medium transition-colors hover:text-primary"
              onClick={(e) => navigateToSection(e, 'features')}
            >
              Features
            </a>
            <a
              href="/#how-it-works"
              className="text-sm font-medium transition-colors hover:text-primary"
              onClick={(e) => navigateToSection(e, 'how-it-works')}
            >
              How It Works
            </a>
            <a
              href="/#about-us"
              className="text-sm font-medium transition-colors hover:text-primary"
              onClick={(e) => navigateToSection(e, 'about-us')}
            >
              About Us
            </a>
            <a
              href="/#testimonials"
              className="text-sm font-medium transition-colors hover:text-primary"
              onClick={(e) => navigateToSection(e, 'testimonials')}
            >
              Testimonials
            </a>
            <div className="border-t border-border my-2 pt-4">
              {user? (
                <Button asChild className="w-full">
                  <Link href="/dashboard" prefetch={true}>
                    Dashboard
                  </Link>
                </Button>
              ) : (
                <>
                  <Button variant="outline" asChild className="w-full mb-2">
                    <Link href="/login" prefetch={true}>
                      Log In
                    </Link>
                  </Button>
                  <Button asChild className="w-full">
                    <Link href="/signup" prefetch={true}>
                      Sign Up
                    </Link>
                  </Button>
                </>
              )}
            </div>
          </nav>
        </div>
      )}
    </header>
  )
}

