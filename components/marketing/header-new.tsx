"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { useRouter, usePathname } from "next/navigation"
import { Menu, X, Sun, Moon, Spark<PERSON>, Zap } from "lucide-react"
import { useTheme } from "next-themes"
import { User } from "@supabase/supabase-js"
import Image from "next/image"

export function MarketingHeader({ user }: { user: User | null }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { setTheme, theme, resolvedTheme } = useTheme()
  const router = useRouter()
  const pathname = usePathname()

  // Memoize the theme toggle to prevent unnecessary rerenders
  const toggleTheme = useCallback(() => {
    setTheme(resolvedTheme === "light" ? "dark" : "light")
  }, [resolvedTheme, setTheme])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  // Navigate to section with smooth scrolling for improved UX and performance
  const navigateToSection = (e: React.MouseEvent<HTMLAnchorElement>, section: string) => {
    e.preventDefault()
    setIsMenuOpen(false)

    // Only apply smooth scrolling if we're on the homepage
    if (pathname === "/") {
      const element = document.getElementById(section)
      if (element) {
        element.scrollIntoView({ behavior: "smooth" })
      }
    } else {
      // If not on homepage, navigate to homepage with hash
      router.push(`/#${section}`)
    }
  }

  // Handle scroll event to add shadow to header
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // After mounting, we can safely show the UI that depends on the theme
  useEffect(() => {
    setMounted(true)
  }, [])

  // Prefetch important routes for faster navigation
  useEffect(() => {
    router.prefetch('/login')
    router.prefetch('/signup')
    router.prefetch('/dashboard')
  }, [router])

  return (
    <header
      className={`fixed top-0 z-50 w-full transition-all duration-300 ${
        isScrolled
          ? "bg-white/80 dark:bg-gray-950/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 shadow-lg"
          : "bg-transparent"
      }`}
    >
      {/* Gradient Border */}
      {isScrolled && (
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-indigo-500/50 to-transparent"></div>
      )}

      <div className="container flex h-20 items-center justify-between px-4 md:px-6">
        {/* Logo Section */}
        <div className="flex items-center gap-2">
          <Link href="/" className="group flex items-center space-x-3" prefetch={true}>
            <div className="relative">
              <div className="absolute -inset-2 bg-gradient-to-r from-[#adbf3b]/20 to-black/20 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative group-hover:scale-110 transition-transform duration-300">
                <Image
                  src={!isScrolled ? "/svg/travella_logo_light.svg" : (resolvedTheme === "dark" ? "/svg/travella_logo_light.svg" : "/svg/travella_logo.svg")}
                  alt="Travella Logo"
                  width={32}
                  height={28}
                  className="transition-all duration-300"
                  priority
                />
              </div>
            </div>
            <span className={`text-2xl font-bold transition-all duration-300 ${
              !isScrolled
                ? "text-white"
                : "bg-gradient-to-r from-[#adbf3b] to-black bg-clip-text text-transparent group-hover:from-black group-hover:to-[#adbf3b]"
            }`}>
              Travella
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex gap-8 ml-12">
            <a
              href="/#features"
              onClick={(e) => navigateToSection(e, 'features')}
              className={`relative text-sm font-medium transition-all duration-300 hover:scale-105 group ${
                isScrolled
                  ? "text-gray-700 dark:text-gray-300 hover:text-[#adbf3b] dark:hover:text-[#adbf3b]"
                  : "text-white/90 hover:text-white"
              }`}
            >
              Features
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#adbf3b] to-black group-hover:w-full transition-all duration-300"></span>
            </a>
            <a
              href="/#how-it-works"
              onClick={(e) => navigateToSection(e, 'how-it-works')}
              className={`relative text-sm font-medium transition-all duration-300 hover:scale-105 group ${
                isScrolled
                  ? "text-gray-700 dark:text-gray-300 hover:text-[#adbf3b] dark:hover:text-[#adbf3b]"
                  : "text-white/90 hover:text-white"
              }`}
            >
              How It Works
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#adbf3b] to-black group-hover:w-full transition-all duration-300"></span>
            </a>
            <a
              href="/#about-us"
              onClick={(e) => navigateToSection(e, 'about-us')}
              className={`relative text-sm font-medium transition-all duration-300 hover:scale-105 group ${
                isScrolled
                  ? "text-gray-700 dark:text-gray-300 hover:text-[#adbf3b] dark:hover:text-[#adbf3b]"
                  : "text-white/90 hover:text-white"
              }`}
            >
              About Us
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#adbf3b] to-black group-hover:w-full transition-all duration-300"></span>
            </a>
            <a
              href="/#testimonials"
              onClick={(e) => navigateToSection(e, 'testimonials')}
              className={`relative text-sm font-medium transition-all duration-300 hover:scale-105 group ${
                isScrolled
                  ? "text-gray-700 dark:text-gray-300 hover:text-[#adbf3b] dark:hover:text-[#adbf3b]"
                  : "text-white/90 hover:text-white"
              }`}
            >
              Testimonials
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#adbf3b] to-black group-hover:w-full transition-all duration-300"></span>
            </a>
          </nav>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-3">
          {/* Theme Toggle */}
          {mounted && (
            <button
              onClick={toggleTheme}
              className={`hidden md:flex items-center justify-center w-10 h-10 rounded-xl transition-all duration-300 hover:scale-110 ${
                isScrolled
                  ? "bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700"
                  : "bg-white/10 backdrop-blur-md hover:bg-white/20"
              }`}
              aria-label="Toggle theme"
            >
              {resolvedTheme === "light" ?
                <Moon className="h-4 w-4" /> :
                <Sun className="h-4 w-4" />
              }
            </button>
          )}

          {/* Desktop CTA Buttons */}
          <div className="hidden md:flex gap-3">
            {user ? (
              <Link href="/dashboard" prefetch={true}>
                <button className="px-6 py-2.5 bg-[#adbf3b] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg hover:bg-[#9db034]">
                  <span className="flex items-center gap-2">
                    <Zap className="w-4 h-4" />
                    Dashboard
                  </span>
                </button>
              </Link>
            ) : (
              <>
                <Link href="/login" prefetch={true}>
                  <button className={`px-6 py-2.5 rounded-xl font-medium transition-all duration-300 hover:scale-105 ${
                    isScrolled
                      ? "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                      : "bg-white/10 backdrop-blur-md text-white hover:bg-white/20"
                  }`}>
                    Log In
                  </button>
                </Link>
                <Link href="/signup" prefetch={true}>
                  <button className="px-6 py-2.5 bg-[#adbf3b] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg hover:bg-[#9db034]">
                    Sign Up
                  </button>
                </Link>
              </>
            )}
          </div>

          {/* Mobile Menu */}
          <div className="flex md:hidden items-center gap-2">
            {mounted && (
              <button
                onClick={toggleTheme}
                className={`flex items-center justify-center w-10 h-10 rounded-xl transition-all duration-300 ${
                  isScrolled
                    ? "bg-gray-100 dark:bg-gray-800"
                    : "bg-white/10 backdrop-blur-md text-white"
                }`}
                aria-label="Toggle theme"
              >
                {resolvedTheme === "light" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
              </button>
            )}
            <button
              onClick={toggleMenu}
              className={`flex items-center justify-center w-10 h-10 rounded-xl transition-all duration-300 ${
                isScrolled
                  ? "bg-gray-100 dark:bg-gray-800"
                  : "bg-white/10 backdrop-blur-md text-white"
              }`}
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X size={20} /> : <Menu size={20} />}
            </button>
          </div>
        </div>
      </div>
      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-white/95 dark:bg-gray-950/95 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 shadow-xl">
          <div className="container py-6 px-4">
            <nav className="flex flex-col gap-6">
              {/* Navigation Links */}
              <div className="space-y-4">
                <a
                  href="/#features"
                  className="block text-lg font-medium text-gray-700 dark:text-gray-300 hover:text-[#adbf3b] dark:hover:text-[#adbf3b] transition-colors duration-300"
                  onClick={(e) => navigateToSection(e, 'features')}
                >
                  Features
                </a>
                <a
                  href="/#how-it-works"
                  className="block text-lg font-medium text-gray-700 dark:text-gray-300 hover:text-[#adbf3b] dark:hover:text-[#adbf3b] transition-colors duration-300"
                  onClick={(e) => navigateToSection(e, 'how-it-works')}
                >
                  How It Works
                </a>
                <a
                  href="/#about-us"
                  className="block text-lg font-medium text-gray-700 dark:text-gray-300 hover:text-[#adbf3b] dark:hover:text-[#adbf3b] transition-colors duration-300"
                  onClick={(e) => navigateToSection(e, 'about-us')}
                >
                  About Us
                </a>
                <a
                  href="/#testimonials"
                  className="block text-lg font-medium text-gray-700 dark:text-gray-300 hover:text-[#adbf3b] dark:hover:text-[#adbf3b] transition-colors duration-300"
                  onClick={(e) => navigateToSection(e, 'testimonials')}
                >
                  Testimonials
                </a>
              </div>

              {/* Divider */}
              <div className="h-px bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>

              {/* CTA Buttons */}
              <div className="space-y-3">
                {user ? (
                  <Link href="/dashboard" prefetch={true}>
                    <button className="w-full px-6 py-3 bg-[#adbf3b] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg hover:bg-[#9db034]">
                      <span className="flex items-center justify-center gap-2">
                        <Zap className="w-4 h-4" />
                        Dashboard
                      </span>
                    </button>
                  </Link>
                ) : (
                  <>
                    <Link href="/login" prefetch={true}>
                      <button className="w-full px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-xl font-medium transition-all duration-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                        Log In
                      </button>
                    </Link>
                    <Link href="/signup" prefetch={true}>
                      <button className="w-full px-6 py-3 bg-[#adbf3b] text-white rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg hover:bg-[#9db034]">
                        Sign Up
                      </button>
                    </Link>
                  </>
                )}
              </div>
            </nav>
          </div>
        </div>
      )}
    </header>
  )
}

