/**
 * Travella Design System
 *
 * This document outlines the core design principles, components, and guidelines
 * for the Travella car rental platform.
 */

/**
 * Color Palette
 *
 * Primary Colors:
 * - teal-600 (#0D9488): Primary brand color, used for primary actions and key UI elements
 * - slate-800 (#1E293B): Used for text and UI elements requiring visual weight
 * - amber-500 (#F59E0B): Used for highlights, calls to action, and important notifications
 *
 * Neutral Colors:
 * - slate-50 (#F8FAFC): Background color for light mode
 * - slate-100 (#F1F5F9): Secondary background, cards, and form elements
 * - slate-200 (#E2E8F0): Borders and dividers
 * - slate-300 (#CBD5E1): Disabled states and secondary borders
 * - slate-400 (#94A3B8): Placeholder text and icons
 * - slate-500 (#64748B): Secondary text and icons
 * - slate-600 (#475569): Primary text for light mode
 * - slate-700 (#334155): Headings for light mode
 *
 * Accent Colors:
 * - teal-50 (#F0FDFA): Light teal for backgrounds and hover states
 * - teal-100 (#CCFBF1): Light teal for selected states
 * - teal-200 (#99F6E4): Light teal for active states
 * - teal-700 (#0F766E): Dark teal for hover states
 * - teal-800 (#115E59): Dark teal for active states
 * - amber-50 (#FFFBEB): Light amber for backgrounds and hover states
 * - amber-100 (#FEF3C7): Light amber for selected states
 * - amber-600 (#D97706): Dark amber for hover states
 * - amber-700 (#B45309): Dark amber for active states
 * - rose-500 (#F43F5E): Error and destructive actions
 * - emerald-500 (#10B981): Success states
 * - blue-500 (#3B82F6): Information states
 *
 * Dark Mode Colors:
 * - slate-900 (#0F172A): Background color for dark mode
 * - slate-800 (#1E293B): Secondary background for dark mode
 * - slate-700 (#334155): Borders and dividers for dark mode
 * - slate-300 (#CBD5E1): Primary text for dark mode
 * - slate-400 (#94A3B8): Secondary text for dark mode
 */

/**
 * Typography
 *
 * Font Families:
 * - Headings: 'Inter', sans-serif (or system-ui)
 * - Body: 'Inter', sans-serif (or system-ui)
 *
 * Font Sizes:
 * - xs: 0.75rem (12px)
 * - sm: 0.875rem (14px)
 * - base: 1rem (16px)
 * - lg: 1.125rem (18px)
 * - xl: 1.25rem (20px)
 * - 2xl: 1.5rem (24px)
 * - 3xl: 1.875rem (30px)
 * - 4xl: 2.25rem (36px)
 * - 5xl: 3rem (48px)
 *
 * Font Weights:
 * - normal: 400
 * - medium: 500
 * - semibold: 600
 * - bold: 700
 *
 * Line Heights:
 * - tight: 1.25
 * - normal: 1.5
 * - relaxed: 1.75
 *
 * Letter Spacing:
 * - tighter: -0.05em
 * - tight: -0.025em
 * - normal: 0
 * - wide: 0.025em
 * - wider: 0.05em
 */

/**
 * Spacing System
 *
 * - 0: 0px
 * - px: 1px
 * - 0.5: 0.125rem (2px)
 * - 1: 0.25rem (4px)
 * - 1.5: 0.375rem (6px)
 * - 2: 0.5rem (8px)
 * - 2.5: 0.625rem (10px)
 * - 3: 0.75rem (12px)
 * - 3.5: 0.875rem (14px)
 * - 4: 1rem (16px)
 * - 5: 1.25rem (20px)
 * - 6: 1.5rem (24px)
 * - 8: 2rem (32px)
 * - 10: 2.5rem (40px)
 * - 12: 3rem (48px)
 * - 16: 4rem (64px)
 * - 20: 5rem (80px)
 * - 24: 6rem (96px)
 */

/**
 * Border Radius
 *
 * - none: 0
 * - sm: 0.125rem (2px)
 * - DEFAULT: 0.25rem (4px)
 * - md: 0.375rem (6px)
 * - lg: 0.5rem (8px)
 * - xl: 0.75rem (12px)
 * - 2xl: 1rem (16px)
 * - 3xl: 1.5rem (24px)
 * - full: 9999px
 */

/**
 * Shadows
 *
 * - sm: 0 1px 2px 0 rgb(0 0 0 / 0.05)
 * - DEFAULT: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)
 * - md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)
 * - lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)
 * - xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)
 * - 2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25)
 */

/**
 * Transitions
 *
 * - DEFAULT: all 150ms cubic-bezier(0.4, 0, 0.2, 1)
 * - fast: all 100ms cubic-bezier(0.4, 0, 0.2, 1)
 * - slow: all 300ms cubic-bezier(0.4, 0, 0.2, 1)
 * - ease-in-out: all 200ms cubic-bezier(0.4, 0, 0.2, 1)
 * - ease-out: all 200ms cubic-bezier(0, 0, 0.2, 1)
 * - ease-in: all 200ms cubic-bezier(0.4, 0, 1, 1)
 */

/**
 * Z-Index
 *
 * - 0: 0
 * - 10: 10
 * - 20: 20
 * - 30: 30
 * - 40: 40
 * - 50: 50
 * - auto: auto
 */

/**
 * Accessibility Guidelines
 *
 * - All interactive elements must have a minimum touch target size of 44x44px
 * - Color contrast must meet WCAG AA standards (4.5:1 for normal text, 3:1 for large text)
 * - All functionality must be accessible via keyboard
 * - Focus states must be clearly visible
 * - All images must have alt text
 * - Form elements must have associated labels
 * - Error messages must be clear and descriptive
 * - Animations must respect user preferences (prefers-reduced-motion)
 */

/**
 * Responsive Breakpoints
 *
 * - sm: 640px
 * - md: 768px
 * - lg: 1024px
 * - xl: 1280px
 * - 2xl: 1536px
 */

/**
 * Grid System
 *
 * - Container max-widths:
 *   - sm: 640px
 *   - md: 768px
 *   - lg: 1024px
 *   - xl: 1280px
 *   - 2xl: 1536px
 *
 * - Grid columns: 12
 * - Grid gap: 1rem (16px) by default
 */

/**
 * Animation Guidelines
 *
 * - Use subtle animations that enhance the user experience
 * - Keep animations short (150-300ms)
 * - Avoid animations that could cause motion sickness
 * - Respect user preferences for reduced motion
 * - Use animations to provide feedback and guide attention
 */

export {}

