import { Car, Calendar, DollarSign, Users } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card-new"

interface DashboardStatsProps {
  carCount: number
  activeCarCount: number
  bookingCount: number
  totalRevenue: number
}

export function DashboardStats({ carCount, activeCarCount, bookingCount, totalRevenue }: DashboardStatsProps) {
  // Format the revenue as currency
  const formattedRevenue = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(totalRevenue)

  const stats = [
    {
      title: "Total Revenue",
      value: formattedRevenue,
      description: "Lifetime earnings",
      icon: DollarSign,
      trend: "+12.5% from last month",
      trendUp: true,
    },
    {
      title: "Active Listings",
      value: activeCarCount,
      description: `Out of ${carCount} total cars`,
      icon: Car,
      trend: carCount > 0 ? `${Math.round((activeCarCount / carCount) * 100)}% active rate` : "No cars listed",
      trendUp: activeCarCount > 0,
    },
    {
      title: "Bookings",
      value: bookingCount,
      description: "Total bookings received",
      icon: Calendar,
      trend: "+3 new this week",
      trendUp: true,
    },
    {
      title: "Completion Rate",
      value: `${bookingCount > 0 ? Math.round((bookingCount / carCount) * 100) : 0}%`,
      description: "Booking to listing ratio",
      icon: Users,
      trend: bookingCount > 0 ? "Good performance" : "Add more cars",
      trendUp: bookingCount > 0,
    },
  ]

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index} hover>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                <div className="flex items-baseline gap-1">
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
              </div>
              <div className="rounded-full bg-primary/10 p-2 text-primary">
                <stat.icon className="h-5 w-5" />
              </div>
            </div>
            <div className="mt-4 flex items-center justify-between">
              <p className="text-xs text-muted-foreground">{stat.description}</p>
              {stat.trend && (
                <p className={`text-xs ${stat.trendUp ? "text-success" : "text-muted-foreground"}`}>{stat.trend}</p>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

