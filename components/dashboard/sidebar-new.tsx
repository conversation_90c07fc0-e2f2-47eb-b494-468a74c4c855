"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { Car, Calendar, CreditCard, LayoutDashboard, Settings, Users, LogOut, BarChart3 } from "lucide-react"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarSeparator,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button-new"
import { cn } from "@/lib/utils"

export function DashboardSidebar() {
  const pathname = usePathname()

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`)
  }

  const menuItems = [
    {
      title: "Dashboard",
      icon: LayoutDashboard,
      href: "/dashboard",
      active: isActive("/dashboard") && pathname === "/dashboard",
    },
    {
      title: "My Cars",
      icon: Car,
      href: "/dashboard/cars",
      active: isActive("/dashboard/cars"),
    },
    {
      title: "Bookings",
      icon: Calendar,
      href: "/dashboard/bookings",
      active: isActive("/dashboard/bookings"),
    },
    {
      title: "Transactions",
      icon: CreditCard,
      href: "/dashboard/transactions",
      active: isActive("/dashboard/transactions"),
    },
    {
      title: "Performance",
      icon: BarChart3,
      href: "/dashboard/performance",
      active: isActive("/dashboard/performance"),
    },
    {
      title: "Customers",
      icon: Users,
      href: "/dashboard/customers",
      active: isActive("/dashboard/customers"),
    },
    {
      title: "Settings",
      icon: Settings,
      href: "/dashboard/settings",
      active: isActive("/dashboard/settings"),
    },
  ]

  return (
    <Sidebar>
      <SidebarHeader className="flex items-center justify-center py-4">
        <Link href="/dashboard" className="flex items-center gap-2 font-bold text-xl">
          <Car className="h-6 w-6 text-primary" />
          <span>Travella</span>
        </Link>
      </SidebarHeader>
      <SidebarSeparator />
      <SidebarContent>
        <SidebarMenu>
          {menuItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <SidebarMenuButton asChild isActive={item.active}>
                <Link
                  href={item.href}
                  className={cn("transition-colors", item.active ? "text-primary" : "text-foreground")}
                >
                  <item.icon className={cn("h-4 w-4", item.active ? "text-primary" : "text-muted-foreground")} />
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <Button variant="ghost" className="w-full justify-start" asChild>
          <Link href="/api/auth/signout">
            <LogOut className="mr-2 h-4 w-4 text-muted-foreground" />
            Sign Out
          </Link>
        </Button>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

