"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Edit, MoreHorizontal, Trash } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { deleteCar } from "@/app/actions/car-actions"
import { toast } from "@/components/ui/use-toast"

type CarFeature = {
  id: string
  car_id: string
  feature: string
}

type CarImage = {
  id: string
  car_id: string
  url: string
}

type Car = {
  id: string
  title: string
  description: string | null
  make: string
  model: string
  year: number
  price: number
  location: string
  status: "active" | "maintenance" | "unavailable"
  owner_id: string
  created_at: string
  updated_at: string
  car_features: CarFeature[]
  car_images: CarImage[]
}

interface CarListingsProps {
  cars: Car[]
}

export function CarListings({ cars }: CarListingsProps) {
  const router = useRouter()
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [selectedCar, setSelectedCar] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = (carId: string) => {
    setSelectedCar(carId)
    setOpenDeleteDialog(true)
  }

  const confirmDelete = async () => {
    if (!selectedCar) return

    setIsDeleting(true)

    try {
      const result = await deleteCar(selectedCar)

      if (result.error) {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Success",
          description: "Car listing deleted successfully",
        })
        router.refresh()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete car listing",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
      setOpenDeleteDialog(false)
    }
  }

  if (cars.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <h3 className="text-lg font-medium">No cars listed yet</h3>
        <p className="text-muted-foreground mt-1">Add your first car to start receiving bookings</p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/cars/new">Add Your First Car</Link>
        </Button>
      </div>
    )
  }

  return (
    <>
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {cars.map((car) => (
          <Card key={car.id} className="overflow-hidden">
            <div className="relative h-48 w-full">
              <Image
                src={car.car_images[0]?.url || "/placeholder.svg?height=200&width=300"}
                alt={car.title}
                fill
                className="object-cover"
              />
              <div className="absolute right-2 top-2">
                <Badge variant={car.status === "active" ? "default" : "secondary"}>{car.status}</Badge>
              </div>
            </div>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{car.title}</CardTitle>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Open menu</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem asChild>
                      <Link href={`/dashboard/cars/${car.id}`}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={() => handleDelete(car.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <CardDescription>{car.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Price:</span>
                  <span className="font-medium">${car.price}/day</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Location:</span>
                  <span className="font-medium">{car.location}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Year:</span>
                  <span className="font-medium">{car.year}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="flex flex-wrap gap-2">
                {car.car_features.slice(0, 3).map((feature) => (
                  <Badge key={feature.id} variant="outline">
                    {feature.feature}
                  </Badge>
                ))}
                {car.car_features.length > 3 && <Badge variant="outline">+{car.car_features.length - 3} more</Badge>}
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Dialog open={openDeleteDialog} onOpenChange={setOpenDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete this car?</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the car listing and remove the data from our
              servers.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenDeleteDialog(false)} disabled={isDeleting}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete} disabled={isDeleting}>
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

