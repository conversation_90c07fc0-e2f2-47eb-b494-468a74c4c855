"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
} from "recharts"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { format, subDays, subMonths } from "date-fns"
import { Button } from "@/components/ui/button"
import { Download } from "lucide-react"

interface FinancialDashboardProps {
  financialData: {
    earnings: {
      date: string
      amount: number
    }[]
    expenses: {
      date: string
      amount: number
    }[]
    transactions: {
      id: string
      date: string
      description: string
      amount: number
      type: "booking" | "expense" | "payout"
      status: "pending" | "completed" | "failed"
    }[]
    summary: {
      totalEarnings: number
      totalExpenses: number
      netIncome: number
      pendingPayouts: number
    }
    carPerformance: {
      carId: string
      carName: string
      revenue: number
      bookings: number
    }[]
  }
}

export function FinancialDashboard({ financialData }: FinancialDashboardProps) {
  const [timeRange, setTimeRange] = useState("30days")

  // Filter data based on selected time range
  const getFilteredData = (data: any[]) => {
    const now = new Date()
    let cutoffDate: Date

    switch (timeRange) {
      case "7days":
        cutoffDate = subDays(now, 7)
        break
      case "30days":
        cutoffDate = subDays(now, 30)
        break
      case "90days":
        cutoffDate = subDays(now, 90)
        break
      case "year":
        cutoffDate = subMonths(now, 12)
        break
      default:
        cutoffDate = subDays(now, 30)
    }

    return data.filter((item) => new Date(item.date) >= cutoffDate)
  }

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(value)
  }

  // Format date for tooltip
  const formatDate = (dateStr: string) => {
    return format(new Date(dateStr), "MMM d, yyyy")
  }

  // Prepare data for charts
  const filteredEarnings = getFilteredData(financialData.earnings)
  const filteredExpenses = getFilteredData(financialData.expenses)
  const filteredTransactions = getFilteredData(financialData.transactions)

  // Prepare data for revenue vs expenses chart
  const revenueVsExpensesData = filteredEarnings.map((earningItem, index) => {
    const expenseItem = filteredExpenses[index] || { amount: 0 }
    return {
      date: earningItem.date,
      revenue: earningItem.amount,
      expenses: expenseItem.amount,
      profit: earningItem.amount - expenseItem.amount,
    }
  })

  // Colors for pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"]

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Financial Dashboard</h2>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Last 7 days</SelectItem>
            <SelectItem value="30days">Last 30 days</SelectItem>
            <SelectItem value="90days">Last 90 days</SelectItem>
            <SelectItem value="year">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(financialData.summary.totalEarnings)}</div>
            <p className="text-xs text-muted-foreground">Lifetime earnings</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(financialData.summary.totalExpenses)}</div>
            <p className="text-xs text-muted-foreground">Maintenance and fees</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Net Income</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(financialData.summary.netIncome)}</div>
            <p className="text-xs text-muted-foreground">Earnings minus expenses</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending Payouts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(financialData.summary.pendingPayouts)}</div>
            <p className="text-xs text-muted-foreground">To be paid out</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="cars">Car Performance</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue vs Expenses</CardTitle>
              <CardDescription>Compare your earnings and expenses over time</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={revenueVsExpensesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={(date) => format(new Date(date), "MMM d")} />
                  <YAxis tickFormatter={(value) => `$${value}`} />
                  <Tooltip formatter={(value) => [formatCurrency(Number(value))]} labelFormatter={formatDate} />
                  <Legend />
                  <Line type="monotone" dataKey="revenue" name="Revenue" stroke="#8884d8" activeDot={{ r: 8 }} />
                  <Line type="monotone" dataKey="expenses" name="Expenses" stroke="#82ca9d" />
                  <Line type="monotone" dataKey="profit" name="Profit" stroke="#ffc658" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Breakdown</CardTitle>
              <CardDescription>Your revenue sources</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <div className="grid md:grid-cols-2 gap-4 h-full">
                <div className="flex items-center justify-center">
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={financialData.carPerformance}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="revenue"
                        nameKey="carName"
                      >
                        {financialData.carPerformance.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [formatCurrency(Number(value))]} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div>
                  <h3 className="font-medium mb-4">Revenue by Car</h3>
                  <div className="space-y-4">
                    {financialData.carPerformance.map((car) => (
                      <div key={car.carId} className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{car.carName}</p>
                          <p className="text-sm text-muted-foreground">{car.bookings} bookings</p>
                        </div>
                        <p className="font-medium">{formatCurrency(car.revenue)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cars" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Car Performance</CardTitle>
              <CardDescription>Revenue generated by each car</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={financialData.carPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="carName" />
                  <YAxis tickFormatter={(value) => `$${value}`} />
                  <Tooltip formatter={(value) => [formatCurrency(Number(value))]} />
                  <Legend />
                  <Bar dataKey="revenue" name="Revenue" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="pt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Your recent financial activity</CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredTransactions.slice(0, 10).map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between border-b pb-2">
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-muted-foreground">
                        {format(new Date(transaction.date), "MMM d, yyyy")} ·
                        <span className="ml-1 capitalize">{transaction.type}</span>
                      </p>
                    </div>
                    <div className="text-right">
                      <p
                        className={`font-medium ${transaction.type === "expense" ? "text-destructive" : "text-green-600"}`}
                      >
                        {transaction.type === "expense" ? "-" : "+"}
                        {formatCurrency(transaction.amount)}
                      </p>
                      <p className="text-sm">
                        <span
                          className={`inline-block px-2 py-0.5 rounded-full text-xs ${
                            transaction.status === "completed"
                              ? "bg-green-100 text-green-800"
                              : transaction.status === "pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                          }`}
                        >
                          {transaction.status}
                        </span>
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              {filteredTransactions.length > 10 && (
                <Button variant="link" className="mt-4 w-full">
                  View all transactions
                </Button>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

