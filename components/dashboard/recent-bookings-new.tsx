import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar-new"
import { Badge } from "@/components/ui/badge-new"
import { Button } from "@/components/ui/button-new"
import { format } from "date-fns"
import { ArrowRight } from "lucide-react"
import Link from "next/link"

interface Booking {
  id: string
  car_id: string
  renter_id: string
  start_date: string
  end_date: string
  status: "pending" | "confirmed" | "completed" | "cancelled"
  total_amount: number
  created_at: string
  updated_at: string
  cars: {
    title: string
    make: string
    model: string
  }
  profiles: {
    full_name: string
    email: string
    avatar_url: string | null
  }
}

interface RecentBookingsProps {
  bookings: Booking[]
}

export function RecentBookings({ bookings }: RecentBookingsProps) {
  if (bookings.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-6 text-center">
        <p className="text-sm text-muted-foreground">No recent bookings</p>
      </div>
    )
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "completed":
        return "success"
      case "confirmed":
        return "default"
      case "pending":
        return "secondary"
      case "cancelled":
        return "destructive"
      default:
        return "outline"
    }
  }

  return (
    <div className="space-y-6">
      {bookings.map((booking) => (
        <div key={booking.id} className="flex items-center gap-4">
          <Avatar size="md">
            <AvatarImage
              src={booking.profiles.avatar_url || "/placeholder-user.jpg"}
              alt={booking.profiles.full_name}
            />
            <AvatarFallback>{booking.profiles.full_name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div className="flex-1 space-y-1">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">{booking.profiles.full_name}</p>
              <Badge variant={getStatusVariant(booking.status)}>{booking.status}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                {booking.cars.make} {booking.cars.model}
              </p>
              <p className="text-sm font-medium">
                {new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: "USD",
                }).format(booking.total_amount)}
              </p>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                {format(new Date(booking.start_date), "MMM d")} - {format(new Date(booking.end_date), "MMM d, yyyy")}
              </p>
              <p className="text-xs text-muted-foreground">Booked {format(new Date(booking.created_at), "MMM d")}</p>
            </div>
          </div>
        </div>
      ))}

      <div className="pt-2">
        <Button variant="ghost" size="sm" className="w-full" asChild>
          <Link href="/dashboard/bookings">
            View all bookings
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>
    </div>
  )
}

