"use client"

import { useState, useEffect } from "react"
import { Bell, Search, User, Sun, Moon } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button-new"
import { Input } from "@/components/ui/input-new"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar-new"
import { useTheme } from "next-themes"
import { Badge } from "@/components/ui/badge-new"
import { User as LoggedInUser } from "@supabase/supabase-js"
import { createClient } from "@/lib/supabase"

export function DashboardHeader({ user}: {user: LoggedInUser}) {
  const supabase = createClient();
  const [searchOpen, setSearchOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { setTheme, theme } = useTheme()
  const [notifications, setNotifications] = useState([
    { id: 1, title: "New booking request", read: false },
    { id: 2, title: "Payment received", read: false },
    { id: 3, title: "Car approval needed", read: true },
  ])

  // After mounting, we can safely show the UI that depends on the theme
  useEffect(() => {
    setMounted(true)
  }, [])

  const unreadCount = notifications.filter((n) => !n.read).length

  const markAllAsRead = () => {
    setNotifications(notifications.map((n) => ({ ...n, read: true })))
  }

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4 md:px-6">
      <SidebarTrigger />

      {searchOpen ? (
        <div className="flex-1 md:w-auto md:flex-none">
          <form className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search..."
              className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[336px]"
              icon={<Search className="h-4 w-4 text-muted-foreground" />}
              iconPosition="left"
            />
          </form>
        </div>
      ) : (
        <Button variant="ghost" size="icon" className="md:hidden" onClick={() => setSearchOpen(true)}>
          <Search className="h-4 w-4" />
          <span className="sr-only">Search</span>
        </Button>
      )}

      <div className="hidden md:flex md:flex-1">
        <form className="relative">
          <Input
            type="search"
            placeholder="Search..."
            className="w-full rounded-lg bg-background md:w-[200px] lg:w-[336px]"
            icon={<Search className="h-4 w-4 text-muted-foreground" />}
            iconPosition="left"
          />
        </form>
      </div>

      <div className="flex items-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              <span className="sr-only">Notifications</span>
              {unreadCount > 0 && (
                <Badge
                  variant="secondary"
                  size="sm"
                  className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center p-0"
                >
                  {unreadCount}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel className="flex items-center justify-between">
              <span>Notifications</span>
              {unreadCount > 0 && (
                <Button variant="ghost" size="sm" onClick={markAllAsRead} className="h-auto py-1">
                  Mark all as read
                </Button>
              )}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {notifications.length === 0 ? (
              <div className="py-4 text-center text-sm text-muted-foreground">No notifications</div>
            ) : (
              notifications.map((notification) => (
                <DropdownMenuItem key={notification.id} className="flex items-start gap-2 p-3">
                  <div
                    className={`mt-0.5 h-2 w-2 rounded-full ${notification.read ? "bg-transparent" : "bg-secondary"}`}
                  />
                  <div className="flex-1">
                    <p className={`text-sm ${notification.read ? "text-muted-foreground" : "font-medium"}`}>
                      {notification.title}
                    </p>
                    <p className="text-xs text-muted-foreground">Just now</p>
                  </div>
                </DropdownMenuItem>
              ))
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="justify-center text-sm font-medium">View all notifications</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          variant="ghost"
          size="icon"
          onClick={() => setTheme(theme === "light" ? "dark" : "light")}
          className="hidden md:flex"
        >
          {mounted && theme === "light" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
          <span className="sr-only">Toggle theme</span>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Avatar size="sm">
                <AvatarImage
                  src={user?.user_metadata.avatar_url || "/placeholder-user.jpg"}
                  alt={user?.user_metadata.name || "User"}
                />
                <AvatarFallback>
                  {user?.user_metadata.name ? user.user_metadata.name.charAt(0).toUpperCase() : <User className="h-4 w-4" />}
                </AvatarFallback>
              </Avatar>
              <span className="sr-only">Toggle user menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{`${ user.user_metadata.name}`|| "My Account"}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <a href="/dashboard/profile">Profile</a>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <a href="/dashboard/settings">Settings</a>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <a href="/dashboard/billing">Billing</a>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => supabase.auth.signOut()}>Sign out</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}

