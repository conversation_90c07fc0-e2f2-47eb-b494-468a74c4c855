"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, XAxis, <PERSON><PERSON><PERSON><PERSON>, Tooltip, CartesianGrid } from "recharts"
import { useState } from "react"
import { Button } from "@/components/ui/button-new"

interface OverviewProps {
  data: {
    name: string
    revenue: number
    bookings: number
  }[]
}

export function Overview({ data }: OverviewProps) {
  const [view, setView] = useState<"revenue" | "bookings">("revenue")

  // Format the tooltip value
  const formatTooltipValue = (value: number) => {
    if (view === "revenue") {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(value)
    }
    return value
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-end">
        <div className="flex items-center space-x-2">
          <Button variant={view === "revenue" ? "subtle" : "ghost"} size="sm" onClick={() => setView("revenue")}>
            Revenue
          </Button>
          <Button variant={view === "bookings" ? "subtle" : "ghost"} size="sm" onClick={() => setView("bookings")}>
            Bookings
          </Button>
        </div>
      </div>
      <ResponsiveContainer width="100%" height={350}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="name" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => (view === "revenue" ? `$${value}` : `${value}`)}
          />
          <Tooltip
            formatter={(value: number) => [formatTooltipValue(value), view === "revenue" ? "Revenue" : "Bookings"]}
            labelFormatter={(label) => `Month: ${label}`}
            contentStyle={{
              backgroundColor: "var(--background)",
              borderColor: "var(--border)",
              borderRadius: "var(--radius-md)",
            }}
          />
          <Bar
            dataKey={view}
            fill="currentColor"
            radius={[4, 4, 0, 0]}
            className="fill-primary"
            animationDuration={500}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

