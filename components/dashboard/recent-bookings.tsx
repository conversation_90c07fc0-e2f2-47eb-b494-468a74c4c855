import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface Booking {
  id: string
  car_id: string
  renter_id: string
  start_date: string
  end_date: string
  status: "pending" | "confirmed" | "completed" | "cancelled"
  total_amount: number
  created_at: string
  updated_at: string
  cars: {
    title: string
    make: string
    model: string
  }
  profiles: {
    full_name: string
    email: string
    avatar_url: string | null
  }
}

interface RecentBookingsProps {
  bookings: Booking[]
}

export function RecentBookings({ bookings }: RecentBookingsProps) {
  if (bookings.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-6 text-center">
        <p className="text-sm text-muted-foreground">No recent bookings</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {bookings.map((booking) => (
        <div key={booking.id} className="flex items-center">
          <Avatar className="h-9 w-9">
            <AvatarImage
              src={booking.profiles.avatar_url || "/placeholder-user.jpg"}
              alt={booking.profiles.full_name}
            />
            <AvatarFallback>{booking.profiles.full_name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{booking.profiles.full_name}</p>
            <p className="text-sm text-muted-foreground">
              {booking.cars.make} {booking.cars.model}
            </p>
          </div>
          <div className="ml-auto text-right">
            <Badge
              variant={
                booking.status === "completed"
                  ? "default"
                  : booking.status === "confirmed"
                    ? "outline"
                    : booking.status === "pending"
                      ? "secondary"
                      : "destructive"
              }
            >
              {booking.status}
            </Badge>
            <p className="mt-1 text-sm text-muted-foreground">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD",
              }).format(booking.total_amount)}
            </p>
          </div>
        </div>
      ))}
    </div>
  )
}

