"use client"

import { useState } from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Check, X, MessageSquare, Calendar, FileText } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { format, differenceInDays } from "date-fns"
import { toast } from "@/components/ui/use-toast"
import { updateBookingStatus } from "@/app/actions/booking-actions"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"

type Booking = {
  id: string
  car_id: string
  renter_id: string
  start_date: string
  end_date: string
  status: "pending" | "confirmed" | "completed" | "cancelled"
  total_amount: number
  created_at: string
  updated_at: string
  cars: {
    id: string
    title: string
    make: string
    model: string
    year: number
    price: number
  }
  profiles: {
    id: string
    full_name: string
    email: string
    avatar_url: string | null
    phone: string | null
  }
}

interface EnhancedBookingTableProps {
  bookings: Booking[]
}

export function EnhancedBookingTable({ bookings }: EnhancedBookingTableProps) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [isMessageOpen, setIsMessageOpen] = useState(false)
  const [isStatusUpdateOpen, setIsStatusUpdateOpen] = useState(false)
  const [newStatus, setNewStatus] = useState<string>("")
  const [isUpdating, setIsUpdating] = useState(false)
  const [messageText, setMessageText] = useState("")

  const columns: ColumnDef<Booking>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "id",
      header: "Booking ID",
      cell: ({ row }) => <div className="font-medium">{row.getValue("id").substring(0, 8)}...</div>,
    },
    {
      accessorKey: "profiles.full_name",
      header: ({ column }) => {
        return (
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
            Customer
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const booking = row.original
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={booking.profiles.avatar_url || ""} alt={booking.profiles.full_name} />
              <AvatarFallback>{booking.profiles.full_name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>{booking.profiles.full_name}</div>
          </div>
        )
      },
    },
    {
      accessorKey: "cars.title",
      header: "Car",
      cell: ({ row }) => {
        const booking = row.original
        return (
          <div>
            {booking.cars.make} {booking.cars.model} ({booking.cars.year})
          </div>
        )
      },
    },
    {
      accessorKey: "start_date",
      header: ({ column }) => {
        return (
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
            Start Date
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("start_date"))
        return <div>{format(date, "MMM d, yyyy")}</div>
      },
    },
    {
      accessorKey: "end_date",
      header: "End Date",
      cell: ({ row }) => {
        const date = new Date(row.getValue("end_date"))
        return <div>{format(date, "MMM d, yyyy")}</div>
      },
    },
    {
      accessorKey: "duration",
      header: "Duration",
      cell: ({ row }) => {
        const startDate = new Date(row.original.start_date)
        const endDate = new Date(row.original.end_date)
        const days = differenceInDays(endDate, startDate) + 1
        return (
          <div>
            {days} {days === 1 ? "day" : "days"}
          </div>
        )
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <Badge
            variant={
              status === "completed"
                ? "default"
                : status === "confirmed"
                  ? "outline"
                  : status === "pending"
                    ? "secondary"
                    : "destructive"
            }
          >
            {status}
          </Badge>
        )
      },
    },
    {
      accessorKey: "total_amount",
      header: () => <div className="text-right">Amount</div>,
      cell: ({ row }) => {
        const amount = Number.parseFloat(row.getValue("total_amount"))
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount)

        return <div className="text-right font-medium">{formatted}</div>
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const booking = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => {
                  setSelectedBooking(booking)
                  setIsDetailsOpen(true)
                }}
              >
                <Calendar className="mr-2 h-4 w-4" />
                View details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setSelectedBooking(booking)
                  setNewStatus(booking.status)
                  setIsStatusUpdateOpen(true)
                }}
              >
                <FileText className="mr-2 h-4 w-4" />
                Update status
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  setSelectedBooking(booking)
                  setMessageText("")
                  setIsMessageOpen(true)
                }}
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                Message customer
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(booking.id)}>
                Copy booking ID
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data: bookings,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      rowSelection,
      columnVisibility,
    },
  })

  const handleStatusUpdate = async () => {
    if (!selectedBooking || !newStatus) return

    setIsUpdating(true)

    try {
      await updateBookingStatus(selectedBooking.id, newStatus as any)

      toast({
        title: "Status updated",
        description: `Booking status has been updated to ${newStatus}`,
      })

      // In a real app, you would refresh the data here
      // For now, we'll just close the dialog
      setIsStatusUpdateOpen(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update booking status",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const handleSendMessage = () => {
    if (!selectedBooking || !messageText) return

    // In a real app, you would send the message to the customer
    // For now, we'll just show a toast
    toast({
      title: "Message sent",
      description: `Your message has been sent to ${selectedBooking.profiles.full_name}`,
    })

    setIsMessageOpen(false)
  }

  const handleBatchAction = (action: "approve" | "decline" | "cancel") => {
    const selectedRows = table.getFilteredSelectedRowModel().rows

    if (selectedRows.length === 0) {
      toast({
        title: "No bookings selected",
        description: "Please select at least one booking to perform this action",
        variant: "destructive",
      })
      return
    }

    // In a real app, you would update the status of all selected bookings
    // For now, we'll just show a toast
    toast({
      title: "Batch action",
      description: `${action} action applied to ${selectedRows.length} bookings`,
    })
  }

  if (bookings.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <h3 className="text-lg font-medium">No bookings yet</h3>
        <p className="text-muted-foreground mt-1">When customers book your cars, they will appear here</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Input
          placeholder="Filter bookings..."
          value={(table.getColumn("profiles.full_name")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("profiles.full_name")?.setFilterValue(event.target.value)}
          className="max-w-sm"
        />
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => handleBatchAction("approve")}
            disabled={Object.keys(rowSelection).length === 0}
          >
            <Check className="mr-2 h-4 w-4" />
            Approve Selected
          </Button>
          <Button
            variant="outline"
            onClick={() => handleBatchAction("decline")}
            disabled={Object.keys(rowSelection).length === 0}
          >
            <X className="mr-2 h-4 w-4" />
            Decline Selected
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="ml-auto">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) => column.toggleVisibility(!!value)}
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s)
          selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
            Next
          </Button>
        </div>
      </div>

      {/* Booking Details Dialog */}
      {selectedBooking && (
        <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Booking Details</DialogTitle>
              <DialogDescription>Booking ID: {selectedBooking.id}</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="font-medium mb-2">Customer</h3>
                    <div className="flex items-center gap-2 mb-4">
                      <Avatar>
                        <AvatarImage src={selectedBooking.profiles.avatar_url || ""} />
                        <AvatarFallback>{selectedBooking.profiles.full_name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{selectedBooking.profiles.full_name}</p>
                        <p className="text-sm text-muted-foreground">{selectedBooking.profiles.email}</p>
                      </div>
                    </div>
                    {selectedBooking.profiles.phone && (
                      <p className="text-sm">Phone: {selectedBooking.profiles.phone}</p>
                    )}
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="font-medium mb-2">Car</h3>
                    <p className="font-medium">
                      {selectedBooking.cars.make} {selectedBooking.cars.model}
                    </p>
                    <p className="text-sm text-muted-foreground">{selectedBooking.cars.year}</p>
                    <p className="text-sm mt-2">Daily Rate: ${selectedBooking.cars.price}</p>
                  </CardContent>
                </Card>
              </div>
              <Card>
                <CardContent className="pt-6">
                  <h3 className="font-medium mb-2">Booking Information</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Start Date</p>
                      <p>{format(new Date(selectedBooking.start_date), "MMMM d, yyyy")}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">End Date</p>
                      <p>{format(new Date(selectedBooking.end_date), "MMMM d, yyyy")}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Duration</p>
                      <p>
                        {differenceInDays(new Date(selectedBooking.end_date), new Date(selectedBooking.start_date)) + 1}{" "}
                        days
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Status</p>
                      <Badge
                        variant={
                          selectedBooking.status === "completed"
                            ? "default"
                            : selectedBooking.status === "confirmed"
                              ? "outline"
                              : selectedBooking.status === "pending"
                                ? "secondary"
                                : "destructive"
                        }
                      >
                        {selectedBooking.status}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Created</p>
                      <p>{format(new Date(selectedBooking.created_at), "MMMM d, yyyy")}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Amount</p>
                      <p className="font-medium">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: "USD",
                        }).format(selectedBooking.total_amount)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDetailsOpen(false)}>
                Close
              </Button>
              <Button
                onClick={() => {
                  setIsDetailsOpen(false)
                  setNewStatus(selectedBooking.status)
                  setIsStatusUpdateOpen(true)
                }}
              >
                Update Status
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Status Update Dialog */}
      {selectedBooking && (
        <Dialog open={isStatusUpdateOpen} onOpenChange={setIsStatusUpdateOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Update Booking Status</DialogTitle>
              <DialogDescription>
                Change the status of booking {selectedBooking.id.substring(0, 8)}...
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 gap-4">
                <Button
                  variant={newStatus === "pending" ? "default" : "outline"}
                  className="w-full"
                  onClick={() => setNewStatus("pending")}
                >
                  Pending
                </Button>
                <Button
                  variant={newStatus === "confirmed" ? "default" : "outline"}
                  className="w-full"
                  onClick={() => setNewStatus("confirmed")}
                >
                  Confirmed
                </Button>
                <Button
                  variant={newStatus === "completed" ? "default" : "outline"}
                  className="w-full"
                  onClick={() => setNewStatus("completed")}
                >
                  Completed
                </Button>
                <Button
                  variant={newStatus === "cancelled" ? "default" : "outline"}
                  className="w-full"
                  onClick={() => setNewStatus("cancelled")}
                >
                  Cancelled
                </Button>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsStatusUpdateOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleStatusUpdate} disabled={isUpdating || newStatus === selectedBooking.status}>
                {isUpdating ? "Updating..." : "Update Status"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Message Customer Dialog */}
      {selectedBooking && (
        <Dialog open={isMessageOpen} onOpenChange={setIsMessageOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Message Customer</DialogTitle>
              <DialogDescription>Send a message to {selectedBooking.profiles.full_name}</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <Textarea
                placeholder="Type your message here..."
                value={messageText}
                onChange={(e) => setMessageText(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsMessageOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSendMessage} disabled={!messageText.trim()}>
                Send Message
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

