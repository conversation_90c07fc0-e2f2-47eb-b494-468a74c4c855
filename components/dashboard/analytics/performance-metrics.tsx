"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { format, subDays, subMonths } from "date-fns"

interface PerformanceMetricsProps {
  carId?: string // Optional - if provided, shows metrics for a specific car
  metrics: {
    views: PerformanceData[]
    bookings: PerformanceData[]
    revenue: PerformanceData[]
    occupancyRate: PerformanceData[]
  }
}

interface PerformanceData {
  date: string
  value: number
}

export function PerformanceMetrics({ carId, metrics }: PerformanceMetricsProps) {
  const [timeRange, setTimeRange] = useState("30days")

  // Filter data based on selected time range
  const getFilteredData = (data: PerformanceData[]) => {
    const now = new Date()
    let cutoffDate: Date

    switch (timeRange) {
      case "7days":
        cutoffDate = subDays(now, 7)
        break
      case "30days":
        cutoffDate = subDays(now, 30)
        break
      case "90days":
        cutoffDate = subDays(now, 90)
        break
      case "year":
        cutoffDate = subMonths(now, 12)
        break
      default:
        cutoffDate = subDays(now, 30)
    }

    return data.filter((item) => new Date(item.date) >= cutoffDate)
  }

  // Format data for charts
  const viewsData = getFilteredData(metrics.views)
  const bookingsData = getFilteredData(metrics.bookings)
  const revenueData = getFilteredData(metrics.revenue)
  const occupancyData = getFilteredData(metrics.occupancyRate)

  // Calculate summary metrics
  const totalViews = viewsData.reduce((sum, item) => sum + item.value, 0)
  const totalBookings = bookingsData.reduce((sum, item) => sum + item.value, 0)
  const totalRevenue = revenueData.reduce((sum, item) => sum + item.value, 0)
  const avgOccupancy =
    occupancyData.length > 0 ? occupancyData.reduce((sum, item) => sum + item.value, 0) / occupancyData.length : 0

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(value)
  }

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${Math.round(value)}%`
  }

  // Format date for tooltip
  const formatDate = (dateStr: string) => {
    return format(new Date(dateStr), "MMM d, yyyy")
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">{carId ? "Car Performance" : "Overall Performance"}</h2>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Last 7 days</SelectItem>
            <SelectItem value="30days">Last 30 days</SelectItem>
            <SelectItem value="90days">Last 90 days</SelectItem>
            <SelectItem value="year">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalViews}</div>
            <p className="text-xs text-muted-foreground">Listing page views</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Bookings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalBookings}</div>
            <p className="text-xs text-muted-foreground">Total bookings received</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">Total earnings</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(avgOccupancy)}</div>
            <p className="text-xs text-muted-foreground">Average occupancy</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="views" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="views">Views</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="occupancy">Occupancy</TabsTrigger>
        </TabsList>

        <TabsContent value="views" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Listing Views</CardTitle>
              <CardDescription>Number of times your listing was viewed</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={viewsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={(date) => format(new Date(date), "MMM d")} />
                  <YAxis />
                  <Tooltip formatter={(value) => [value, "Views"]} labelFormatter={formatDate} />
                  <Legend />
                  <Bar dataKey="value" name="Views" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bookings" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Bookings</CardTitle>
              <CardDescription>Number of bookings received</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={bookingsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={(date) => format(new Date(date), "MMM d")} />
                  <YAxis />
                  <Tooltip formatter={(value) => [value, "Bookings"]} labelFormatter={formatDate} />
                  <Legend />
                  <Bar dataKey="value" name="Bookings" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue</CardTitle>
              <CardDescription>Total earnings from bookings</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={(date) => format(new Date(date), "MMM d")} />
                  <YAxis tickFormatter={(value) => `$${value}`} />
                  <Tooltip
                    formatter={(value) => [formatCurrency(Number(value)), "Revenue"]}
                    labelFormatter={formatDate}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="value" name="Revenue" stroke="#8884d8" activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="occupancy" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Occupancy Rate</CardTitle>
              <CardDescription>Percentage of days your car is booked</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={occupancyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={(date) => format(new Date(date), "MMM d")} />
                  <YAxis tickFormatter={(value) => `${value}%`} />
                  <Tooltip formatter={(value) => [`${value}%`, "Occupancy Rate"]} labelFormatter={formatDate} />
                  <Legend />
                  <Line type="monotone" dataKey="value" name="Occupancy Rate" stroke="#82ca9d" activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

