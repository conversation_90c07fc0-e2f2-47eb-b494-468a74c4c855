"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent } from "@/components/ui/card"
import { ImageUpload } from "@/components/dashboard/image-upload"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2 } from "lucide-react"
import { createCar, updateCar } from "@/app/actions/car-actions"

const formSchema = z.object({
  title: z.string().min(2, {
    message: "Title must be at least 2 characters.",
  }),
  description: z.string().min(10, {
    message: "Description must be at least 10 characters.",
  }),
  price: z.string().min(1, {
    message: "Price is required",
  }),
  location: z.string().min(2, {
    message: "Location is required",
  }),
  year: z.string().min(4, {
    message: "Year is required",
  }),
  make: z.string().min(1, {
    message: "Make is required",
  }),
  model: z.string().min(1, {
    message: "Model is required",
  }),
  features: z.array(z.string()).optional(),
  status: z.string(),
  images: z.array(z.string()).min(1, {
    message: "At least one image is required",
  }),
})

const features = [
  { id: "electric", label: "Electric" },
  { id: "hybrid", label: "Hybrid" },
  { id: "leather", label: "Leather Seats" },
  { id: "navigation", label: "Navigation" },
  { id: "bluetooth", label: "Bluetooth" },
  { id: "camera", label: "Backup Camera" },
  { id: "sunroof", label: "Sunroof" },
  { id: "heated", label: "Heated Seats" },
  { id: "audio", label: "Premium Audio" },
]

interface CarFormProps {
  car?: {
    id: string
    owner_id: string;
    registration_number: string
    vin?: string
    brand: string
    title: string
    description: string | null
    make: string
    model: string
    year: number
    seats: number
    is_electric: boolean
    fuel_type: string
    guidelines?: string
    daily_rate: number
    location: string
    status: string
    available: boolean
    car_features: { id: string; feature: string }[]
    car_images: { id: string; url: string }[]
  }
}

export function CarForm({ car }: CarFormProps) {
  const router = useRouter()
  const [images, setImages] = useState<string[]>(car?.car_images.map((img) => img.url) || [])
  const [error, setError] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: car?.title || "",
      description: car?.description || "",
      price: car?.daily_rate ? car.daily_rate.toString() : "",
      location: car?.location || "",
      year: car?.year ? car.year.toString() : new Date().getFullYear().toString(),
      make: car?.make || "",
      model: car?.model || "",
      features: car?.car_features.map((f) => f.feature) || [],
      status: car?.status || "active",
      images: car?.car_images.map((img) => img.url) || [],
    },
  })

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)
    setError(null)

    try {
      const formData = new FormData()

      // Add all form fields to FormData
      formData.append("title", values.title)
      formData.append("description", values.description)
      formData.append("make", values.make)
      formData.append("model", values.model)
      formData.append("year", values.year)
      formData.append("price", values.price)
      formData.append("location", values.location)
      formData.append("status", values.status)

      // Add features as multiple values
      if (values.features && values.features.length > 0) {
        values.features.forEach((feature) => {
          formData.append("features", feature)
        })
      }

      // Add images as multiple values
      if (values.images && values.images.length > 0) {
        values.images.forEach((image) => {
          formData.append("images", image)
        })
      }

      // Submit the form
      if (car) {
        // Update existing car
        const result = await updateCar(car.id, formData)
        if (result.error) {
          setError(result.error)
        }
      } else {
        // Create new car
        const result = await createCar(formData)
        if (result.error) {
          setError(result.error)
        }
      }
    } catch (error) {
      console.error("Form submission error:", error)
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleImageUpload = (url: string) => {
    setImages([...images, url])
    form.setValue("images", [...images, url])
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Card>
          <CardContent className="pt-6">
            <div className="grid gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Tesla Model 3" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Daily Rate</FormLabel>
                    <FormControl>
                      <Input placeholder="75" {...field} />
                    </FormControl>
                    <FormDescription>Price per day in USD</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="make"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Make</FormLabel>
                    <FormControl>
                      <Input placeholder="Tesla" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Model</FormLabel>
                    <FormControl>
                      <Input placeholder="Model 3" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="year"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Year</FormLabel>
                    <FormControl>
                      <Input placeholder="2023" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input placeholder="San Francisco, CA" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                        <SelectItem value="unavailable">Unavailable</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Describe your car" className="min-h-[120px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <FormLabel className="mb-4 block">Features</FormLabel>
            <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
              {features.map((feature) => (
                <FormField
                  key={feature.id}
                  control={form.control}
                  name="features"
                  render={({ field }) => {
                    return (
                      <FormItem key={feature.id} className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(feature.id)}
                            onCheckedChange={(checked) => {
                              return checked
                                ? field.onChange([...(field.value || []), feature.id])
                                : field.onChange(field.value?.filter((value) => value !== feature.id))
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">{feature.label}</FormLabel>
                      </FormItem>
                    )
                  }}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <FormField
              control={form.control}
              name="images"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Images</FormLabel>
                  <FormControl>
                    <ImageUpload onUpload={handleImageUpload} images={images} />
                  </FormControl>
                  <FormDescription>Upload at least one image of your car</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button variant="outline" type="button" onClick={() => router.back()} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {car ? "Update Car" : "Save Car"}
          </Button>
        </div>
      </form>
    </Form>
  )
}

