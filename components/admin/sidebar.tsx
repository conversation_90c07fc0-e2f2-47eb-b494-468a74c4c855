"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { BarChart3, Car, CreditCard, LayoutDashboard, LogOut, Settings, Shield, Users, HeadphonesIcon, List, Banknote, NotebookPen } from "lucide-react"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarSeparator,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import Image from "next/image"

export function AdminSidebar() {
  const pathname = usePathname()

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`)
  }

  return (
    <Sidebar className="border-r border-border/40 bg-card/30 backdrop-blur-sm">
      <SidebarHeader className="flex items-center justify-center py-6">
        <Link href="/admin" className="flex items-center gap-2 font-bold text-xl">
          <Image src="/svg/travella_logo.svg" alt="Travella Logo" width={32} height={32} />
          <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">Admin Panel</span>
        </Link>
      </SidebarHeader>
      <SidebarSeparator className="opacity-50" />
      <SidebarContent className="px-2 py-4">
        <div className="mb-4 px-4">
          <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Main</p>
        </div>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin") && pathname === "/admin"}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin">
                <LayoutDashboard className="h-4 w-4" />
                <span>Dashboard</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/users")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/users">
                <Users className="h-4 w-4" />
                <span>Users</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/cars")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/cars">
                <Car className="h-4 w-4" />
                <span>Car Listings</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/bookings")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/bookings">
                <NotebookPen className="h-4 w-4" />
                <span>Bookings</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        <div className="mt-8 mb-4 px-4">
          <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            Support
          </p>
        </div>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/support")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/support">
                <HeadphonesIcon className="h-4 w-4" />
                <span>Support Tickets</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/refunds")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/refunds">
                <Banknote className="h-4 w-4" />
                <span>Booking Refunds</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/hosts")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/hosts">
                <Users className="h-4 w-4" />
                <span>Hosts</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/payouts")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/payouts">
                <CreditCard className="h-4 w-4" />
                <span>Payouts</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/payments")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/payments">
                <Car className="h-4 w-4" />
                <span>Booking Payments</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        <div className="mt-8 mb-4 px-4">
          <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Reports</p>
        </div>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/analytics")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/analytics">
                <BarChart3 className="h-4 w-4" />
                <span>Analytics</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        <div className="mt-8 mb-4 px-4">
          <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">System</p>
        </div>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/settings")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/settings">
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/admin/listings")}
              className="mb-1 transition-all hover:bg-accent/50"
            >
              <Link href="/admin/listings">
                <List className="h-4 w-4" />
                <span>Listings</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="border-t border-border/40 p-4">
        <Button variant="outline" className="w-full justify-start" asChild>
          <Link href="/api/auth/signout">
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </Link>
        </Button>
      </SidebarFooter>
      <SidebarRail className="bg-card/30 backdrop-blur-sm" />
    </Sidebar>
  )
}

