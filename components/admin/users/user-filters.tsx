"use client"

import { useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { UserRole } from "@/types/users"
import { Search, X } from "lucide-react"

interface UserFiltersProps {
  currentSearch?: string
  currentRole?: UserRole
}

export function UserFilters({ currentSearch, currentRole }: UserFiltersProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [search, setSearch] = useState(currentSearch || "")
  const [role, setRole] = useState<UserRole | undefined>(currentRole)

  const applyFilters = () => {
    const params = new URLSearchParams()
    
    if (search) params.set("search", search)
    if (role) params.set("role", role)
    
    // Reset to page 1 when filters change
    params.set("page", "1")
    
    router.push(`${pathname}?${params.toString()}`)
  }

  const clearFilters = () => {
    setSearch("")
    setRole(undefined)
    router.push(pathname)
  }

  return (
    <div className="flex flex-col sm:flex-row gap-3 mt-4">
      <div className="relative flex-1">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search users..."
          className="pl-8"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && applyFilters()}
        />
      </div>
      
      <Select
        value={role}
        onValueChange={(value) => setRole(value as UserRole)}
      >
        <SelectTrigger className="w-full sm:w-[180px]">
          <SelectValue placeholder="Filter by role" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={UserRole.ADMIN}>Admin</SelectItem>
          <SelectItem value={UserRole.USER}>User</SelectItem>
        </SelectContent>
      </Select>
      
      <Button onClick={applyFilters}>Apply Filters</Button>
      
      {(search || role) && (
        <Button variant="outline" onClick={clearFilters}>
          <X className="mr-2 h-4 w-4" />
          Clear
        </Button>
      )}
    </div>
  )
} 