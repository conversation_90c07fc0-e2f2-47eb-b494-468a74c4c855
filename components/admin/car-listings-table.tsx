"use client"

import { useState } from "react"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, CheckCircle, XCircle, Eye } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { approveCarListing, rejectCarListing } from "@/app/actions/admin/admin-actions"
import { toast } from "sonner"
import Link from "next/link"

interface CarListingsTableProps {
  carListings: any[]
  onAction?: () => void
}

export function CarListingsTable({ carListings, onAction }: CarListingsTableProps) {
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedCar, setSelectedCar] = useState<any>(null)
  const [rejectionReason, setRejectionReason] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleApprove = async (carId: string) => {
    try {
      setIsSubmitting(true)
      await approveCarListing(carId)
      toast.success("Car listing approved successfully")
      if (onAction) onAction()
    } catch (error) {
      console.error("Error approving car:", error)
      toast.error("Failed to approve car listing")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleReject = async () => {
    if (!selectedCar || !rejectionReason.trim()) return

    try {
      setIsSubmitting(true)
      await rejectCarListing(selectedCar.id, rejectionReason)
      toast.success("Car listing rejected successfully")
      setIsRejectDialogOpen(false)
      setRejectionReason("")
      if (onAction) onAction()
    } catch (error) {
      console.error("Error rejecting car:", error)
      toast.error("Failed to reject car listing")
    } finally {
      setIsSubmitting(false)
    }
  }

  const openRejectDialog = (car: any) => {
    setSelectedCar(car)
    setIsRejectDialogOpen(true)
  }

  const openViewDialog = (car: any) => {
    setSelectedCar(car)
    setIsViewDialogOpen(true)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Car</TableHead>
              <TableHead>Owner</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {carListings.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                  No car listings found
                </TableCell>
              </TableRow>
            ) : (
              carListings.map((car) => (
                <TableRow key={car.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="h-12 w-12 rounded-md overflow-hidden bg-muted">
                        {car.car_images && car.car_images.length > 0 ? (
                          <img 
                            src={car.car_images[0].url} 
                            alt={`${car.brand} ${car.model}`}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center bg-muted">
                            <span className="text-xs text-muted-foreground">No image</span>
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-medium">{car.year} {car.brand} {car.model}</p>
                        <p className="text-xs text-muted-foreground">
                          {car.seats} seats • {car.fuel_type}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {car.owner && (
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={car.owner.avatar_url} />
                          <AvatarFallback>
                            {car.owner.first_name?.charAt(0) || "U"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">
                            {car.owner.first_name} {car.owner.last_name}
                          </p>
                          <p className="text-xs text-muted-foreground">{car.owner.email}</p>
                        </div>
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    ${car.daily_rate}/day
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(car.status)}
                  </TableCell>
                  <TableCell>
                    {formatDate(car.created_at)}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openViewDialog(car)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View details
                        </DropdownMenuItem>
                        {car.status === "pending" && (
                          <>
                            <DropdownMenuItem 
                              onClick={() => handleApprove(car.id)}
                              disabled={isSubmitting}
                            >
                              <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                              Approve
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => openRejectDialog(car)}
                              disabled={isSubmitting}
                            >
                              <XCircle className="mr-2 h-4 w-4 text-red-500" />
                              Reject
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Reject Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Car Listing</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this car listing. This will be sent to the car owner.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="reason">Rejection Reason</Label>
              <Input
                id="reason"
                placeholder="Enter reason for rejection"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsRejectDialogOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleReject}
              disabled={isSubmitting || !rejectionReason.trim()}
            >
              {isSubmitting ? "Rejecting..." : "Reject Listing"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Car Listing Details</DialogTitle>
          </DialogHeader>
          {selectedCar && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2">
                    {selectedCar.year} {selectedCar.brand} {selectedCar.model}
                  </h3>
                  <div className="space-y-2">
                    <p><span className="font-medium">Status:</span> {getStatusBadge(selectedCar.status)}</p>
                    <p><span className="font-medium">Daily Rate:</span> ${selectedCar.daily_rate}/day</p>
                    <p><span className="font-medium">Seats:</span> {selectedCar.seats}</p>
                    <p><span className="font-medium">Fuel Type:</span> {selectedCar.fuel_type}</p>
                    <p><span className="font-medium">Registration:</span> {selectedCar.registration_number}</p>
                    {selectedCar.vin && <p><span className="font-medium">VIN:</span> {selectedCar.vin}</p>}
                    <p><span className="font-medium">Created:</span> {formatDate(selectedCar.created_at)}</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Owner Information</h3>
                  {selectedCar.owner && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 mb-2">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={selectedCar.owner.avatar_url} />
                          <AvatarFallback>
                            {selectedCar.owner.first_name?.charAt(0) || "U"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">
                            {selectedCar.owner.first_name} {selectedCar.owner.last_name}
                          </p>
                        </div>
                      </div>
                      <p><span className="font-medium">Email:</span> {selectedCar.owner.email}</p>
                      <p><span className="font-medium">Phone:</span> {selectedCar.owner.phone || "Not provided"}</p>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Description</h3>
                <p className="text-sm text-muted-foreground">
                  {selectedCar.description || "No description provided"}
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Images</h3>
                <div className="grid grid-cols-3 gap-2">
                  {selectedCar.car_images && selectedCar.car_images.length > 0 ? (
                    selectedCar.car_images.map((image: any) => (
                      <div key={image.id} className="h-24 rounded-md overflow-hidden">
                        <img 
                          src={image.url} 
                          alt={`${selectedCar.brand} ${selectedCar.model}`}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ))
                  ) : (
                    <div className="col-span-3 h-24 flex items-center justify-center bg-muted rounded-md">
                      <span className="text-muted-foreground">No images available</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setIsViewDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
