"use client"

import { useEffect, useState } from "react"
import { getHostPaymentAccounts, getHostPayoutStatus, getHostPayouts } from "@/app/actions/admin/hosts"
import { HostPaymentAccount, HostPayoutStats, Payout, PayoutStatus } from "@/types/hosts"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Banknote, CreditCard, Smartphone, AlertCircle } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface HostPaymentDetailsModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  hostId: string | null
}

export default function HostPaymentDetailsModal({
  isOpen,
  onOpenChange,
  hostId
}: HostPaymentDetailsModalProps) {
  const [paymentAccounts, setPaymentAccounts] = useState<HostPaymentAccount[]>([])
  const [payoutStats, setPayoutStats] = useState<HostPayoutStats | null>(null)
  const [pendingPayouts, setPendingPayouts] = useState<Payout[]>([])
  const [loadingAccounts, setLoadingAccounts] = useState(false)
  const [loadingStats, setLoadingStats] = useState(false)
  const [loadingPayouts, setLoadingPayouts] = useState(false)
  const [activeTab, setActiveTab] = useState("accounts")

  useEffect(() => {
    if (isOpen && hostId) {
        Promise.all([
            fetchPaymentAccounts(),
            fetchPayoutStats(),
            fetchPendingPayouts()
        ])
    } else {
      // Reset state when modal closes
      setPaymentAccounts([])
      setPayoutStats(null)
      setPendingPayouts([])
    }

    return () => {
      setPaymentAccounts([])
      setPayoutStats(null)
      setPendingPayouts([])
      setLoadingAccounts(false)
      setLoadingStats(false)
      setLoadingPayouts(false)
      setActiveTab("accounts")
    }
  }, [isOpen, hostId])

  const fetchPaymentAccounts = async () => {
    if (!hostId) return
    
    try {
      setLoadingAccounts(true)
      const accounts = await getHostPaymentAccounts({ hostId })
      setPaymentAccounts(accounts || [])
    } catch (error) {
      console.error("Failed to fetch payment accounts:", error)
    } finally {
      setLoadingAccounts(false)
    }
  }

  const fetchPayoutStats = async () => {
    if (!hostId) return
    
    try {
      setLoadingStats(true)
      const stats = await getHostPayoutStatus(hostId)
      setPayoutStats(stats)
    } catch (error) {
      console.error("Failed to fetch payout stats:", error)
    } finally {
      setLoadingStats(false)
    }
  }

  const fetchPendingPayouts = async () => {
    if (!hostId) return
    
    try {
      setLoadingPayouts(true)
      const payouts = await getHostPayouts({
        itemsPerPage: 10,
        page: 1,
        status: PayoutStatus.PENDING,
        hostId,
        booking_status: null,
        host_name: null
      })
      setPendingPayouts(payouts || [])
    } catch (error) {
      console.error("Failed to fetch pending payouts:", error)
    } finally {
      setLoadingPayouts(false)
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'bank_transfer':
        return <Banknote className="h-5 w-5" />
      case 'mobile_money':
        return <Smartphone className="h-5 w-5" />
      default:
        return <CreditCard className="h-5 w-5" />
    }
  }

  const getIsPrimaryBadge = (isPrimary: boolean) => {
    return isPrimary ? <Badge className="bg-green-500">Primary</Badge> : null
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>
      case "inactive":
        return <Badge className="bg-red-500">Inactive</Badge>
      case "completed":
        return <Badge className="bg-green-500">Completed</Badge>
      case "processing":
        return <Badge className="bg-blue-500">Processing</Badge>
      case "failed":
        return <Badge className="bg-red-500">Failed</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getBookingStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-500">Completed</Badge>
      case "cancelled":
        return <Badge className="bg-red-500">Cancelled</Badge>
      case "active":
        return <Badge className="bg-blue-500">Active</Badge>
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const truncateId = (id: string) => {
    return id.length > 8 ? `${id.substring(0, 8)}...` : id;
  }

  if (!hostId) return null

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Host Payment Details</DialogTitle>
          <DialogDescription>
            View payment accounts and payout statistics for this host
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="accounts" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="accounts">Payment Accounts</TabsTrigger>
            <TabsTrigger value="stats">Payout Statistics</TabsTrigger>
            <TabsTrigger value="pending">Pending Payouts</TabsTrigger>
          </TabsList>
          
          <TabsContent value="accounts" className="mt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Payment Methods</h3>
              
              {loadingAccounts ? (
                <div className="space-y-4">
                  <Skeleton className="h-32 w-full" />
                  <Skeleton className="h-32 w-full" />
                </div>
              ) : paymentAccounts.length === 0 ? (
                <Card>
                  <CardContent className="flex items-center justify-center p-6">
                    <div className="text-center">
                      <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-muted-foreground">No payment accounts found for this host</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {paymentAccounts.map((account) => (
                    <Card key={account.id}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getPaymentMethodIcon(account.payout_method)}
                            <CardTitle className="text-base capitalize">
                              {account.payout_method.replace('_', ' ')}
                            </CardTitle>
                          </div>
                          {getIsPrimaryBadge(account.is_primary)}
                        </div>
                        <CardDescription>
                          Added on {new Date(account.created_at).toLocaleDateString()}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        {account.payout_method === 'bank_transfer' && (
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-xs text-muted-foreground">Bank Name</p>
                              <p className="text-sm font-medium">{account.bank_name || 'N/A'}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Account Number</p>
                              <p className="text-sm font-medium">
                                {account.account_number || 'N/A'}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Account Holder</p>
                              <p className="text-sm font-medium">{account.account_holder_name || 'N/A'}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Branch</p>
                              <p className="text-sm font-medium">{account.bank_branch || 'N/A'}</p>
                            </div>
                          </div>
                        )}
                        
                        {account.payout_method === 'mobile_money' && (
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-xs text-muted-foreground">Phone Number</p>
                              <p className="text-sm font-medium">{account.phone_number || 'N/A'}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Provider</p>
                              <p className="text-sm font-medium">{account.mobile_provider || 'N/A'}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Account Name</p>
                              <p className="text-sm font-medium">{account.account_holder_name || 'N/A'}</p>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="stats" className="mt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Payout Statistics</h3>
              
              {loadingStats ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-24 w-full" />
                  </div>
                  <Skeleton className="h-48 w-full" />
                </div>
              ) : !payoutStats ? (
                <Card>
                  <CardContent className="flex items-center justify-center p-6">
                    <div className="text-center">
                      <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-muted-foreground">No payout statistics available for this host</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Total</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {formatCurrency(payoutStats.total_payouts_count || 0)}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {payoutStats.total_payouts_count || 0} payouts
                        </p>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Pending</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-yellow-500">
                          {formatCurrency(payoutStats.pending_amount || 0)}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {payoutStats.pending_payouts_count || 0} payouts
                        </p>
                      </CardContent>
                    </Card>
                    
                    {/* <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Completed</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-green-500">
                          {formatCurrency(payoutStats.paid_amount || 0)}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {payoutStats.paid_payouts_count || 0} payouts
                        </p>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Failed</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-red-500">
                          {formatCurrency(payoutStats.failed_amount || 0)}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {payoutStats.failed_count || 0} payouts
                        </p>
                      </CardContent>
                    </Card> */}
                  </div>
                  
                  {/* <Card className="mt-6">
                    <CardHeader>
                      <CardTitle>Payout Processing</CardTitle>
                      <CardDescription>
                        Current processing status for this host
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-sm font-medium mb-2">Processing</h4>
                          <div className="flex items-center justify-between">
                            <span>Amount:</span>
                            <span className="font-medium text-blue-500">
                              {formatCurrency(payoutStats.processing_amount || 0)}
                            </span>
                          </div>
                          <div className="flex items-center justify-between mt-1">
                            <span>Count:</span>
                            <span>{payoutStats.processing_count || 0} payouts</span>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium mb-2">Last Payout</h4>
                          <div className="flex items-center justify-between">
                            <span>Amount:</span>
                            <span className="font-medium">
                              {formatCurrency(payoutStats.last_payout_amount || 0)}
                            </span>
                          </div>
                          <div className="flex items-center justify-between mt-1">
                            <span>Date:</span>
                            <span>
                              {payoutStats.last_payout_date 
                                ? new Date(payoutStats.last_payout_date).toLocaleDateString() 
                                : 'N/A'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card> */}
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="pending" className="mt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Pending Payouts</h3>
              
              {loadingPayouts ? (
                <div className="space-y-2">
                  {Array(5).fill(0).map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : pendingPayouts.length === 0 ? (
                <Card>
                  <CardContent className="flex items-center justify-center p-6">
                    <div className="text-center">
                      <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-muted-foreground">No pending payouts found for this host</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="border rounded-md overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Amount</TableHead>
                        <TableHead>Fees</TableHead>
                        <TableHead>Net Amount</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Booking Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {pendingPayouts.map((payout) => (
                        <TableRow key={payout.id}>
                          <TableCell>
                            {formatCurrency(payout.amount)}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(payout.amount * 0.10)}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(payout.amount - (payout.amount * 0.10))}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(payout.payout_status)}
                          </TableCell>
                          <TableCell>
                            {getBookingStatusBadge(payout.booking_status)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
} 