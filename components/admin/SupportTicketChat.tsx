"use client"

import { useState, useEffect, useCallback, useRef, memo } from "react";
import { format } from "date-fns";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import {
  Send,
  CheckCircle,
  AlertCircle,
  Clock,
  UserCog,
  ArrowUp
} from "lucide-react";
import { 
  getSupportTicketMessageById,
  getSupportTicketMessages, 
  sendSupportTicketMessage,
  updateSupportTicketStatus
} from "@/app/actions/admin/admin-actions";
import { SupportTicket } from "@/types/support-tickets";
import { SupportTicketMessage } from "@/types/support-messages";
import { useToast } from "@/hooks/use-toast";
import { createClient } from "@/lib/supabase-client";
import { RealtimeChannel } from "@supabase/supabase-js";

interface SupportTicketChatProps {
  ticket: SupportTicket | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onStatusChange: (ticketId: string, status: 'open' | 'in_progress' | 'resolved' | 'closed') => void;
  currentUserId: string;
  inlineMode?: boolean;
}

// Message component for better organization
const ChatMessage = ({ 
  message, 
  isCurrentUser 
}: { 
  message: SupportTicketMessage; 
  isCurrentUser: boolean;
}) => {
  return (
    <div 
      className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-4`}
    >
      {!isCurrentUser && (
        <div className="mr-2 flex-shrink-0">
          {message.sender_avatar_url ? (
            <Image 
              src={message.sender_avatar_url} 
              alt={message.sender_first_name}
              width={32} 
              height={32}
              className="rounded-full" 
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center text-xs font-medium">
              {message.sender_first_name?.[0]?.toUpperCase() || '?'}
            </div>
          )}
        </div>
      )}
      <div 
        className={`max-w-[80%] rounded-lg p-3 ${
          isCurrentUser 
            ? 'bg-primary text-primary-foreground rounded-tr-none' 
            : 'bg-muted rounded-tl-none'
        } ${message.isOptimistic ? 'opacity-70' : ''}`}
      >
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs font-semibold">
            {`${message.sender_first_name} ${message.sender_last_name}`}
          </span>
          {message.isOptimistic && (
            <span className="text-xs text-muted-foreground">(Sending...)</span>
          )}
        </div>
        <p className="text-sm whitespace-pre-wrap break-words">{message.content}</p>
        <div className="flex justify-end mt-1">
          <span className="text-xs opacity-70">
            {format(new Date(message.created_at), "MMM d, h:mm a")}
          </span>
        </div>
      </div>
      {isCurrentUser && (
        <div className="ml-2 flex-shrink-0">
          {message.sender_avatar_url ? (
            <Image 
              src={message.sender_avatar_url} 
              alt="You"
              width={32} 
              height={32}
              className="rounded-full" 
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-xs font-medium text-primary-foreground">
              A
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Status Badge component
const StatusBadge = ({ status }: { status: string }) => {
  switch (status) {
    case "open":
      return <Badge className="bg-blue-500 text-white">Open</Badge>;
    case "in_progress":
      return <Badge className="bg-yellow-500 text-white">In Progress</Badge>;
    case "resolved":
      return <Badge className="bg-green-500 text-white">Resolved</Badge>;
    case "closed":
      return <Badge variant="outline" className="text-slate-500 border-slate-500">Closed</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
};

// Type Badge component
const TypeBadge = ({ type }: { type: string }) => {
  switch (type) {
    case "technical":
      return <Badge variant="outline" className="text-purple-600 border-purple-600">Technical</Badge>;
    case "billing":
      return <Badge variant="outline" className="text-orange-600 border-orange-600">Billing</Badge>;
    case "account":
      return <Badge variant="outline" className="text-blue-600 border-blue-600">Account</Badge>;
    default:
      return <Badge variant="outline">Other</Badge>;
  }
};

// Create a dedicated message input component to maintain focus
const MessageInput = memo(({ 
  onSendMessage, 
  disabled,
  ticketStatus 
}: { 
  onSendMessage: (message: string) => void;
  disabled: boolean;
  ticketStatus: string;
}) => {
  const [inputValue, setInputValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  }, []);
  
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && inputValue.trim()) {
      e.preventDefault();
      onSendMessage(inputValue);
      setInputValue("");
    }
  }, [inputValue, onSendMessage]);
  
  const handleSend = useCallback(() => {
    if (inputValue.trim()) {
      onSendMessage(inputValue);
      setInputValue("");
      // Focus back on input after sending
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  }, [inputValue, onSendMessage]);
  
  // Focus the input when the component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);
  
  return (
    <div className="p-4 border-t">
      <div className="flex items-center gap-2">
        <Input
          ref={inputRef}
          placeholder="Type your message here..."
          value={inputValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          disabled={disabled}
        />
        <Button 
          size="icon"
          disabled={!inputValue.trim() || disabled}
          onClick={handleSend}
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
      {ticketStatus === 'closed' && (
        <p className="text-xs text-muted-foreground mt-2 text-center">
          This ticket is closed. Update the status to continue the conversation.
        </p>
      )}
    </div>
  );
});

// Add a display name to the memo component
MessageInput.displayName = "MessageInput";

export function SupportTicketChat({
  ticket,
  isOpen,
  onOpenChange,
  onStatusChange,
  currentUserId,
  inlineMode = false
}: SupportTicketChatProps) {
  const supabase = createClient();
  const { toast } = useToast();
  const [messages, setMessages] = useState<SupportTicketMessage[]>([]);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(false);
  const [totalMessages, setTotalMessages] = useState(0);
  const [sendingMessage, setSendingMessage] = useState(false);
  const subscriptionRef = useRef<RealtimeChannel | null>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [loadingOlderMessages, setLoadingOlderMessages] = useState(false);

  // Fetch messages for the ticket
  const fetchMessages = useCallback(async (ticketId: string, limit = 20, offset = 0) => {
    try {
      setMessagesLoading(true);
      const result = await getSupportTicketMessages(ticketId, limit, offset);
      return result;
    } catch (error) {
      console.error("Error fetching ticket messages:", error);
      toast({
        title: "Error",
        description: "Failed to load messages",
        variant: "destructive",
      });
      throw error;
    } finally {
      setMessagesLoading(false);
    }
  }, [toast]);

  // Initial loading of messages
  useEffect(() => {
    if (!ticket || !isOpen) return;
    
    const loadInitialMessages = async () => {
      try {
        const result = await fetchMessages(ticket.id);
        setMessages(result.messages || []);
        setHasMoreMessages(result.hasMore);
        setTotalMessages(result.total);
      } catch (error) {
        // Error already handled in fetchMessages
      }
    };

    loadInitialMessages();
    
    return () => {
      // Clear messages when closing the sheet
      setMessages([]);
    };
  }, [isOpen, ticket, fetchMessages]);

  // Handle new message from realtime subscription
  const handleNewMessage = useCallback((newMessage: SupportTicketMessage) => {
    setMessages(prev => {
      // Check if this message already exists in our state
      if (prev.some(msg => msg.id === newMessage.id)) {
        return prev;
      }
      
      // Add the new message at the beginning (since we're showing newest first)
      return [newMessage, ...prev];
    });
    
    // Update total message count
    setTotalMessages(prev => prev + 1);
    
    // Display a notification if the message is from the user (not from current admin)
    if (!newMessage.is_support && newMessage.sender_id !== currentUserId) {
      toast({
        title: "New message",
        description: `${newMessage.sender_first_name} sent a new message`,
      });
    }
  }, [currentUserId, toast]);

  // Set up real-time listener for new messages
  useEffect(() => {
    if (!ticket || !isOpen) return;

    // Set up subscription for new messages
    const channel = supabase
      .channel(`support-ticket-${ticket.id}`)
      .on('postgres_changes', {
        event: 'INSERT', 
        schema: 'public', 
        table: 'support_messages',
        filter: `ticket_id=eq.${ticket.id}`
      }, async (payload) => {
        const message = await getSupportTicketMessageById(payload.new.id);
        handleNewMessage(message);
      })
      .subscribe();
    
    // Save subscription reference for cleanup
    subscriptionRef.current = channel;

    // Cleanup subscription on unmount or when ticket changes
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
    };
  }, [ticket, isOpen, handleNewMessage, supabase]);

  // Scroll detection for loading older messages
  useEffect(() => {
    const messagesContainer = messagesContainerRef.current;
    if (!messagesContainer) return;

    const handleScroll = () => {
      // If we're near the top and have more messages to load
      if (
        messagesContainer.scrollTop < 100 && 
        hasMoreMessages && 
        !loadingOlderMessages && 
        messages.length > 0
      ) {
        loadOlderMessages();
      }
    };

    messagesContainer.addEventListener('scroll', handleScroll);
    return () => messagesContainer.removeEventListener('scroll', handleScroll);
  }, [hasMoreMessages, messages.length, loadingOlderMessages]);

  // Load older messages when scrolling up
  const loadOlderMessages = async () => {
    if (!ticket || !hasMoreMessages || loadingOlderMessages) return;
    
    try {
      setLoadingOlderMessages(true);
      const result = await fetchMessages(ticket.id, 20, messages.length);
      
      // Keep track of current scroll position
      const container = messagesContainerRef.current;
      const oldScrollHeight = container?.scrollHeight || 0;
      
      // Append older messages at the end (since we're showing newest first)
      setMessages(prev => [...prev, ...(result.messages || [])]);
      setHasMoreMessages(result.hasMore);
      
      // Restore scroll position after new messages are added
      setTimeout(() => {
        if (container) {
          const newScrollHeight = container.scrollHeight;
          container.scrollTop = newScrollHeight - oldScrollHeight;
        }
      }, 0);
    } catch (error) {
      // Error already handled in fetchMessages
    } finally {
      setLoadingOlderMessages(false);
    }
  };

  // Memoize the send message function first
  const handleSendMessage = useCallback(async (messageContent: string) => {
    if (!ticket || !messageContent.trim() || sendingMessage) return;
    
    try {
      setSendingMessage(true);
      
      // Actually send the message
      await sendSupportTicketMessage(ticket.id, messageContent);
      
      // Update total count even if real-time fails
      setTotalMessages(prev => prev + 1);
      
    } catch (error) {
      console.error("Error sending message:", error);
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSendingMessage(false);
    }
  }, [ticket, sendingMessage, toast]);

  // Update ticket status
  const handleUpdateStatus = useCallback(async (status: 'open' | 'in_progress' | 'resolved' | 'closed') => {
    if (!ticket) return;

    try {
      await updateSupportTicketStatus(ticket.id, status);
      
      // Notify parent component
      onStatusChange(ticket.id, status);
      
      toast({
        title: "Status updated",
        description: `Ticket status updated to ${status.replace('_', ' ')}`,
      });
    } catch (error) {
      console.error("Error updating ticket status:", error);
      toast({
        title: "Error",
        description: "Failed to update ticket status",
        variant: "destructive",
      });
    }
  }, [ticket, onStatusChange, toast]);
  
  // The main chat content component extracted for reuse in both modes
  const ChatContent = () => (
    <>
      <div className="p-6 border-b">
        <div className="flex justify-between items-start">
          <div>
            <div className="mb-1 flex items-center gap-2">
              <StatusBadge status={ticket!.status} />
              <TypeBadge type={ticket!.type} />
            </div>
            <div className="text-base font-medium text-foreground">
              {ticket!.brand} {ticket!.model} ({ticket!.year})
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {format(new Date(ticket!.start_date), "MMM d")} - {format(new Date(ticket!.end_date), "MMM d, yyyy")}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-end">
              <span className="text-sm font-medium">
                {ticket!.reporter_first_name} {ticket!.reporter_last_name}
              </span>
              <span className="text-xs text-muted-foreground">Reporter</span>
            </div>
            {ticket!.reporter_avatar && (
              <Image 
                src={ticket!.reporter_avatar} 
                alt={ticket!.reporter_first_name} 
                width={40} 
                height={40} 
                className="rounded-full"
              />
            )}
          </div>
        </div>
        
        {/* Ticket Status Buttons */}
        <div className="mt-4 flex flex-wrap gap-2">
          <Button 
            size="sm" 
            variant={ticket!.status === 'open' ? 'default' : 'outline'}
            onClick={() => handleUpdateStatus('open')}
            disabled={ticket!.status === 'open'}
          >
            <AlertCircle className="h-4 w-4 mr-1" /> Open
          </Button>
          <Button 
            size="sm" 
            variant={ticket!.status === 'in_progress' ? 'default' : 'outline'}
            onClick={() => handleUpdateStatus('in_progress')}
            disabled={ticket!.status === 'in_progress'}
          >
            <Clock className="h-4 w-4 mr-1" /> In Progress
          </Button>
          <Button 
            size="sm" 
            variant={ticket!.status === 'resolved' ? 'default' : 'outline'}
            onClick={() => handleUpdateStatus('resolved')}
            disabled={ticket!.status === 'resolved'}
          >
            <CheckCircle className="h-4 w-4 mr-1" /> Resolved
          </Button>
          <Button 
            size="sm" 
            variant={ticket!.status === 'closed' ? 'default' : 'outline'}
            onClick={() => handleUpdateStatus('closed')}
            disabled={ticket!.status === 'closed'}
          >
            <UserCog className="h-4 w-4 mr-1" /> Closed
          </Button>
        </div>
      </div>
      
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-6 flex flex-col-reverse"
      >
        {messagesLoading && messages.length === 0 ? (
          // Loading skeletons for messages
          Array(5).fill(0).map((_, i) => (
            <div key={`msg-skeleton-${i}`} className={`flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'} mb-4`}>
              {i % 2 === 0 && <Skeleton className="h-8 w-8 rounded-full mr-2" />}
              <div className={`max-w-[80%] ${i % 2 === 0 ? 'bg-muted' : 'bg-primary text-primary-foreground'} rounded-lg p-3`}>
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-10 w-full" />
                <div className="flex justify-end mt-2">
                  <Skeleton className="h-3 w-16" />
                </div>
              </div>
              {i % 2 !== 0 && <Skeleton className="h-8 w-8 rounded-full ml-2" />}
            </div>
          ))
        ) : (
          <>
            {messages.length === 0 ? (
              <div className="text-center text-muted-foreground py-10">
                No messages yet. Start the conversation!
              </div>
            ) : (
              <>
                {messages.map((message) => (
                  <ChatMessage 
                    key={message.id || message.tempId} 
                    message={message} 
                    isCurrentUser={message.is_support}
                  />
                ))}
                
                {/* Loading indicator for older messages */}
                {loadingOlderMessages && (
                  <div className="flex justify-center py-2">
                    <div className="animate-spin h-5 w-5 text-primary">
                      <ArrowUp />
                    </div>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
      
      {/* Use the new MessageInput component */}
      <MessageInput 
        onSendMessage={handleSendMessage}
        disabled={sendingMessage || (ticket?.status === 'closed')}
        ticketStatus={ticket?.status || ''}
      />
      
      <div className="px-4 pb-2 text-xs text-muted-foreground text-center">
        {totalMessages} total messages
      </div>
    </>
  );

  // Render either the sheet or inline content based on the inlineMode prop
  if (inlineMode) {
    return ticket ? (
      <div className="flex flex-col h-full">
        <ChatContent />
      </div>
    ) : null;
  }

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-lg w-full p-0 flex flex-col h-full">
        {ticket && <ChatContent />}
      </SheetContent>
    </Sheet>
  );
} 