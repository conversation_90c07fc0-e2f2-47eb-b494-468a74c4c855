"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  BarChart,
  Bar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"

interface RatingsTabProps {
  analyticsData: {
    ratingsAnalytics: {
      ratingsChart: {
        category: string
        value: number
      }[]
      averageRatings: {
        accuracy: number
        communication: number
        convenience: number
        maintenance: number
        overall: number
      }
    }
  }
}

export function RatingsTab({ analyticsData }: RatingsTabProps) {
  // Format rating for display
  const formatRating = (value: number) => value.toFixed(1)
  
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Rating Categories</CardTitle>
          <CardDescription>Average ratings by category</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <Bar<PERSON>hart data={analyticsData.ratingsAnalytics.ratingsChart}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis domain={[0, 5]} />
              <Tooltip formatter={(value) => [formatRating(Number(value)), "Rating"]} />
              <Bar dataKey="value" name="Rating" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Rating Radar</CardTitle>
          <CardDescription>Ratings across all categories</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart 
              cx="50%" 
              cy="50%" 
              outerRadius="80%" 
              data={[
                { subject: 'Accuracy', value: analyticsData.ratingsAnalytics.averageRatings.accuracy },
                { subject: 'Communication', value: analyticsData.ratingsAnalytics.averageRatings.communication },
                { subject: 'Convenience', value: analyticsData.ratingsAnalytics.averageRatings.convenience },
                { subject: 'Maintenance', value: analyticsData.ratingsAnalytics.averageRatings.maintenance },
              ]}
            >
              <PolarGrid />
              <PolarAngleAxis dataKey="subject" />
              <PolarRadiusAxis domain={[0, 5]} />
              <Radar name="Rating" dataKey="value" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
              <Tooltip formatter={(value) => [formatRating(Number(value)), "Rating"]} />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Overall Rating</CardTitle>
          <CardDescription>Average rating across all categories</CardDescription>
        </CardHeader>
        <CardContent className="h-[200px] flex flex-col justify-center items-center">
          <div className="text-6xl font-bold text-primary">
            {formatRating(analyticsData.ratingsAnalytics.averageRatings.overall)}
            <span className="text-2xl text-muted-foreground">/5</span>
          </div>
          <div className="flex mt-4">
            {[1, 2, 3, 4, 5].map((star) => (
              <svg
                key={star}
                className={`w-8 h-8 ${
                  star <= Math.round(analyticsData.ratingsAnalytics.averageRatings.overall)
                    ? 'text-yellow-400'
                    : 'text-gray-300'
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
