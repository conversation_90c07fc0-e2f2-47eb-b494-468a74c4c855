"use client"

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { format } from "date-fns"
import {
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"

interface UsersTabProps {
  analyticsData: {
    userAnalytics: {
      userGrowth: {
        date: string
        count: number
      }[]
      userTypes: {
        type: string
        count: number
      }[]
      userGrowthRate: number
    }
  }
  formatDate: (dateStr: string) => string
  colors: string[]
}

export function UsersTab({ analyticsData, formatDate, colors }: UsersTabProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>User Growth</CardTitle>
          <CardDescription>New user registrations over time</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={analyticsData.userAnalytics.userGrowth}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" tickFormatter={(date) => format(new Date(date), "MMM d")} />
              <YAxis />
              <Tooltip formatter={(value) => [value, "Users"]} labelFormatter={formatDate} />
              <Area type="monotone" dataKey="count" name="Users" stroke="#8884d8" fill="#8884d8" />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>User Types</CardTitle>
          <CardDescription>Distribution of user roles</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={analyticsData.userAnalytics.userTypes}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="count"
                nameKey="type"
              >
                {analyticsData.userAnalytics.userTypes.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => [value, "Users"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>User Growth Rate</CardTitle>
          <CardDescription>Last 3 months vs previous 3 months</CardDescription>
        </CardHeader>
        <CardContent className="h-[200px] flex flex-col justify-center items-center">
          <div className={`text-5xl font-bold ${analyticsData.userAnalytics.userGrowthRate > 0 ? 'text-green-500' : 'text-red-500'}`}>
            {analyticsData.userAnalytics.userGrowthRate > 0 ? '+' : ''}{analyticsData.userAnalytics.userGrowthRate}%
          </div>
          <p className="text-muted-foreground mt-2">Compared to previous period</p>
        </CardContent>
      </Card>
    </div>
  )
}
