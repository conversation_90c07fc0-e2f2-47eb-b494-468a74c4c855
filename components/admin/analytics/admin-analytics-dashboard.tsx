"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { OverviewCards } from "./overview-cards"
import { OverviewTab } from "./overview-tab"
import { UsersTab } from "./users-tab"
import { CarsTab } from "./cars-tab"
import { BookingsTab } from "./bookings-tab"
import { RevenueTab } from "./revenue-tab"
import { RatingsTab } from "./ratings-tab"
import { format } from "date-fns"

interface AdminAnalyticsDashboardProps {
  analyticsData: {
    overview: {
      totalUsers: number
      totalCars: number
      totalBookings: number
      totalRevenue: number
      activeUsers: number
      pendingApprovals: number
      userGrowthRate: number
      bookingGrowthRate: number
      revenueGrowthRate: number
      carGrowthRate: number
      revenueByCurrency?: Record<string, number>
      averageRatings: {
        accuracy: number
        communication: number
        convenience: number
        maintenance: number
        overall: number
      }
    }
    userAnalytics: {
      userGrowth: {
        date: string
        count: number
      }[]
      userTypes: {
        type: string
        count: number
      }[]
      userGrowthRate: number
    }
    carAnalytics: {
      carStats: {
        date: string
        count: number
        active: number
        pending: number
      }[]
      carStatusTypes: {
        type: string
        count: number
      }[]
      carGrowthRate: number
      popularCarBrands: {
        brand: string
        count: number
      }[]
    }
    bookingAnalytics: {
      bookingStats: {
        date: string
        count: number
        revenue: number
      }[]
      bookingStatusTypes: {
        type: string
        count: number
      }[]
      bookingGrowthRate: number
    }
    revenueAnalytics: {
      revenueByMonth: {
        month: string
        revenue: number
      }[]
      revenueByCurrency: Record<string, number>
      revenueGrowthRate: number
    }
    locationAnalytics: {
      topLocations: {
        location: string
        count: number
      }[]
    }
    popularCars: {
      make: string
      model: string
      bookings: number
    }[]
    ratingsAnalytics: {
      ratingsChart: {
        category: string
        value: number
      }[]
      averageRatings: {
        accuracy: number
        communication: number
        convenience: number
        maintenance: number
        overall: number
      }
    }
  }
}

export function AdminAnalyticsDashboard({ analyticsData }: AdminAnalyticsDashboardProps) {
  const [activeTab, setActiveTab] = useState("overview")

  // Chart colors
  const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#0088fe", "#00C49F", "#FFBB28", "#FF8042"]

  // Format currency
  const formatCurrency = (value: number, currencyCode: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
      maximumFractionDigits: 0,
    }).format(value)
  }

  // Format date
  const formatDate = (dateStr: string) => {
    return format(new Date(dateStr), "MMMM d, yyyy")
  }

  return (
    <div className="space-y-6">
      <OverviewCards analyticsData={analyticsData} formatCurrency={formatCurrency} />

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-6 h-auto">
          <TabsTrigger value="overview" className="py-2">Overview</TabsTrigger>
          <TabsTrigger value="users" className="py-2">Users</TabsTrigger>
          <TabsTrigger value="cars" className="py-2">Cars</TabsTrigger>
          <TabsTrigger value="bookings" className="py-2">Bookings</TabsTrigger>
          <TabsTrigger value="revenue" className="py-2">Revenue</TabsTrigger>
          <TabsTrigger value="ratings" className="py-2">Ratings</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <OverviewTab 
            analyticsData={analyticsData} 
            formatDate={formatDate} 
            colors={COLORS} 
          />
        </TabsContent>
        
        <TabsContent value="users" className="space-y-4">
          <UsersTab 
            analyticsData={analyticsData} 
            formatDate={formatDate} 
            colors={COLORS} 
          />
        </TabsContent>
        
        <TabsContent value="cars" className="space-y-4">
          <CarsTab 
            analyticsData={analyticsData} 
            formatDate={formatDate} 
            colors={COLORS} 
          />
        </TabsContent>
        
        <TabsContent value="bookings" className="space-y-4">
          <BookingsTab 
            analyticsData={analyticsData} 
            formatDate={formatDate} 
            formatCurrency={formatCurrency}
            colors={COLORS} 
          />
        </TabsContent>
        
        <TabsContent value="revenue" className="space-y-4">
          <RevenueTab 
            analyticsData={analyticsData} 
            formatCurrency={formatCurrency}
            colors={COLORS} 
          />
        </TabsContent>
        
        <TabsContent value="ratings" className="space-y-4">
          <RatingsTab 
            analyticsData={analyticsData} 
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
