"use client"

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { format } from "date-fns"
import {
  LineChart,
  Line,
  <PERSON>Chart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"

interface CarsTabProps {
  analyticsData: {
    carAnalytics: {
      carStats: {
        date: string
        count: number
        active: number
        pending: number
      }[]
      carStatusTypes: {
        type: string
        count: number
      }[]
      carGrowthRate: number
      popularCarBrands: {
        brand: string
        count: number
      }[]
    }
    locationAnalytics: {
      topLocations: {
        location: string
        count: number
      }[]
    }
    popularCars: {
      make: string
      model: string
      bookings: number
    }[]
  }
  formatDate: (dateStr: string) => string
  colors: string[]
}

export function CarsTab({ analyticsData, formatDate, colors }: CarsTabProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Car Listings</CardTitle>
          <CardDescription>Car listings over time</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={analyticsData.carAnalytics.carStats}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" tickFormatter={(date) => format(new Date(date), "MMM d")} />
              <YAxis />
              <Tooltip formatter={(value) => [value, "Cars"]} labelFormatter={formatDate} />
              <Legend />
              <Line type="monotone" dataKey="count" name="Total" stroke="#8884d8" />
              <Line type="monotone" dataKey="active" name="Active" stroke="#82ca9d" />
              <Line type="monotone" dataKey="pending" name="Pending" stroke="#ffc658" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Car Status Distribution</CardTitle>
          <CardDescription>Distribution of car listing statuses</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={analyticsData.carAnalytics.carStatusTypes}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="count"
                nameKey="type"
              >
                {analyticsData.carAnalytics.carStatusTypes.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => [value, "Cars"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Top Locations</CardTitle>
          <CardDescription>Most popular car locations</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={analyticsData.locationAnalytics.topLocations}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="location" />
              <YAxis />
              <Tooltip formatter={(value) => [value, "Cars"]} />
              <Bar dataKey="count" name="Cars" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Popular Car Brands</CardTitle>
          <CardDescription>Most listed car brands</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={analyticsData.carAnalytics.popularCarBrands} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="brand" type="category" />
              <Tooltip formatter={(value) => [value, "Cars"]} />
              <Bar dataKey="count" name="Cars" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Popular Car Models</CardTitle>
          <CardDescription>Most booked car models</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={analyticsData.popularCars} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis
                dataKey="make"
                type="category"
                tick={{ width: 200 }}
                tickFormatter={(value, index) => {
                  const car = analyticsData.popularCars[index]
                  return `${car.make} ${car.model}`
                }}
              />
              <Tooltip formatter={(value) => [value, "Bookings"]} />
              <Bar dataKey="bookings" name="Bookings" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  )
}
