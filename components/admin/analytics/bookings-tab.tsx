"use client"

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { format } from "date-fns"
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"

interface BookingsTabProps {
  analyticsData: {
    bookingAnalytics: {
      bookingStats: {
        date: string
        count: number
        revenue: number
      }[]
      bookingStatusTypes: {
        type: string
        count: number
      }[]
      bookingGrowthRate: number
    }
  }
  formatDate: (dateStr: string) => string
  formatCurrency: (value: number, currencyCode?: string) => string
  colors: string[]
}

export function BookingsTab({ analyticsData, formatDate, formatCurrency, colors }: BookingsTabProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Booking Trends</CardTitle>
          <CardDescription>Booking activity over time</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={analyticsData.bookingAnalytics.bookingStats}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" tickFormatter={(date) => format(new Date(date), "MMM d")} />
              <YAxis />
              <Tooltip formatter={(value) => [value, "Bookings"]} labelFormatter={formatDate} />
              <Line type="monotone" dataKey="count" name="Bookings" stroke="#82ca9d" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Booking Revenue</CardTitle>
          <CardDescription>Revenue from bookings over time</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={analyticsData.bookingAnalytics.bookingStats}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" tickFormatter={(date) => format(new Date(date), "MMM d")} />
              <YAxis tickFormatter={(value) => `$${value}`} />
              <Tooltip
                formatter={(value) => [formatCurrency(Number(value)), "Revenue"]}
                labelFormatter={formatDate}
              />
              <Area type="monotone" dataKey="revenue" name="Revenue" stroke="#8884d8" fill="#8884d8" />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Booking Status</CardTitle>
          <CardDescription>Distribution of booking statuses</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={analyticsData.bookingAnalytics.bookingStatusTypes}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="count"
                nameKey="type"
              >
                {analyticsData.bookingAnalytics.bookingStatusTypes.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => [value, "Bookings"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Booking Growth Rate</CardTitle>
          <CardDescription>Last 3 months vs previous 3 months</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px] flex flex-col justify-center items-center">
          <div className={`text-5xl font-bold ${analyticsData.bookingAnalytics.bookingGrowthRate > 0 ? 'text-green-500' : 'text-red-500'}`}>
            {analyticsData.bookingAnalytics.bookingGrowthRate > 0 ? '+' : ''}{analyticsData.bookingAnalytics.bookingGrowthRate}%
          </div>
          <p className="text-muted-foreground mt-2">Compared to previous period</p>
        </CardContent>
      </Card>
    </div>
  )
}
