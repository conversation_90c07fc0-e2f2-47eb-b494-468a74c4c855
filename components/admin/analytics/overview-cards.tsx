"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"

interface OverviewCardsProps {
  analyticsData: {
    overview: {
      totalUsers: number
      totalCars: number
      totalBookings: number
      totalRevenue: number
      activeUsers: number
      pendingApprovals: number
      userGrowthRate: number
      bookingGrowthRate: number
      revenueGrowthRate: number
      carGrowthRate: number
    }
  }
  formatCurrency: (value: number, currencyCode?: string) => string
}

export function OverviewCards({ analyticsData, formatCurrency }: OverviewCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-2xl font-bold">{analyticsData.overview.totalUsers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">{analyticsData.overview.activeUsers.toLocaleString()} active</p>
            </div>
            {analyticsData.overview.userGrowthRate !== 0 && (
              <div className={`text-sm font-medium ${analyticsData.overview.userGrowthRate > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {analyticsData.overview.userGrowthRate > 0 ? '+' : ''}{analyticsData.overview.userGrowthRate}%
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Cars</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-2xl font-bold">{analyticsData.overview.totalCars.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">{analyticsData.overview.pendingApprovals.toLocaleString()} pending approval</p>
            </div>
            {analyticsData.overview.carGrowthRate !== 0 && (
              <div className={`text-sm font-medium ${analyticsData.overview.carGrowthRate > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {analyticsData.overview.carGrowthRate > 0 ? '+' : ''}{analyticsData.overview.carGrowthRate}%
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-2xl font-bold">{analyticsData.overview.totalBookings.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">All time</p>
            </div>
            {analyticsData.overview.bookingGrowthRate !== 0 && (
              <div className={`text-sm font-medium ${analyticsData.overview.bookingGrowthRate > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {analyticsData.overview.bookingGrowthRate > 0 ? '+' : ''}{analyticsData.overview.bookingGrowthRate}%
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-2xl font-bold">{formatCurrency(analyticsData.overview.totalRevenue)}</div>
              <p className="text-xs text-muted-foreground">Platform earnings</p>
            </div>
            {analyticsData.overview.revenueGrowthRate !== 0 && (
              <div className={`text-sm font-medium ${analyticsData.overview.revenueGrowthRate > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {analyticsData.overview.revenueGrowthRate > 0 ? '+' : ''}{analyticsData.overview.revenueGrowthRate}%
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
