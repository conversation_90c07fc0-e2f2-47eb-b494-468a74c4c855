"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"

interface RevenueTabProps {
  analyticsData: {
    overview: {
      totalRevenue: number
      revenueByCurrency?: Record<string, number>
    }
    revenueAnalytics: {
      revenueByMonth: {
        month: string
        revenue: number
      }[]
      revenueByCurrency: Record<string, number>
      revenueGrowthRate: number
    }
  }
  formatCurrency: (value: number, currencyCode?: string) => string
  colors: string[]
}

export function RevenueTab({ analyticsData, formatCurrency, colors }: RevenueTabProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Monthly Revenue</CardTitle>
          <CardDescription>Platform revenue by month</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={analyticsData.revenueAnalytics.revenueByMonth}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => `$${value}`} />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Revenue"]} />
              <Bar dataKey="revenue" name="Revenue" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Revenue Breakdown</CardTitle>
          <CardDescription>Revenue by currency</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={
                  Object.entries(analyticsData.revenueAnalytics.revenueByCurrency || {}).map(([currency, amount]) => ({
                    name: currency,
                    value: amount,
                  }))
                }
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
              >
                {Object.keys(analyticsData.revenueAnalytics.revenueByCurrency || {}).map((_, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [formatCurrency(Number(value), name as string), name]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Revenue Growth</CardTitle>
          <CardDescription>Last 3 months vs previous 3 months</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex flex-col justify-center items-center">
          <div className={`text-5xl font-bold ${analyticsData.revenueAnalytics.revenueGrowthRate > 0 ? 'text-green-500' : 'text-red-500'}`}>
            {analyticsData.revenueAnalytics.revenueGrowthRate > 0 ? '+' : ''}{analyticsData.revenueAnalytics.revenueGrowthRate}%
          </div>
          <p className="text-muted-foreground mt-2">Compared to previous period</p>
        </CardContent>
      </Card>
    </div>
  )
}
