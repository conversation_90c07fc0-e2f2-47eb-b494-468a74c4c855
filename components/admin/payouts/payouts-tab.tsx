"use client"

import { useEffect, useState, useCallback } from "react"
import { getHostPayouts, updateHostPayoutStatus } from "@/app/actions/admin/hosts"
import { Payout, PayoutStatus, UpdateHostPayoutStatusPayload } from "@/types/hosts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Search, Filter, Eye, Edit2 } from "lucide-react"
import { formatDate, formatCurrency } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import PayoutDetailsModal from "./payout-details-modal"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { BookingStatus } from "@/types/supabase"
import { useToast } from "@/hooks/use-toast"

export default function PayoutsTab() {
  const [payouts, setPayouts] = useState<Payout[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<PayoutStatus | "all">("all")
  const [selectedPayout, setSelectedPayout] = useState<Payout | null>(null)
  const [bookingStatusFilter, setBookingStatusFilter] = useState<BookingStatus | "all">("all")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [updatingPayoutId, setUpdatingPayoutId] = useState<string | null>(null)
  const itemsPerPage = 20
  const { toast } = useToast()
  const fetchPayouts = useCallback(async (pageNum: number, reset = false) => {
    try {
      setLoading(true)
      const data = await getHostPayouts({
        itemsPerPage,
        page: pageNum,
        status: statusFilter === "all" ? null : statusFilter,
        hostId: null,
        booking_status: bookingStatusFilter === "all" ? null : bookingStatusFilter,
        host_name: searchTerm
      })
      
      if (reset) {
        setPayouts(data || [])
      } else {
        setPayouts(prev => [...prev, ...(data || [])])
      }
      
      setHasMore((data?.length || 0) === itemsPerPage)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch payouts. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }, [statusFilter, bookingStatusFilter])

  useEffect(() => {
    setPage(1)
    fetchPayouts(1, true)
  }, [fetchPayouts, statusFilter])

  const loadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1
      setPage(nextPage)
      fetchPayouts(nextPage)
    }
  }

  const filteredPayouts = payouts.filter(payout => {
    const matchesSearch = searchTerm === "" || 
      `${payout.host_first_name} ${payout.host_last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payout.id.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesSearch
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-500">Completed</Badge>
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>
      case "processing":
        return <Badge className="bg-blue-500">Processing</Badge>
      case "failed":
        return <Badge className="bg-red-500">Failed</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getBookingStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-500">Completed</Badge>
      case "confirmed":
        return <Badge className="bg-blue-500">Confirmed</Badge>
      case "cancelled":
        return <Badge className="bg-red-500">Cancelled</Badge>
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const handleStatusChange = (value: string) => {
    setStatusFilter(value as PayoutStatus | "all")
  }

  const openPayoutDetails = (payout: Payout) => {
    setSelectedPayout(payout)
    setIsDialogOpen(true)
  }

  const handleBookingStatusChange = (value: string) => {
    setBookingStatusFilter(value as BookingStatus | "all")
  }

  const updatePayoutStatus = async (payoutId: string, newStatus: PayoutStatus) => {
    try {
      setUpdatingPayoutId(payoutId)
      
      const payload: UpdateHostPayoutStatusPayload = {
        payoutId,
        payoutStatus: newStatus
      }
      
      await updateHostPayoutStatus(payload)
      
      // Update the local state to reflect the change
      setPayouts(prevPayouts => 
        prevPayouts.map(payout => 
          payout.id === payoutId 
            ? { ...payout, payout_status: newStatus } 
            : payout
        )
      )
      
      toast({
        title: "Success",
        description: `Payout status updated to ${newStatus}`,
      })
    } catch (error) {
      console.error("Failed to update payout status:", error)
      toast({
        title: "Error",
        description: "Failed to update payout status. Please try again.",
        variant: "destructive"
      })
    } finally {
      setUpdatingPayoutId(null)
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Host Payouts</CardTitle>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select 
              value={statusFilter} 
              onValueChange={handleStatusChange}
            >
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search hosts or payouts..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div 
          className="border rounded-md overflow-auto" 
          style={{ height: "calc(100vh - 300px)" }}
        >
          <Table>
            <TableHeader className="sticky top-0 bg-card z-10">
              <TableRow>
                <TableHead>Host</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Payout Status</TableHead>
                <TableHead className="w-[130px]">
                  <Select
                    value={bookingStatusFilter}
                    onValueChange={handleBookingStatusChange}
                    defaultValue="all"
                  >
                    <SelectTrigger>{bookingStatusFilter === "all" ? "All" : bookingStatusFilter}</SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="confirmed">Confirmed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                      <SelectItem value="ongoing">Ongoing</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </TableHead>
                <TableHead>Notes</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPayouts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    {loading ? (
                      <div className="space-y-2">
                        {Array(5).fill(0).map((_, i) => (
                          <Skeleton key={i} className="h-5 w-full" />
                        ))}
                      </div>
                    ) : (
                      searchTerm || statusFilter !== "all" 
                        ? "No payouts found matching your filters" 
                        : "No payouts found"
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                filteredPayouts.map((payout) => (
                  <TableRow key={payout.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Avatar>
                          <AvatarImage src={payout.host_avatar} alt={`${payout.host_first_name} ${payout.host_last_name}`} />
                          <AvatarFallback>
                            {payout.host_first_name.charAt(0)}{payout.host_last_name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span>{payout.host_first_name} {payout.host_last_name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{formatCurrency(payout.net_amount)}</TableCell>
                    <TableCell>{getStatusBadge(payout.payout_status)}</TableCell>
                    <TableCell>{getBookingStatusBadge(payout.booking_status)}</TableCell>
                    <TableCell>
                      <div className="max-w-[200px] truncate" title={payout.notes}>
                        {payout.notes || "No notes"}
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(payout.payout_created_at)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => openPayoutDetails(payout)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              disabled={updatingPayoutId === payout.id}
                            >
                              <Edit2 className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Update Status</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => updatePayoutStatus(payout.id, PayoutStatus.PENDING)}
                              disabled={payout.payout_status === PayoutStatus.PENDING}
                            >
                              Set as Pending
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => updatePayoutStatus(payout.id, PayoutStatus.PROCESSING)}
                              disabled={payout.payout_status === "processing"}
                            >
                              Set as Processing
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => updatePayoutStatus(payout.id, PayoutStatus.COMPLETED)}
                              disabled={payout.payout_status === PayoutStatus.COMPLETED}
                            >
                              Set as Completed
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => updatePayoutStatus(payout.id, PayoutStatus.FAILED)}
                              disabled={payout.payout_status === PayoutStatus.FAILED}
                              className="text-red-500"
                            >
                              Set as Failed
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          
          {hasMore && !loading && filteredPayouts.length > 0 && (
            <div className="flex justify-center p-4">
              <Button 
                variant="outline" 
                onClick={loadMore}
                disabled={loading}
              >
                Load More
              </Button>
            </div>
          )}
          
          {loading && filteredPayouts.length > 0 && (
            <div className="p-4">
              <Skeleton className="h-10 w-full" />
            </div>
          )}
        </div>
      </CardContent>

      {/* Payout Details Modal */}
      <PayoutDetailsModal 
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        payout={selectedPayout}
        getStatusBadge={getStatusBadge}
        getBookingStatusBadge={getBookingStatusBadge}
      />
    </Card>
  )
} 