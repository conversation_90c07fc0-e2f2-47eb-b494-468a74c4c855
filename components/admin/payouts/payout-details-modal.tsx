import { Payout } from "@/types/hosts"
import { formatDate, formatCurrency } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface PayoutDetailsModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  payout: Payout | null
  getStatusBadge: (status: string) => React.ReactNode
  getBookingStatusBadge: (status: string) => React.ReactNode
}

export default function PayoutDetailsModal({
  isOpen,
  onOpenChange,
  payout,
  getStatusBadge,
  getBookingStatusBadge,
}: PayoutDetailsModalProps) {
  if (!payout) return null

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Payout Details</DialogTitle>
          <DialogDescription>
            Complete information about this payout
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Host Information</h3>
              <div className="flex items-center space-x-2 mt-2">
                <Avatar>
                  <AvatarImage src={payout.host_avatar} alt={`${payout.host_first_name} ${payout.host_last_name}`} />
                  <AvatarFallback>
                    {payout.host_first_name.charAt(0)}{payout.host_last_name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <span className="font-medium">{payout.host_first_name} {payout.host_last_name}</span>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Payout Information</h3>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div>
                  <p className="text-xs text-muted-foreground">Payout ID</p>
                  <p className="text-sm font-medium">{payout.id}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Transaction ID</p>
                  <p className="text-sm font-medium">{payout.transaction_id || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Status</p>
                  <p className="text-sm">{getStatusBadge(payout.payout_status)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Method</p>
                  <p className="text-sm font-medium capitalize">{payout.payout_method.replace('_', ' ')}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Created</p>
                  <p className="text-sm font-medium">{formatDate(payout.payout_created_at)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Processed</p>
                  <p className="text-sm font-medium">{payout.processed_at ? formatDate(payout.processed_at) : "Not processed"}</p>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Payment Details</h3>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div>
                  <p className="text-xs text-muted-foreground">Booking Total</p>
                  <p className="text-sm font-medium">{formatCurrency(payout.booking_total)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Amount</p>
                  <p className="text-sm font-medium">{formatCurrency(payout.amount)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Fee</p>
                  <p className="text-sm font-medium">{formatCurrency(payout.fee)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Net Amount</p>
                  <p className="text-sm font-medium font-bold">{formatCurrency(payout.net_amount)}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Booking Information</h3>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div>
                  <p className="text-xs text-muted-foreground">Booking ID</p>
                  <Link href={`/admin/bookings/${payout.booking_id}`} target="_blank" className="text-sm font-medium">
                    <span className="text-primary">View Booking details</span>
                  </Link>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Status</p>
                  <p className="text-sm">{getBookingStatusBadge(payout.booking_status)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Start Date</p>
                  <p className="text-sm font-medium">{formatDate(payout.start_date)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">End Date</p>
                  <p className="text-sm font-medium">{formatDate(payout.end_date)}</p>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Car Information</h3>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div>
                  <p className="text-xs text-muted-foreground">Car ID</p>
                  <p className="text-sm font-medium">{payout.car_id}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Car</p>
                  <p className="text-sm font-medium">{payout.car_brand} {payout.car_modal}</p>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Payment Account</h3>
              <div className="mt-2">
                <p className="text-xs text-muted-foreground">Preferred Method</p>
                <p className="text-sm font-medium capitalize">{payout.preferred_payout_method?.replace('_', ' ') || "Not set"}</p>
              </div>
              
              {payout.payment_details && (
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {payout.payment_details.bank_name && (
                    <div>
                      <p className="text-xs text-muted-foreground">Bank</p>
                      <p className="text-sm font-medium">{payout.payment_details.bank_name}</p>
                    </div>
                  )}
                  {payout.payment_details.account_number && (
                    <div>
                      <p className="text-xs text-muted-foreground">Account Number</p>
                      <p className="text-sm font-medium">••••{payout.payment_details.account_number.slice(-4)}</p>
                    </div>
                  )}
                  {payout.payment_details.account_holder_name && (
                    <div>
                      <p className="text-xs text-muted-foreground">Account Holder</p>
                      <p className="text-sm font-medium">{payout.payment_details.account_holder_name}</p>
                    </div>
                  )}
                  {payout.payment_details.phone_number && (
                    <div>
                      <p className="text-xs text-muted-foreground">Phone Number</p>
                      <p className="text-sm font-medium">{payout.payment_details.phone_number}</p>
                    </div>
                  )}
                  {payout.payment_details.mobile_provider && (
                    <div>
                      <p className="text-xs text-muted-foreground">Mobile Provider</p>
                      <p className="text-sm font-medium">{payout.payment_details.mobile_provider}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
              <p className="text-sm mt-2 p-2 bg-muted rounded-md min-h-[60px]">
                {payout.notes || "No notes available for this payout."}
              </p>
            </div>
          </div>
        </div>
        
        <DialogFooter className="mt-6 flex items-center justify-between">
          {/* <div className="flex items-center space-x-2">
            <Select value={newStatus} onValueChange={(value) => setNewStatus(value as PayoutStatus)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Update status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Button 
              onClick={handleStatusChange} 
              disabled={!newStatus || updating}
            >
              {updating ? "Updating..." : "Update Status"}
            </Button>
          </div> */}
          <Button variant="outline" onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 