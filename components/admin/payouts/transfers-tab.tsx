import { useState } from "react"
import { PaymentTransfer, PayoutStatus, UpdateTransferPayload } from "@/types/hosts"
import { formatDate, formatCurrency } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { updateTransfer } from "@/app/actions/admin/hosts"
import { toast } from "@/components/ui/use-toast"
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableRow,
// } from "@/components/ui/table"
// import {
//   Card,
//   CardContent,
// } from "@/components/ui/card"
// import {
//   AlertDialog,
//   AlertDialogContent,
//   AlertDialogDescription,
//   AlertDialogFooter,
//   AlertDialogHeader,
//   AlertDialogTitle,
//   AlertDialogCancel,
//   AlertDialogAction,
// } from "@/components/ui/alert-dialog"
// import {
//   Skeleton,
// } from "@/components/ui/skeleton"
// import {
//   Eye,
//   Trash2,
// } from "lucide-react"
// import {
//   CreateTransferModal,
// } from "@/components/admin/payouts/create-transfer-modal"

interface TransferDetailsModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  transfer: PaymentTransfer | null
  getStatusBadge: (status: string) => React.ReactNode
  onUpdate: () => void
}

export default function TransferDetailsModal({
  isOpen,
  onOpenChange,
  transfer,
  getStatusBadge,
  onUpdate
}: TransferDetailsModalProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [status, setStatus] = useState<string>("")
  const [referenceId, setReferenceId] = useState<string>("")
  const [description, setDescription] = useState<string>("")
  const [loading, setLoading] = useState(false)

  if (!transfer) return null

  const startEditing = () => {
    setStatus(transfer.status)
    setReferenceId(transfer.reference_id || "")
    setDescription(transfer.description || "")
    setIsEditing(true)
  }

  const cancelEditing = () => {
    setIsEditing(false)
  }

  const handleSave = async () => {
    try {
      setLoading(true)
      const payload: UpdateTransferPayload = {
        transferId: transfer.id,
        status: status as PayoutStatus,
        reference_id: referenceId,
        description,
        transaction_date: new Date().toISOString()
      }
      
      await updateTransfer(payload)
      toast({
        title: "Success",
        description: "Transfer updated successfully",
      })
      setIsEditing(false)
      onUpdate()
    } catch (error) {
      console.error("Failed to update transfer:", error)
      toast({
        title: "Error",
        description: "Failed to update transfer. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Transfer Details</DialogTitle>
          <DialogDescription>
            Complete information about this payment transfer
          </DialogDescription>
        </DialogHeader>
        
        {isEditing ? (
          <div className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={status} onValueChange={setStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="reference">Reference ID</Label>
                <Input 
                  id="reference" 
                  value={referenceId} 
                  onChange={(e) => setReferenceId(e.target.value)}
                  placeholder="Transaction reference ID"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea 
                id="description" 
                value={description} 
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Add notes or description about this transfer"
                rows={3}
              />
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={cancelEditing}>Cancel</Button>
              <Button onClick={handleSave} disabled={loading}>
                {loading ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Host Information</h3>
                  <div className="flex items-center space-x-2 mt-2">
                    <Avatar>
                      <AvatarImage src={transfer.host_avatar} alt={transfer.host_first_name} />
                      <AvatarFallback>
                        {transfer.host_first_name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{transfer.host_first_name} {transfer.host_last_name}</span>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Transfer Information</h3>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div>
                      <p className="text-xs text-muted-foreground">Transfer ID</p>
                      <p className="text-sm font-medium">{transfer.id}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Status</p>
                      <p className="text-sm">{getStatusBadge(transfer.status)}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Created</p>
                      <p className="text-sm font-medium">{formatDate(transfer.created_at)}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Updated</p>
                      <p className="text-sm font-medium">{formatDate(transfer.updated_at)}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Payment Details</h3>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div>
                      <p className="text-xs text-muted-foreground">Amount</p>
                      <p className="text-sm font-medium">{formatCurrency(transfer.amount, transfer.currency)}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Currency</p>
                      <p className="text-sm font-medium">{transfer.currency}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Reference ID</p>
                      <p className="text-sm font-medium">{transfer.reference_id || "N/A"}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Payout Method</p>
                      <p className="text-sm font-medium capitalize">{transfer.payout_setting_id?.replace('_', ' ') || "N/A"}</p>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                  <p className="text-sm mt-2 p-2 bg-muted rounded-md min-h-[60px]">
                    {transfer.description || "No description available for this transfer."}
                  </p>
                </div>
              </div>
            </div>
            
            <DialogFooter className="mt-6">
              <Button variant="outline" onClick={() => onOpenChange(false)}>Close</Button>
              <Button onClick={startEditing}>Edit Transfer</Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
} 