import { useState, useEffect } from "react"
import { CreateTransferPayload } from "@/types/hosts"
import { getHosts, getHostPaymentAccounts } from "@/app/actions/admin/hosts"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  <PERSON>alogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Host, HostPaymentAccount } from "@/types/hosts"

interface CreateTransferModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: CreateTransferPayload) => void
}

export default function CreateTransferModal({
  isOpen,
  onOpenChange,
  onSubmit
}: CreateTransferModalProps) {
  const [hosts, setHosts] = useState<Host[]>([])
  const [paymentAccounts, setPaymentAccounts] = useState<HostPaymentAccount[]>([])
  const [selectedHost, setSelectedHost] = useState<string>("")
  const [selectedPayoutId, setSelectedPayoutId] = useState<string>("")
  const [amount, setAmount] = useState<string>("")
  const [currency, setCurrency] = useState<string>("UGX")
  const [description, setDescription] = useState<string>("")
  const [referenceId, setReferenceId] = useState<string>("")
  const [loading, setLoading] = useState(false)
  const [fetchingHosts, setFetchingHosts] = useState(false)
  const [fetchingAccounts, setFetchingAccounts] = useState(false)

  useEffect(() => {
    if (isOpen) {
      fetchHosts()
    }
  }, [isOpen])

  useEffect(() => {
    if (selectedHost) {
      fetchPaymentAccounts(selectedHost)
    } else {
      setPaymentAccounts([])
      setSelectedPayoutId("")
    }
  }, [selectedHost])

  const fetchHosts = async () => {
    try {
      setFetchingHosts(true)
      const data = await getHosts()
      setHosts(data || [])
    } catch (error) {
      console.error("Failed to fetch hosts:", error)
    } finally {
      setFetchingHosts(false)
    }
  }

  const fetchPaymentAccounts = async (hostId: string) => {
    try {
      setFetchingAccounts(true)
      const data = await getHostPaymentAccounts({ hostId })
      setPaymentAccounts(data || [])
    } catch (error) {
      console.error("Failed to fetch payment accounts:", error)
    } finally {
      setFetchingAccounts(false)
    }
  }

  const resetForm = () => {
    setSelectedHost("")
    setSelectedPayoutId("")
    setAmount("")
    setCurrency("UGX")
    setDescription("")
    setReferenceId("")
  }

  const handleClose = () => {
    resetForm()
    onOpenChange(false)
  }

  const handleSubmit = () => {
    if (!selectedHost || !selectedPayoutId || !amount) return

    const payload: CreateTransferPayload = {
      hostId: selectedHost,
      payout_id: selectedPayoutId,
      amount: parseFloat(amount),
      currency,
      description: description || null,
      reference_id: referenceId || null
    }

    setLoading(true)
    onSubmit(payload)
    setLoading(false)
    resetForm()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Transfer</DialogTitle>
          <DialogDescription>
            Create a new payment transfer to a host
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="host">Host</Label>
            <Select value={selectedHost} onValueChange={setSelectedHost} disabled={fetchingHosts}>
              <SelectTrigger>
                <SelectValue placeholder="Select a host" />
              </SelectTrigger>
              <SelectContent>
                {hosts.map(host => (
                  <SelectItem key={host.id} value={host.id}>
                    {host.first_name} {host.last_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="payoutMethod">Payment Account</Label>
            <Select 
              value={selectedPayoutId} 
              onValueChange={setSelectedPayoutId}
              disabled={!selectedHost || fetchingAccounts}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select payment account" />
              </SelectTrigger>
              <SelectContent>
                {paymentAccounts.map(account => (
                  <SelectItem key={account.id} value={account.id}>  
                    {account.bank_name 
                      ? `Bank: ${account.bank_name} (${account.account_number?.slice(-4)})` 
                      : `Mobile Money: ${account.phone_number}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <Input 
                id="amount" 
                type="number"
                value={amount} 
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select value={currency} onValueChange={setCurrency}>
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UGX">UGX</SelectItem>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="reference">Reference ID (Optional)</Label>
            <Input 
              id="reference" 
              value={referenceId} 
              onChange={(e) => setReferenceId(e.target.value)}
              placeholder="Transaction reference ID"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea 
              id="description" 
              value={description} 
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Add notes or description about this transfer"
              rows={3}
            />
          </div>
        </div>
        
        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={handleClose}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            disabled={loading || !selectedHost || !selectedPayoutId || !amount}
          >
            {loading ? "Creating..." : "Create Transfer"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 