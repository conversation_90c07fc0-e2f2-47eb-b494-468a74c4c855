import React from 'react';

type SourceItem = {
  name: string;
  value: number;
  color: string;
};

type SourceDistributionChartProps = {
  title: string;
  items: SourceItem[];
  total: number;
};

export function SourceDistributionChart({ title, items, total }: SourceDistributionChartProps) {
  // Calculate percentages and stroke dasharray values
  const calculateStrokeDashArray = (value: number) => {
    const circumference = 2 * Math.PI * 40; // 40 is the radius
    const percentage = value / total;
    return `${percentage * circumference} ${circumference}`;
  };

  // Calculate stroke dash offsets
  const calculateStrokeDashOffset = (index: number) => {
    const circumference = 2 * Math.PI * 40;
    let offset = 0;
    
    for (let i = 0; i < index; i++) {
      offset += (items[i].value / total) * circumference;
    }
    
    return offset;
  };

  return (
    <div className="p-4 bg-white dark:bg-gray-950 rounded-lg shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium">{title}</h3>
        <button className="text-gray-500 hover:text-gray-700">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
      
      <div className="relative flex justify-center mb-6">
        <div className="relative h-48 w-48">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <p className="text-3xl font-bold">{total.toLocaleString()}</p>
              <p className="text-sm text-muted-foreground">Total</p>
            </div>
          </div>
          
          <svg className="h-full w-full -rotate-90" viewBox="0 0 100 100">
            {items.map((item, index) => (
              <circle
                key={item.name}
                cx="50"
                cy="50"
                r="40"
                fill="none"
                stroke={item.color}
                strokeWidth="12"
                strokeDasharray={calculateStrokeDashArray(item.value)}
                strokeDashoffset={`-${calculateStrokeDashOffset(index)}`}
                style={{ transformOrigin: 'center' }}
              />
            ))}
          </svg>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-x-4 gap-y-2">
        {items.map((item) => (
          <div key={item.name} className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }}></div>
            <div className="flex flex-col">
              <span className="text-xs text-muted-foreground">{item.name}</span>
              <span className="text-sm font-medium">{item.value.toLocaleString()}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 