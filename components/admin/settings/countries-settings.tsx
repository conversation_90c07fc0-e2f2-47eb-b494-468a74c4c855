"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { PlusCircle } from "lucide-react"
import { createCountry, updateCountry, deleteCountry } from "@/app/actions/admin/admin-actions"
import { CountryForm, CountryFormValues } from "./country-form"
import { CountryList } from "./country-list"

interface CountriesSettingsProps {
  countries: any[]
}

export function CountriesSettings({ countries = [] }: CountriesSettingsProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedCountry, setSelectedCountry] = useState<any>(null)

  const handleAddDialogOpen = () => {
    setIsAddDialogOpen(true)
  }

  const handleEditDialogOpen = (country: any) => {
    setSelectedCountry(country)
    setIsEditDialogOpen(true)
  }

  async function handleAddSubmit(data: CountryFormValues) {
    setIsLoading(true)

    try {
      await createCountry(data)

      toast({
        title: "Country Added",
        description: "The country has been added successfully.",
      })

      setIsAddDialogOpen(false)
      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add country. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  async function handleEditSubmit(data: CountryFormValues) {
    if (!selectedCountry) return

    setIsLoading(true)

    try {
      await updateCountry(selectedCountry.id, data)

      toast({
        title: "Country Updated",
        description: "The country has been updated successfully.",
      })

      setIsEditDialogOpen(false)
      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update country. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  async function handleDeleteCountry(id: string) {
    try {
      await deleteCountry(id)

      toast({
        title: "Country Deleted",
        description: "The country has been deleted successfully.",
      })

      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete country. Please try again.",
        variant: "destructive",
      })
    }
  }

  async function handleToggleStatus(id: string, currentStatus: boolean) {
    try {
      await updateCountry(id, { is_active: !currentStatus })
      
      toast({
        title: "Status Updated",
        description: `Country is now ${!currentStatus ? 'active' : 'inactive'}.`,
      })
      
      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update status. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Supported Countries</h3>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleAddDialogOpen} size="sm">
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Country
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Country</DialogTitle>
              <DialogDescription>
                Add a new country to support on your platform.
              </DialogDescription>
            </DialogHeader>
            <CountryForm 
              defaultValues={{
                name: "",
                code: "",
                currency_code: "",
                currency_prefix: "",
                is_active: true,
                flag_image: ""
              }}
              onSubmit={handleAddSubmit}
              isLoading={isLoading}
              submitLabel="Add Country"
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Countries List</CardTitle>
        </CardHeader>
        <CardContent>
          <CountryList 
            countries={countries}
            onEdit={handleEditDialogOpen}
            onDelete={handleDeleteCountry}
            onToggleStatus={handleToggleStatus}
          />
        </CardContent>
      </Card>

      {/* Edit Country Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Country</DialogTitle>
            <DialogDescription>
              Update country information.
            </DialogDescription>
          </DialogHeader>
          {selectedCountry && (
            <CountryForm 
              defaultValues={{
                name: selectedCountry.name,
                code: selectedCountry.code,
                currency_code: selectedCountry.currency_code,
                currency_prefix: selectedCountry.currency_prefix,
                is_active: selectedCountry.is_active || false,
                flag_image: selectedCountry.flag_image || ""
              }}
              onSubmit={handleEditSubmit}
              isLoading={isLoading}
              submitLabel="Save Changes"
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
} 