"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { updateAdminSettings } from "@/app/actions/admin/admin-actions"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const paymentSettingsSchema = z.object({
  payment_gateway: z.enum(["stripe", "paypal", "flutterwave"]),
  stripe_public_key: z.string().optional(),
  stripe_secret_key: z.string().optional(),
  paypal_client_id: z.string().optional(),
  paypal_secret_key: z.string().optional(),
  flutterwave_public_key: z.string().optional(),
  flutterwave_secret_key: z.string().optional(),
  flutterwave_encryption_key: z.string().optional(),
})

type PaymentSettingsValues = z.infer<typeof paymentSettingsSchema>

interface PaymentSettingsProps {
  settings: any
}

export function PaymentSettings({ settings }: PaymentSettingsProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<PaymentSettingsValues>({
    resolver: zodResolver(paymentSettingsSchema),
    defaultValues: {
      payment_gateway: settings?.payment_gateway || "stripe",
      stripe_public_key: settings?.stripe_public_key || "",
      stripe_secret_key: settings?.stripe_secret_key || "",
      paypal_client_id: settings?.paypal_client_id || "",
      paypal_secret_key: settings?.paypal_secret_key || "",
      flutterwave_public_key: settings?.flutterwave_public_key || "",
      flutterwave_secret_key: settings?.flutterwave_secret_key || "",
      flutterwave_encryption_key: settings?.flutterwave_encryption_key || "",
    },
  })

  const watchPaymentGateway = form.watch("payment_gateway")

  async function onSubmit(data: PaymentSettingsValues) {
    setIsLoading(true)

    try {
      // Keep existing settings and update only payment settings
      const updatedSettings = {
        ...settings,
        ...data,
      }

      await updateAdminSettings(updatedSettings)

      toast({
        title: "Settings Updated",
        description: "Your payment settings have been updated successfully.",
      })

      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="payment_gateway"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Payment Gateway</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-1"
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="stripe" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Stripe
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="paypal" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      PayPal
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="flutterwave" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Flutterwave
                    </FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormDescription>
                Select the payment gateway you want to use.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {watchPaymentGateway === "stripe" && (
          <Card>
            <CardHeader>
              <CardTitle>Stripe Configuration</CardTitle>
              <CardDescription>
                Enter your Stripe API keys to enable Stripe payments.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="stripe_public_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stripe Public Key</FormLabel>
                    <FormControl>
                      <Input placeholder="pk_test_..." {...field} />
                    </FormControl>
                    <FormDescription>
                      Your Stripe publishable key.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="stripe_secret_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stripe Secret Key</FormLabel>
                    <FormControl>
                      <Input placeholder="sk_test_..." {...field} />
                    </FormControl>
                    <FormDescription>
                      Your Stripe secret key.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        )}

        {watchPaymentGateway === "paypal" && (
          <Card>
            <CardHeader>
              <CardTitle>PayPal Configuration</CardTitle>
              <CardDescription>
                Enter your PayPal API credentials to enable PayPal payments.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="paypal_client_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>PayPal Client ID</FormLabel>
                    <FormControl>
                      <Input placeholder="Client ID" {...field} />
                    </FormControl>
                    <FormDescription>
                      Your PayPal client ID.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="paypal_secret_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>PayPal Secret Key</FormLabel>
                    <FormControl>
                      <Input placeholder="Secret Key" {...field} />
                    </FormControl>
                    <FormDescription>
                      Your PayPal secret key.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        )}

        {watchPaymentGateway === "flutterwave" && (
          <Card>
            <CardHeader>
              <CardTitle>Flutterwave Configuration</CardTitle>
              <CardDescription>
                Enter your Flutterwave API credentials to enable Flutterwave payments.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="flutterwave_public_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Flutterwave Public Key</FormLabel>
                    <FormControl>
                      <Input placeholder="Public Key" {...field} />
                    </FormControl>
                    <FormDescription>
                      Your Flutterwave public key.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="flutterwave_secret_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Flutterwave Secret Key</FormLabel>
                    <FormControl>
                      <Input placeholder="Secret Key" {...field} />
                    </FormControl>
                    <FormDescription>
                      Your Flutterwave secret key.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="flutterwave_encryption_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Flutterwave Encryption Key</FormLabel>
                    <FormControl>
                      <Input placeholder="Encryption Key" {...field} />
                    </FormControl>
                    <FormDescription>
                      Your Flutterwave encryption key.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        )}

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
