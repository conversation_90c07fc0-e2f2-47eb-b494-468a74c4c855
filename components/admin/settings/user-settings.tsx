"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { toast } from "@/components/ui/use-toast"
import { updateAdminSettings } from "@/app/actions/admin/admin-actions"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const userSettingsSchema = z.object({
  allow_user_registration: z.boolean().default(true),
  default_user_role: z.enum(["admin", "car_owner", "renter"]),
  require_email_verification: z.boolean().default(true),
  maintenance_mode: z.boolean().default(false),
  maintenance_message: z.string().optional(),
  max_cars_per_owner: z.coerce.number().int().positive().optional(),
  max_bookings_per_user: z.coerce.number().int().positive().optional(),
})

type UserSettingsValues = z.infer<typeof userSettingsSchema>

interface UserSettingsProps {
  settings: any
}

export function UserSettings({ settings }: UserSettingsProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<UserSettingsValues>({
    resolver: zodResolver(userSettingsSchema),
    defaultValues: {
      allow_user_registration: settings?.allow_user_registration !== false,
      default_user_role: settings?.default_user_role || "renter",
      require_email_verification: settings?.require_email_verification !== false,
      maintenance_mode: settings?.maintenance_mode === true,
      maintenance_message: settings?.maintenance_message || "The site is currently under maintenance. Please check back later.",
      max_cars_per_owner: settings?.max_cars_per_owner || 10,
      max_bookings_per_user: settings?.max_bookings_per_user || 5,
    },
  })

  const watchMaintenanceMode = form.watch("maintenance_mode")

  async function onSubmit(data: UserSettingsValues) {
    setIsLoading(true)

    try {
      // Keep existing settings and update only user settings
      const updatedSettings = {
        ...settings,
        ...data,
      }

      await updateAdminSettings(updatedSettings)

      toast({
        title: "Settings Updated",
        description: "Your user settings have been updated successfully.",
      })

      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="allow_user_registration"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">User Registration</FormLabel>
                  <FormDescription>
                    Allow new users to register on the platform.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="require_email_verification"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Email Verification</FormLabel>
                  <FormDescription>
                    Require email verification for new users.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="default_user_role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Default User Role</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="renter">Renter</SelectItem>
                    <SelectItem value="car_owner">Car Owner</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  The default role assigned to new users.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="max_cars_per_owner"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Max Cars Per Owner</FormLabel>
                <FormControl>
                  <Input type="number" min="1" {...field} />
                </FormControl>
                <FormDescription>
                  Maximum number of cars a single owner can list.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="max_bookings_per_user"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Max Bookings Per User</FormLabel>
                <FormControl>
                  <Input type="number" min="1" {...field} />
                </FormControl>
                <FormDescription>
                  Maximum number of active bookings a user can have.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="maintenance_mode"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 md:col-span-2">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Maintenance Mode</FormLabel>
                  <FormDescription>
                    Put the site in maintenance mode. Only admins will be able to access the site.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {watchMaintenanceMode && (
          <FormField
            control={form.control}
            name="maintenance_message"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Maintenance Message</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter maintenance message..."
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Message to display to users during maintenance mode.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
