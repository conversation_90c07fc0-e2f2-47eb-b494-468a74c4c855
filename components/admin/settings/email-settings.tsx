"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { updateAdminSettings } from "@/app/actions/admin/admin-actions"
import { Switch } from "@/components/ui/switch"

const emailSettingsSchema = z.object({
  smtp_host: z.string().optional(),
  smtp_port: z.coerce.number().int().positive().optional(),
  smtp_user: z.string().optional(),
  smtp_password: z.string().optional(),
  smtp_from_email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  smtp_from_name: z.string().min(2, {
    message: "From name must be at least 2 characters.",
  }),
  enable_email_notifications: z.boolean().default(true),
})

type EmailSettingsValues = z.infer<typeof emailSettingsSchema>

interface EmailSettingsProps {
  settings: any
}

export function EmailSettings({ settings }: EmailSettingsProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [testEmailSent, setTestEmailSent] = useState(false)

  const form = useForm<EmailSettingsValues>({
    resolver: zodResolver(emailSettingsSchema),
    defaultValues: {
      smtp_host: settings?.smtp_host || "",
      smtp_port: settings?.smtp_port || 587,
      smtp_user: settings?.smtp_user || "",
      smtp_password: settings?.smtp_password || "",
      smtp_from_email: settings?.smtp_from_email || "",
      smtp_from_name: settings?.smtp_from_name || "",
      enable_email_notifications: settings?.enable_email_notifications !== false,
    },
  })

  async function onSubmit(data: EmailSettingsValues) {
    setIsLoading(true)

    try {
      // Keep existing settings and update only email settings
      const updatedSettings = {
        ...settings,
        ...data,
      }

      await updateAdminSettings(updatedSettings)

      toast({
        title: "Settings Updated",
        description: "Your email settings have been updated successfully.",
      })

      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const sendTestEmail = async () => {
    setTestEmailSent(true)
    
    // In a real implementation, this would send a test email
    setTimeout(() => {
      toast({
        title: "Test Email Sent",
        description: "A test email has been sent to the configured address.",
      })
      setTestEmailSent(false)
    }, 2000)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="enable_email_notifications"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Email Notifications</FormLabel>
                <FormDescription>
                  Enable email notifications for users.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="grid gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="smtp_host"
            render={({ field }) => (
              <FormItem>
                <FormLabel>SMTP Host</FormLabel>
                <FormControl>
                  <Input placeholder="smtp.example.com" {...field} />
                </FormControl>
                <FormDescription>
                  The hostname of your SMTP server.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="smtp_port"
            render={({ field }) => (
              <FormItem>
                <FormLabel>SMTP Port</FormLabel>
                <FormControl>
                  <Input type="number" placeholder="587" {...field} />
                </FormControl>
                <FormDescription>
                  The port of your SMTP server.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="smtp_user"
            render={({ field }) => (
              <FormItem>
                <FormLabel>SMTP Username</FormLabel>
                <FormControl>
                  <Input placeholder="username" {...field} />
                </FormControl>
                <FormDescription>
                  The username for SMTP authentication.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="smtp_password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>SMTP Password</FormLabel>
                <FormControl>
                  <Input type="password" placeholder="••••••••" {...field} />
                </FormControl>
                <FormDescription>
                  The password for SMTP authentication.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="smtp_from_email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>From Email</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormDescription>
                  The email address that will appear in the From field.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="smtp_from_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>From Name</FormLabel>
                <FormControl>
                  <Input placeholder="Your Company Name" {...field} />
                </FormControl>
                <FormDescription>
                  The name that will appear in the From field.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-between">
          <Button 
            type="button" 
            variant="outline" 
            onClick={sendTestEmail}
            disabled={isLoading || testEmailSent}
          >
            {testEmailSent ? "Sending..." : "Send Test Email"}
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
