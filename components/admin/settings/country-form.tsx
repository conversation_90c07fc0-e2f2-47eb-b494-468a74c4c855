"use client"

import { useState, useEffect, useRef } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import Image from "next/image"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Icons } from "@/components/ui/icons"
import { createClient } from "@/lib/supabase-browser"
import { v4 as uuidv4 } from "uuid"
import { toast } from "@/components/ui/use-toast"

export const countrySchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  code: z.string().min(2).max(2, {
    message: "Country code must be exactly 2 characters.",
  }),
  currency_code: z.string().min(3).max(3, {
    message: "Currency code must be exactly 3 characters.",
  }),
  currency_prefix: z.string().min(1, {
    message: "Currency prefix is required.",
  }),
  is_active: z.boolean().default(true),
  flag_image: z.string().optional(),
})

export type CountryFormValues = z.infer<typeof countrySchema>

interface CountryFormProps {
  defaultValues: CountryFormValues
  onSubmit: (values: CountryFormValues) => Promise<void>
  isLoading: boolean
  submitLabel: string
}

export function CountryForm({ defaultValues, onSubmit, isLoading, submitLabel }: CountryFormProps) {
  const [imageUploading, setImageUploading] = useState(false)
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const supabase = createClient()
  
  const form = useForm<CountryFormValues>({
    resolver: zodResolver(countrySchema),
    defaultValues,
  })

  // Update local image state when default values change (for edit mode)
  useEffect(() => {
    if (defaultValues.flag_image) {
      setSelectedImage(defaultValues.flag_image)
    }
  }, [defaultValues.flag_image])

  // Trigger file input click
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  async function handleImageUpload(event: React.ChangeEvent<HTMLInputElement>) {
    const files = event.target.files
    if (!files || files.length === 0) return

    const file = files[0]
    setImageUploading(true)
    
    try {
      console.log("File selected:", file.name, "Size:", file.size, "Type:", file.type)
      
      const fileExt = file.name.split('.').pop()
      const fileName = `${uuidv4()}.${fileExt}`
      const filePath = `flags/${fileName}`

      // Validate file size (limit to 2MB)
      if (file.size > 2 * 1024 * 1024) {
        throw new Error('Image size exceeds 2MB. Please select a smaller image.')
      }
      
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Unsupported file type. Please upload JPG, PNG, GIF, WEBP, or SVG.')
      }

      console.log("Uploading file to path:", filePath)

      // Upload the file to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('countries')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        })

      console.log("Upload response:", uploadData, uploadError)

      if (uploadError) {
        if (uploadError.message.includes('permission') || uploadError.message.includes('policy')) {
          throw new Error('You do not have permission to upload flag images. Only admins can upload flags.')
        }
        throw uploadError
      }

      // Get the public URL for the file
      const { data } = supabase.storage
        .from('countries')
        .getPublicUrl(filePath)

      console.log("Public URL:", data)

      if (!data || !data.publicUrl) {
        throw new Error('Failed to get public URL for uploaded image')
      }

      // Set the image URL in the form
      form.setValue('flag_image', data.publicUrl)
      setSelectedImage(data.publicUrl)
      
      // Show success toast
      toast({
        title: "Image Uploaded",
        description: "Flag image has been successfully uploaded.",
      })
    } catch (error) {
      console.error('Error uploading flag image:', error)
      // Show error toast with the error message
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Failed to upload flag image. Please try again.",
        variant: "destructive",
      })
    } finally {
      setImageUploading(false)
      // Reset the file input to allow uploading the same file again
      if (event.target) {
        event.target.value = ''
      }
    }
  }

  // Function to remove the current image
  const handleRemoveImage = () => {
    setSelectedImage(null)
    form.setValue('flag_image', undefined)
    
    toast({
      title: "Image Removed",
      description: "Flag image has been removed.",
    })
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-4">
          <div className="flex flex-col items-center justify-center space-y-4 mb-6">
            <div 
              className="relative w-32 h-24 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center overflow-hidden cursor-pointer"
              onClick={triggerFileInput}
            >
              {selectedImage ? (
                <>
                  <Image
                    src={selectedImage}
                    alt="Flag preview"
                    fill
                    style={{ objectFit: 'cover' }}
                    className="rounded-md"
                  />
                  <div 
                    className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleRemoveImage()
                    }}
                  >
                    <span className="text-white text-xs font-medium">Remove</span>
                  </div>
                </>
              ) : (
                <div className="text-center p-2 text-sm text-gray-500">
                  <Icons.image className="mx-auto h-8 w-8 text-gray-400" />
                  <span>Click to upload</span>
                </div>
              )}
            </div>

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleImageUpload}
              disabled={imageUploading}
            />

            <div className="flex space-x-2 w-full justify-center">
              <Button 
                type="button" 
                variant="secondary" 
                className="px-4 py-2 text-sm"
                onClick={triggerFileInput}
                disabled={imageUploading}
              >
                {imageUploading ? (
                  <>
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" /> 
                    Uploading...
                  </>
                ) : (
                  <>
                    <Icons.image className="mr-2 h-4 w-4" />
                    {selectedImage ? 'Change Image' : 'Upload Flag'}
                  </>
                )}
              </Button>
              
              {selectedImage && (
                <Button 
                  type="button" 
                  variant="outline" 
                  className="px-4 py-2 text-sm"
                  onClick={handleRemoveImage}
                >
                  Remove
                </Button>
              )}
            </div>
            
            <FormField
              control={form.control}
              name="flag_image"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormDescription className="text-center">
                    Upload a flag image for this country (max 2MB)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Country Name</FormLabel>
                <FormControl>
                  <Input placeholder="Uganda" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Country Code</FormLabel>
                <FormControl>
                  <Input placeholder="UG" maxLength={2} {...field} />
                </FormControl>
                <FormDescription>
                  Two-letter ISO country code (e.g., US, GB, CA)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="currency_code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Currency Code</FormLabel>
                <FormControl>
                  <Input placeholder="UGX" maxLength={3} {...field} />
                </FormControl>
                <FormDescription>
                  Three-letter ISO currency code (e.g., USD, EUR, GBP)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="currency_prefix"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Currency Prefix</FormLabel>
                <FormControl>
                  <Input placeholder="UGX" {...field} />
                </FormControl>
                <FormDescription>
                  Symbol used before currency amounts (e.g., $, €, £)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="is_active"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <FormLabel>Active</FormLabel>
                  <FormDescription>
                    Whether this country is available for selection.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className="flex justify-end space-x-2">
          <Button type="submit" disabled={isLoading || imageUploading}>
            {isLoading ? 
              <><Icons.spinner className="mr-2 h-4 w-4 animate-spin" /> {submitLabel}</> : 
              submitLabel
            }
          </Button>
        </div>
      </form>
    </Form>
  )
} 