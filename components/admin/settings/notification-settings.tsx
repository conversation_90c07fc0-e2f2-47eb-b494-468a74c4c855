"use client"

import { useState } from "react"
import { use<PERSON>outer } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { toast } from "@/components/ui/use-toast"
import { updateAdminSettings } from "@/app/actions/admin/admin-actions"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const notificationSettingsSchema = z.object({
  enable_email_notifications: z.boolean().default(true),
  enable_sms_notifications: z.boolean().default(false),
  enable_push_notifications: z.boolean().default(false),
  notify_admin_on_new_booking: z.boolean().default(true),
  notify_admin_on_new_user: z.boolean().default(true),
  notify_admin_on_new_car: z.boolean().default(true),
  notify_owner_on_new_booking: z.boolean().default(true),
  notify_renter_on_booking_status: z.boolean().default(true),
  booking_confirmation_template: z.string().optional(),
  booking_cancellation_template: z.string().optional(),
  welcome_email_template: z.string().optional(),
})

type NotificationSettingsValues = z.infer<typeof notificationSettingsSchema>

interface NotificationSettingsProps {
  settings: any
}

export function NotificationSettings({ settings }: NotificationSettingsProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<NotificationSettingsValues>({
    resolver: zodResolver(notificationSettingsSchema),
    defaultValues: {
      enable_email_notifications: settings?.enable_email_notifications !== false,
      enable_sms_notifications: settings?.enable_sms_notifications === true,
      enable_push_notifications: settings?.enable_push_notifications === true,
      notify_admin_on_new_booking: settings?.notify_admin_on_new_booking !== false,
      notify_admin_on_new_user: settings?.notify_admin_on_new_user !== false,
      notify_admin_on_new_car: settings?.notify_admin_on_new_car !== false,
      notify_owner_on_new_booking: settings?.notify_owner_on_new_booking !== false,
      notify_renter_on_booking_status: settings?.notify_renter_on_booking_status !== false,
      booking_confirmation_template: settings?.booking_confirmation_template || "Dear {{renter_name}},\n\nYour booking for {{car_name}} has been confirmed. Your booking ID is {{booking_id}}.\n\nPickup: {{pickup_date}} at {{pickup_time}}\nReturn: {{return_date}} at {{return_time}}\n\nThank you for choosing our service.\n\nBest regards,\nThe Team",
      booking_cancellation_template: settings?.booking_cancellation_template || "Dear {{renter_name}},\n\nYour booking for {{car_name}} has been cancelled. Your booking ID was {{booking_id}}.\n\nReason: {{cancellation_reason}}\n\nIf you have any questions, please contact our support team.\n\nBest regards,\nThe Team",
      welcome_email_template: settings?.welcome_email_template || "Dear {{user_name}},\n\nWelcome to our platform! We're excited to have you on board.\n\nYou can now browse and book cars for your next trip.\n\nIf you have any questions, please don't hesitate to contact our support team.\n\nBest regards,\nThe Team",
    },
  })

  async function onSubmit(data: NotificationSettingsValues) {
    setIsLoading(true)

    try {
      // Keep existing settings and update only notification settings
      const updatedSettings = {
        ...settings,
        ...data,
      }

      await updateAdminSettings(updatedSettings)

      toast({
        title: "Settings Updated",
        description: "Your notification settings have been updated successfully.",
      })

      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Notification Channels</h3>
          <div className="grid gap-4 md:grid-cols-3">
            <FormField
              control={form.control}
              name="enable_email_notifications"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Email Notifications</FormLabel>
                    <FormDescription>
                      Send notifications via email.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="enable_sms_notifications"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">SMS Notifications</FormLabel>
                    <FormDescription>
                      Send notifications via SMS.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="enable_push_notifications"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Push Notifications</FormLabel>
                    <FormDescription>
                      Send push notifications to devices.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Notification Events</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="notify_admin_on_new_booking"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">New Booking Notifications</FormLabel>
                    <FormDescription>
                      Notify admins when a new booking is created.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notify_admin_on_new_user"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">New User Notifications</FormLabel>
                    <FormDescription>
                      Notify admins when a new user registers.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notify_admin_on_new_car"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">New Car Notifications</FormLabel>
                    <FormDescription>
                      Notify admins when a new car is listed.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notify_owner_on_new_booking"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Owner Booking Notifications</FormLabel>
                    <FormDescription>
                      Notify car owners when their car is booked.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notify_renter_on_booking_status"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Booking Status Notifications</FormLabel>
                    <FormDescription>
                      Notify renters when their booking status changes.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Email Templates</h3>
          <Card>
            <CardContent className="pt-6">
              <Tabs defaultValue="booking_confirmation">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="booking_confirmation">Booking Confirmation</TabsTrigger>
                  <TabsTrigger value="booking_cancellation">Booking Cancellation</TabsTrigger>
                  <TabsTrigger value="welcome_email">Welcome Email</TabsTrigger>
                </TabsList>
                <TabsContent value="booking_confirmation" className="mt-4">
                  <FormField
                    control={form.control}
                    name="booking_confirmation_template"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Booking Confirmation Template</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter template content..."
                            className="min-h-[200px] font-mono text-sm"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Use placeholders like renter's name, car's name, booking id, etc.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
                <TabsContent value="booking_cancellation" className="mt-4">
                  <FormField
                    control={form.control}
                    name="booking_cancellation_template"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Booking Cancellation Template</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter template content..."
                            className="min-h-[200px] font-mono text-sm"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Use placeholders like renter's name, car's name, booking id, cancellation reason, etc.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
                <TabsContent value="welcome_email" className="mt-4">
                  <FormField
                    control={form.control}
                    name="welcome_email_template"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Welcome Email Template</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter template content..."
                            className="min-h-[200px] font-mono text-sm"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Use placeholders like user's name, email, etc.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
