"use client"

import { useState } from "react"
import Image from "next/image"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Pencil, Trash2, Flag, Eye } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface Country {
  id: string
  name: string
  code: string
  currency_code: string
  currency_prefix: string
  is_active: boolean
  flag_image?: string
}

interface CountryListProps {
  countries: Country[]
  onEdit: (country: Country) => void
  onDelete: (id: string) => Promise<void>
  onToggleStatus: (id: string, currentStatus: boolean) => Promise<void>
}

export function CountryList({ countries, onEdit, onDelete, onToggleStatus }: CountryListProps) {
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)

  const handlePreviewImage = (url: string) => {
    setPreviewImage(url)
    setIsPreviewOpen(true)
  }

  return (
    <>
      {countries.length === 0 ? (
        <div className="text-center p-4">
          <p className="text-muted-foreground">No countries added yet.</p>
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Flag</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Code</TableHead>
              <TableHead>Currency</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {countries.map((country) => (
              <TableRow key={country.id}>
                <TableCell>
                  {country.flag_image ? (
                    <div className="relative w-10 h-6 cursor-pointer" onClick={() => handlePreviewImage(country.flag_image!)}>
                      <Image
                        src={country.flag_image}
                        alt={`${country.name} flag`}
                        fill
                        style={{ objectFit: 'cover' }}
                        className="rounded-sm"
                      />
                    </div>
                  ) : (
                    <Flag className="h-4 w-4 text-muted-foreground" />
                  )}
                </TableCell>
                <TableCell className="font-medium">{country.name}</TableCell>
                <TableCell>{country.code}</TableCell>
                <TableCell>{country.currency_code} ({country.currency_prefix})</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={country.is_active || false}
                      onCheckedChange={() => onToggleStatus(country.id, country.is_active || false)}
                    />
                    <Badge variant={country.is_active ? "default" : "outline"}>{country.is_active ? "Active" : "Inactive"}</Badge>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    {country.flag_image && (
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handlePreviewImage(country.flag_image!)}
                        title="View Flag"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => onEdit(country)}
                      title="Edit Country"
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="icon"
                          className="text-destructive"
                          title="Delete Country"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Country</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete {country.name}? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => onDelete(country.id)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {/* Image Preview Dialog */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Flag Preview</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center p-4">
            {previewImage && (
              <div className="relative w-full h-48">
                <Image
                  src={previewImage}
                  alt="Flag preview"
                  fill
                  style={{ objectFit: 'contain' }}
                  className="rounded-md"
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
} 