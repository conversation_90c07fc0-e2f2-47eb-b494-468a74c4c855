"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Check, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { approveCarListing, rejectCarListing } from "@/app/actions/admin/admin-actions"

interface CarApprovalActionsProps {
  carId: string
}

export function CarApprovalActions({ carId }: CarApprovalActionsProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isApproveOpen, setIsApproveOpen] = useState(false)
  const [isRejectOpen, setIsRejectOpen] = useState(false)
  const [rejectionReason, setRejectionReason] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  const handleApproveCar = async () => {
    setIsProcessing(true)

    try {
      await approveCarListing(carId)

      toast({
        title: "Car approved",
        description: "The car listing has been approved and is now active",
      })

      setIsApproveOpen(false)
      router.push("/admin/cars")
    } catch (error) {
      console.error('Error approving car listing:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to approve car listing",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRejectCar = async () => {
    if (!rejectionReason.trim()) return

    setIsProcessing(true)

    try {
      await rejectCarListing(carId, rejectionReason)

      toast({
        title: "Car rejected",
        description: "The car listing has been rejected",
      })

      setIsRejectOpen(false)
      router.push("/admin/cars")
    } catch (error) {
      console.error('Error rejecting car listing:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to reject car listing",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="flex gap-2">
      <Button variant="outline" onClick={() => router.push("/admin/cars")}>
        Back to Cars
      </Button>
      <Button 
        variant="destructive" 
        onClick={() => setIsRejectOpen(true)}
      >
        <X className="mr-2 h-4 w-4" />
        Reject
      </Button>
      <Button 
        onClick={() => setIsApproveOpen(true)}
      >
        <Check className="mr-2 h-4 w-4" />
        Approve
      </Button>

      {/* Approve Car Dialog */}
      <Dialog open={isApproveOpen} onOpenChange={setIsApproveOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Car Listing</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve this car listing?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApproveOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApproveCar} disabled={isProcessing}>
              {isProcessing ? "Approving..." : "Approve Car"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Car Dialog */}
      <Dialog open={isRejectOpen} onOpenChange={setIsRejectOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Car Listing</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this car listing.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Reason for rejection"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="flex items-center gap-2 mt-2 text-sm text-amber-600">
              <AlertTriangle className="h-4 w-4" />
              <span>This reason will be sent to the car owner.</span>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRejectCar}
              disabled={isProcessing || !rejectionReason.trim()}
            >
              {isProcessing ? "Rejecting..." : "Reject Car"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
