"use client"

import { useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Check, X, Eye, AlertTriangle, ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { format, formatDistanceToNow } from "date-fns"
import { toast } from "@/components/ui/use-toast"
import { ToastAction } from "@/components/ui/toast"
import Link from "next/link"
import { Textarea } from "@/components/ui/textarea"
import { approveCarListing, rejectCarListing } from "@/app/actions/admin/admin-actions"
import Image from "next/image"
import { CarListingResponse } from "@/types/listings"

interface PaginationInfo {
  currentPage: number
  totalPages: number
  hasNextPage: boolean
  totalItems: number
  itemsPerPage: number
}

interface CarApprovalTableProps {
  cars: CarListingResponse[]
  pagination: PaginationInfo
  searchParams?: Record<string, string | undefined>
}

export function CarApprovalTable({ cars, pagination, searchParams = {} }: CarApprovalTableProps) {
  // Component to display and manage car listings
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [selectedCar, setSelectedCar] = useState<CarListingResponse | null>(null)
  const router = useRouter()
  const pathname = usePathname()
  const [isApproveOpen, setIsApproveOpen] = useState(false)
  const [isRejectOpen, setIsRejectOpen] = useState(false)
  const [rejectionReason, setRejectionReason] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [isUpdating, setIsUpdating] = useState<string | null>(null)

  const columns: ColumnDef<CarListingResponse>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "owner_name",
      header: "Owner",
      cell: ({ row }) => {
        const car = row.original
        // Use owner_name directly if available, otherwise fall back to the owner object
        return <div className="flex items-center gap-2">
        {car.owner_avatar && (
          <Image 
            src={car.owner_avatar} 
            alt={car.owner_name || ""} 
            width={32} 
            height={32} 
            className="rounded-full"
          />
        )}
        <div>
          <div>{car.owner_name || ""}</div>
          <div className="text-muted-foreground text-xs">{`${car.cancellation_policy.name} policy` || ""}</div>
        </div>
      </div>
      },
    },
    {
      accessorKey: "country_name",
      header: "Country",
      cell: ({ row }) => {
        const car = row.original
        return <div>{car.country_name || ""}</div>
      },
    },
    {
      accessorKey: "brand_name",
      header: ({ column }) => {
        return (
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
            Brand
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const car = row.original
        return <div className="font-medium">{car.brand_name || car.brand} {car.model} {car.year ? `(${car.year})` : ''}</div>
      },
    },

    {
      accessorKey: "year",
      header: "Year",
    },
    {
      accessorKey: "daily_rate",
      header: "Price",
      cell: ({ row }) => {
        const car = row.original
        const price = Number.parseFloat(row.getValue("daily_rate") || "0")
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: car.currency_code || "USD",
        }).format(price)

        return <div>{formatted}/day</div>
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const car = row.original
        const status = car.status || 'draft'

        let variant: "outline" | "secondary" | "destructive" = "outline"
        let label = "Draft"

        if (status === 'approved') {
          variant = "secondary"
          label = "Active"
        } else if (status === 'rejected') {
          variant = "destructive"
          label = "Rejected"
        }

        return (
          <Badge variant={variant}>
            {label}
          </Badge>
        )
      },
    },
    {
      accessorKey: "created_at",
      header: "Submitted",
      cell: ({ row }) => {
        const date = new Date(row.getValue("created_at"))
        return <div>{formatDistanceToNow(date)}</div>
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const car = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0" disabled={isUpdating === car.id}>
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => {
                  if (car.id) {
                    router.push(`/admin/cars/${car.id}`)
                  }
                }}
              >
                <Eye className="mr-2 h-4 w-4" />
                View details
              </DropdownMenuItem>
              {/* Only show approve/reject buttons for cars in draft/pending status */}
              {(!car.status || car.status === 'draft') && (
                <>
                  <DropdownMenuItem
                    onClick={() => {
                      setSelectedCar(car)
                      setIsApproveOpen(true)
                    }}
                  >
                    <Check className="mr-2 h-4 w-4" />
                    Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      setSelectedCar(car)
                      setRejectionReason("")
                      setIsRejectOpen(true)
                    }}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Reject
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(car.id || '')}>Copy car ID</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data: cars,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      rowSelection,
      columnVisibility,
    },
  })

  const handleApproveCar = async () => {
    if (!selectedCar) return

    setIsProcessing(true)

    try {
      if (selectedCar.id) {
        await approveCarListing(selectedCar.id)
      } else {
        throw new Error('Car ID is missing')
      }

      toast({
        title: "Car Approved Successfully",
        description: `${selectedCar.brand_name || selectedCar.brand} ${selectedCar.model} has been approved and is now active.`,
        action: (
          <ToastAction altText="View Cars">
            <Link href="/admin/cars">View Cars</Link>
          </ToastAction>
        ),
      })

      setIsApproveOpen(false)

      // Refresh the page to show updated status
      router.refresh()
    } catch (error) {
      // Handle error
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to approve car listing",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRejectCar = async () => {
    if (!selectedCar || !rejectionReason.trim()) return

    setIsProcessing(true)

    try {
      if (selectedCar.id) {
        await rejectCarListing(selectedCar.id, rejectionReason)
      } else {
        throw new Error('Car ID is missing')
      }

      toast({
        title: "Car Rejected",
        description: `${selectedCar.brand_name || selectedCar.brand} ${selectedCar.model} has been rejected.`,
        action: (
          <ToastAction altText="View Cars">
            <Link href="/admin/cars">View Cars</Link>
          </ToastAction>
        ),
      })

      setIsRejectOpen(false)

      // Refresh the page to show updated status
      router.refresh()
    } catch (error) {
      // Handle error
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to reject car listing",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Create URL with search params for pagination
  const createPageUrl = (page: number) => {
    const params = new URLSearchParams()
    
    // Add current search params
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value) params.set(key, value)
    })
    
    // Add page param
    params.set("page", page.toString())
    
    return `${pathname}?${params.toString()}`
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Car details are now shown on a separate page */}

      {/* Approve Car Dialog */}
      {selectedCar && (
        <Dialog open={isApproveOpen} onOpenChange={setIsApproveOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Approve Car Listing</DialogTitle>
              <DialogDescription>
                {(selectedCar as any).owner_name ?
                  `Listed by ${(selectedCar as any).owner_name}`
                : selectedCar.owner_name ?
                  `Listed by ${selectedCar.owner_name || ''}`.trim()
                : 'Are you sure you want to approve this car listing?'}
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <div className="flex items-center gap-4">
                <div className="relative h-16 w-16 overflow-hidden rounded-md">
                  <Image
                    src={selectedCar.car_images[0] || "/placeholder.svg?height=64&width=64"}
                    alt={`${selectedCar.brand_name || selectedCar.brand} ${selectedCar.model || ''}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-medium">{selectedCar.brand_name || selectedCar.brand} {selectedCar.model}</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedCar.year} • {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: selectedCar.currency_code || "USD",
                    }).format(Number(selectedCar.daily_rate || 0))}/day
                  </p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsApproveOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleApproveCar} disabled={isProcessing}>
                {isProcessing ? "Approving..." : "Approve Car"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Reject Car Dialog */}
      {selectedCar && (
        <Dialog open={isRejectOpen} onOpenChange={setIsRejectOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reject Car Listing</DialogTitle>
              <DialogDescription>
                {(selectedCar as any).owner_name ?
                  `Listed by ${(selectedCar as any).owner_name}`
                : selectedCar.owner_name ?
                  `Listed by ${selectedCar.owner_name || ''}`.trim()
                : 'Please provide a reason for rejecting this car listing.'}
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <div className="flex items-center gap-4 mb-4">
                <div className="relative h-16 w-16 overflow-hidden rounded-md">
                  <Image
                    src={selectedCar.car_images[0] || "/placeholder.svg?height=64&width=64"}
                    alt={`${selectedCar.brand_name || selectedCar.brand} ${selectedCar.model || ''}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-medium">{selectedCar.brand_name || selectedCar.brand} {selectedCar.model}</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedCar.year} • {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: selectedCar.currency_code || "USD",
                    }).format(Number(selectedCar.daily_rate || 0))}/day
                  </p>
                </div>
              </div>
              <Textarea
                placeholder="Reason for rejection"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="min-h-[100px]"
              />
              <div className="flex items-center gap-2 mt-2 text-sm text-amber-600">
                <AlertTriangle className="h-4 w-4" />
                <span>This reason will be sent to the car owner.</span>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsRejectOpen(false)}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleRejectCar}
                disabled={isProcessing || !rejectionReason.trim()}
              >
                {isProcessing ? "Rejecting..." : "Reject Car"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Pagination controls */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {table.getFilteredRowModel().rows.length} of {pagination.totalItems} car listings
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={pagination.currentPage <= 1}
              asChild={pagination.currentPage > 1}
            >
              {pagination.currentPage > 1 ? (
                <Link href={createPageUrl(pagination.currentPage - 1)}>
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Link>
              ) : (
                <>
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </>
              )}
            </Button>
            
            <div className="text-sm">
              Page {pagination.currentPage} of {pagination.totalPages}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              disabled={!pagination.hasNextPage}
              asChild={pagination.hasNextPage}
            >
              {pagination.hasNextPage ? (
                <Link href={createPageUrl(pagination.currentPage + 1)}>
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Link>
              ) : (
                <>
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </>
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

