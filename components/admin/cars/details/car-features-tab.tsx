"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import Image from "next/image"
import { CarListingResponse } from "@/types/listings"

interface CarFeaturesTabProps {
  car: CarListingResponse
}

export function CarFeaturesTab({ car }: CarFeaturesTabProps) {
  return (
    <div className="space-y-6">
      <Card className="border-border/60 shadow-sm overflow-hidden">
        <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
          <CardTitle className="text-lg">Features</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {car.car_features?.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {car.car_features.map((feature) => (
                <span 
                  key={feature.id}
                  className="inline-flex px-3 py-1 text-xs rounded-full bg-primary/10 text-primary border border-primary/20"
                >
                  {feature.name || 'Unknown feature'}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground italic">No features specified.</p>
          )}
        </CardContent>
      </Card>

      <Card className="border-border/60 shadow-sm overflow-hidden">
        <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
          <CardTitle className="text-lg">Gallery</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {car.car_images.length > 0 ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
              {car.car_images.map((image, index) => (
                <div key={image} className="relative aspect-video rounded-md overflow-hidden border border-border/60 group hover:border-primary/50 transition-colors">
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`${car.brand_name || car.brand} ${car.model} image ${index + 1}`}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground italic">No images available.</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 