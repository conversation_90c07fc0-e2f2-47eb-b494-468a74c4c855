"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { format } from "date-fns"
import { Clock, Check, X, FileText } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface CarHistoryTabProps {
  carUpdates: any[]
}

export function CarHistoryTab({ carUpdates }: CarHistoryTabProps) {
  return (
    <Card className="border-border/60 shadow-sm overflow-hidden">
      <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
        <CardTitle className="text-lg">Update History</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        {carUpdates?.length > 0 ? (
          <ScrollArea className="h-[500px]">
            <div className="p-6">
              <div className="relative">
                {carUpdates.map((update) => (
                  <div key={update.id} className="mb-8 last:mb-0">
                    <div className="absolute w-[2px] bg-border h-full left-4 top-0 z-0"></div>
                    <div className="relative flex gap-4 z-10">
                      <div className={`h-8 w-8 rounded-full flex items-center justify-center text-white ${
                        update.status === 'approved' ? 'bg-green-500' :
                        update.status === 'rejected' ? 'bg-red-500' :
                        update.status === 'blocked' ? 'bg-orange-500' :
                        update.status === 'reported' ? 'bg-purple-500' :
                        'bg-blue-500'
                      }`}>
                        {update.status === 'approved' ? <Check className="h-4 w-4" /> :
                         update.status === 'rejected' ? <X className="h-4 w-4" /> :
                         <Clock className="h-4 w-4" />}
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-1">
                          <h3 className="font-medium">Status changed to <span className="font-semibold">{update.status.charAt(0).toUpperCase() + update.status.slice(1)}</span></h3>
                          <time className="text-sm text-muted-foreground">
                            {format(new Date(update.updated_at), "MMM d, yyyy 'at' h:mm a")}
                          </time>
                        </div>
                        <div className="flex items-center gap-2 mt-1 mb-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={update.admin.avatar_url || ''} alt={update.admin.name} />
                            <AvatarFallback className="text-xs">
                              {update.admin.name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-muted-foreground">{update.admin.name}</span>
                        </div>
                        {update.reason && (
                          <div className="bg-muted/30 p-3 rounded-md border border-border/40 mt-2">
                            <p className="text-sm">{update.reason}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </ScrollArea>
        ) : (
          <div className="py-8 px-6 text-center">
            <FileText className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
            <p className="text-muted-foreground">No update history available</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 