"use client"

import { <PERSON><PERSON>, Avatar<PERSON>allback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { format } from "date-fns"
import { User } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CarListingResponse } from "@/types/listings"

interface CarOwnerInfoProps {
  car: CarListingResponse
}

export function CarOwnerInfo({ car }: CarOwnerInfoProps) {
  return (
    <Card className="border-border/60 shadow-sm overflow-hidden">
      <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
        <CardTitle className="text-lg">Owner Information</CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        {car.owner_name ? (
          <div className="space-y-4">
            <div className="flex items-center gap-3 pb-3 border-b border-border/40">
              <Avatar className="h-12 w-12 border-2 border-primary/20">
                <AvatarImage src={car.owner_avatar || ""} />
                <AvatarFallback className="bg-primary/10 text-primary font-medium">
                  {(car.owner_name || "U").charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">
                  {car.owner_name || "N/A"}
                </p>
              </div>
            </div>

            <div className="grid gap-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Submitted On</p>
                <p>{car.created_at ? format(new Date(car.created_at), "MMMM d, yyyy 'at' h:mm a") : 'Unknown'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                <p>{car.updated_at ? format(new Date(car.updated_at), "MMMM d, yyyy 'at' h:mm a") : 'Unknown'}</p>
              </div>
            </div>

            <Button className="w-full mt-2" asChild variant="outline">
              <a href={`/admin/users/${car.owner_id || ''}`}>
                <User className="mr-2 h-4 w-4" />
                View Owner Profile
              </a>
            </Button>
          </div>
        ) : (
          <div className="py-4 text-muted-foreground italic">Owner information not available</div>
        )}
      </CardContent>
    </Card>
  )
} 