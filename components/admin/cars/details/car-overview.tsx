"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { format } from "date-fns"
import { CarListingResponse } from "@/types/listings"

interface CarOverviewProps {
  car: CarListingResponse
}

export function CarOverview({ car }: CarOverviewProps) {
  return (
    <Card className="border-border/60 shadow-sm overflow-hidden">
      <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
        <CardTitle className="text-lg">Car Overview</CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Status</span>
            <Badge className={
              car.status === 'approved' ? 'bg-green-100 text-green-800' :
              car.status === 'rejected' ? 'bg-red-100 text-red-800' :
              car.status === 'blocked' ? 'bg-orange-100 text-orange-800' :
              car.status === 'reported' ? 'bg-purple-100 text-purple-800' :
              'bg-blue-100 text-blue-800'
            }>
              {car.status ? car.status.charAt(0).toUpperCase() + car.status.slice(1) : 'Draft'}
            </Badge>
          </div>
          <Separator />
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Daily Rate</span>
            <span className="font-medium">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: car.currency_code || "USD",
              }).format(Number(car.daily_rate || 0))}
            </span>
          </div>
          <Separator />
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Pickup Fee</span>
            <span className="font-medium">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: car.currency_code || "USD",
              }).format(Number(car.pickup_fee || 0))}
            </span>
          </div>
          <Separator />
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Next Available</span>
            <span>
              {car.next_available_date ? format(new Date(car.next_available_date), "MMM d, yyyy") : 'Anytime'}
            </span>
          </div>
          <Separator />
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Unlimited Miles</span>
            <Badge variant={
              typeof (car as any).unlimited_miles === 'boolean' 
                ? (car as any).unlimited_miles ? "default" : "outline"
                : "outline"
            }>
              {typeof (car as any).unlimited_miles === 'boolean'
                ? (car as any).unlimited_miles ? "Yes" : "No"
                : "Not Specified"
              }
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 