"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card" 
import { CarListingResponse } from "@/types/listings"

interface CarPoliciesTabProps {
  car: CarListingResponse
}

export function CarPoliciesTab({ car }: CarPoliciesTabProps) {
  return (
    <div className="space-y-6">
      <Card className="border-border/60 shadow-sm overflow-hidden">
        <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
          <CardTitle className="text-lg">Guidelines</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {car.guidelines ? (
            <div className="prose max-w-none">
              <p className="leading-relaxed whitespace-pre-line">{car.guidelines}</p>
            </div>
          ) : (
            <p className="text-muted-foreground italic">No rental guidelines provided.</p>
          )}
        </CardContent>
      </Card>

      <Card className="border-border/60 shadow-sm overflow-hidden">
        <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
          <CardTitle className="text-lg">Cancellation Policy</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {car.cancellation_policy ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-lg">{car.cancellation_policy.name}</h3>
                <Badge>{car.cancellation_policy.code}</Badge>
              </div>
              <p className="leading-relaxed">{car.cancellation_policy.description}</p>
            </div>
          ) : (
            <p className="text-muted-foreground italic">No cancellation policy specified.</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 