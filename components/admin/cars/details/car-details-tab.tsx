"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Star, Calendar, DollarSign, User } from "lucide-react"
import { CarListingResponse } from "@/types/listings"

interface CarDetailsTabProps {
  car: CarListingResponse
  carMainLocation?: any
}

export function CarDetailsTab({ car, carMainLocation }: CarDetailsTabProps) {
  return (
    <div className="space-y-6">
      <Card className="border-border/60 shadow-sm overflow-hidden">
        <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Car Information</CardTitle>
            <Badge variant="outline" className="text-xs">
              ID: {car.id?.substring(0, 8)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-6 grid gap-6 sm:grid-cols-2">
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Brand & Model</p>
              <p className="font-medium">{car.brand_name || car.brand} {car.model}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Year</p>
              <p>{car.year || 'Not specified'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Daily Rate</p>
              <p className="font-medium text-primary">{new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: car.currency_code || "USD",
              }).format(Number(car.daily_rate || 0))}/day</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Location</p>
              <p>{carMainLocation?.formatted_address || 'Not specified'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              <Badge className={
                car.status === 'approved' ? 'bg-green-100 text-green-800' :
                car.status === 'rejected' ? 'bg-red-100 text-red-800' :
                car.status === 'blocked' ? 'bg-orange-100 text-orange-800' :
                car.status === 'reported' ? 'bg-purple-100 text-purple-800' :
                'bg-blue-100 text-blue-800'
              }>
                {car.status ? car.status.charAt(0).toUpperCase() + car.status.slice(1) : 'Draft'}
              </Badge>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Registration</p>
              <p>{car.registration_number || 'Not provided'}</p>
            </div>
            {car.vin && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">VIN</p>
                <p className="font-mono text-sm">{car.vin}</p>
              </div>
            )}
            <div>
              <p className="text-sm font-medium text-muted-foreground">Seats</p>
              <p>{car.seats || 'Not specified'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Fuel Type</p>
              <p>{car.fuel_type || 'Not specified'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Driver Available</p>
              <p>{car.driver_available ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-border/60 shadow-sm overflow-hidden">
        <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
          <CardTitle className="text-lg">Description</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {car.description ? (
            <p className="leading-relaxed">{car.description}</p>
          ) : (
            <p className="text-muted-foreground italic">No description provided.</p>
          )}
        </CardContent>
      </Card>

      <Card className="border-border/60 shadow-sm overflow-hidden">
        <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
          <CardTitle className="text-lg">Performance & Statistics</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="p-4 bg-muted/20 rounded-lg text-center">
              <Star className="h-5 w-5 text-yellow-500 mx-auto mb-1" />
              <p className="text-lg font-semibold">{car.avg_overall_rating?.toFixed(1) || 'N/A'}</p>
              <p className="text-xs text-muted-foreground">Average Rating</p>
            </div>
            <div className="p-4 bg-muted/20 rounded-lg text-center">
              <Calendar className="h-5 w-5 text-blue-500 mx-auto mb-1" />
              <p className="text-lg font-semibold">{car.total_bookings || '0'}</p>
              <p className="text-xs text-muted-foreground">Total Bookings</p>
            </div>
            <div className="p-4 bg-muted/20 rounded-lg text-center">
              <DollarSign className="h-5 w-5 text-green-500 mx-auto mb-1" />
              <p className="text-lg font-semibold">
                {car.total_earnings 
                  ? new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: car.currency_code || "USD",
                      maximumFractionDigits: 0
                    }).format(car.total_earnings)
                  : 0}
              </p>
              <p className="text-xs text-muted-foreground">Total Earnings</p>
            </div>
            <div className="p-4 bg-muted/20 rounded-lg text-center">
              <User className="h-5 w-5 text-indigo-500 mx-auto mb-1" />
              <p className="text-lg font-semibold">{car.total_ratings || '0'}</p>
              <p className="text-xs text-muted-foreground">Total Reviews</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 