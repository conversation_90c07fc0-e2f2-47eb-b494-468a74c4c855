"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { MapPin } from "lucide-react"
import { CarListingResponse } from "@/types/listings"

interface CarLocationsTabProps {
  car: CarListingResponse
}

export function CarLocationsTab({ car }: CarLocationsTabProps) {
  return (
    <Card className="border-border/60 shadow-sm overflow-hidden">
      <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
        <CardTitle className="text-lg">Pickup Locations</CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        {car.available_pickup_locations?.length > 0 ? (
          <div className="space-y-4">
            {car.available_pickup_locations.map((location) => (
              <div key={location.id} className="p-4 border border-border/40 rounded-lg">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-primary" />
                    <span className="font-medium">{location.place_name || location.formatted_address}</span>
                    {location.is_main && <Badge variant="outline" className="ml-2">Main Location</Badge>}
                  </div>
                  <Badge variant={location.is_active !== false ? "secondary" : "outline"}>
                    {location.is_active !== false ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-2">{location.formatted_address}</p>
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">Pickup Fee:</span>
                    <span>
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: car.currency_code || "USD",
                      }).format(Number(location.fee || 0))}
                    </span>
                  </div>
                  <div className="flex gap-2 items-center">
                    <span className="text-muted-foreground">Coordinates:</span>
                    <span className="font-mono text-xs">
                      {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-muted-foreground italic">No pickup locations specified.</p>
        )}
      </CardContent>
    </Card>
  )
} 