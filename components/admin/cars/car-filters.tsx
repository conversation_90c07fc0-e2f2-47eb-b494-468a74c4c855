"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, X } from "lucide-react"
import { useQuery } from "@tanstack/react-query"
import { getCountries } from "@/app/actions/admin/admin-actions"

interface CarFiltersProps {
  currentStatus?: string | null
  currentCountryId?: string | null
  currentSearch?: string | null
}

interface Country {
  id: string
  name: string
}

export function CarFilters({ currentStatus, currentCountryId, currentSearch }: CarFiltersProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [status, setStatus] = useState<string | null>(currentStatus || null)
  const [countryId, setCountryId] = useState<string | null>(currentCountryId || null)
  const [search, setSearch] = useState(currentSearch || "")

  const { data: countries, isLoading: isLoadingCountries } = useQuery({
    queryKey: ['countries'],
    queryFn: () => getCountries()
  });

  const applyFilters = () => {
    const params = new URLSearchParams()
    
    if (search) params.set("search", search)
    if (status) params.set("status", status)
    if (countryId) params.set("country_id", countryId)
    
    // Reset to page 1 when filters change
    params.set("page", "1")
    
    router.push(`${pathname}?${params.toString()}`)
  }

  const clearFilters = () => {
    setSearch("")
    setStatus(null)
    setCountryId(null)
    router.push(pathname)
  }

  return (
    <div className="flex flex-col sm:flex-row gap-3 mt-4">
      <div className="relative flex-1">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search cars..."
          className="pl-8"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && applyFilters()}
        />
      </div>
      
      <Select
        value={status || ""}
        onValueChange={(value) => setStatus(value || null)}
      >
        <SelectTrigger className="w-full sm:w-[180px]">
          <SelectValue placeholder="Filter by status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="draft">Draft</SelectItem>
          <SelectItem value="under_review">Under Review</SelectItem>
          <SelectItem value="approved">Approved</SelectItem>
          <SelectItem value="rejected">Rejected</SelectItem>
          <SelectItem value="reported">Reported</SelectItem>
          <SelectItem value="blocked">Blocked</SelectItem>
        </SelectContent>
      </Select>
      
      <Select
        value={countryId || ""}
        onValueChange={(value) => setCountryId(value || null)}
        disabled={isLoadingCountries}
      >
        <SelectTrigger className="w-full sm:w-[180px]">
          <SelectValue placeholder="Filter by country" />
        </SelectTrigger>
        <SelectContent>
          {countries?.map(country => (
            <SelectItem key={country.id} value={country.id}>
              {country.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <Button onClick={applyFilters}>Apply Filters</Button>
      
      {(search || status || countryId) && (
        <Button variant="outline" onClick={clearFilters}>
          <X className="mr-2 h-4 w-4" />
          Clear
        </Button>
      )}
    </div>
  )
} 