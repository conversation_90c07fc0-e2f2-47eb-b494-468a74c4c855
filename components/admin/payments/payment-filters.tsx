"use client"

import { useState, useEffect } from "react"
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  <PERSON>et<PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { PaymentFilterValues } from "@/types/payments"

interface PaymentFiltersProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  filters: PaymentFilterValues
  onApplyFilters: (filters: PaymentFilterValues) => void
  onResetFilters: () => void
}

export default function PaymentFilters({
  isOpen,
  onOpenChange,
  filters,
  onApplyFilters,
  onResetFilters
}: PaymentFiltersProps) {
  const [localFilters, setLocalFilters] = useState<PaymentFilterValues>(filters)

  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  const handleApplyFilters = () => {
    onApplyFilters(localFilters)
  }

  const updateFilter = (key: keyof PaymentFilterValues, value: any) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-[400px] sm:max-w-[540px]">
        <SheetHeader>
          <SheetTitle>Filter Payments</SheetTitle>
          <SheetDescription>
            Apply filters to narrow down the payment results
          </SheetDescription>
        </SheetHeader>
        
        <div className="py-6 space-y-6">
          <div className="space-y-2">
            <Label htmlFor="status">Payment Status</Label>
            <Select 
              value={localFilters.status || undefined} 
              onValueChange={(value) => updateFilter("status", value || null)}
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="refunded">Refunded</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="payment_type">Payment Type</Label>
            <Select 
              value={localFilters.payment_type || undefined} 
              onValueChange={(value) => updateFilter("payment_type", value || null)}
            >
              <SelectTrigger id="payment_type">
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                <SelectItem value="booking">Booking</SelectItem>
                <SelectItem value="security_deposit">Security Deposit</SelectItem>
                <SelectItem value="extension">Extension</SelectItem>
                <SelectItem value="damage">Damage</SelectItem>
                <SelectItem value="late_return">Late Return</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="payment_method">Payment Method</Label>
            <Select 
              value={localFilters.payment_method || undefined} 
              onValueChange={(value) => updateFilter("payment_method", value || null)}
            >
              <SelectTrigger id="payment_method">
                <SelectValue placeholder="All methods" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All methods</SelectItem>
                <SelectItem value="card">Card</SelectItem>
                <SelectItem value="mobile_money">Mobile Money</SelectItem>
                <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                <SelectItem value="cash">Cash</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !localFilters.start_date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {localFilters.start_date ? format(new Date(localFilters.start_date), "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={localFilters.start_date ? new Date(localFilters.start_date) : undefined}
                    onSelect={(date) => updateFilter("start_date", date ? date.toISOString() : null)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <Label>End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !localFilters.end_date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {localFilters.end_date ? format(new Date(localFilters.end_date), "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={localFilters.end_date ? new Date(localFilters.end_date) : undefined}
                    onSelect={(date) => updateFilter("end_date", date ? date.toISOString() : null)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
        
        <SheetFooter className="flex flex-row gap-2 sm:space-x-0">
          <Button variant="outline" onClick={onResetFilters}>Reset Filters</Button>
          <Button onClick={handleApplyFilters}>Apply Filters</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
} 