"use client"

import { useState } from "react"
import { BookingPayment } from "@/types/payments"
import { formatDate, formatCurrency } from "@/lib/utils"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ExternalLink, CheckCircle, ArrowDownCircle, DollarSign } from "lucide-react"

interface PaymentDetailsModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  payment: BookingPayment | null
}

export default function PaymentDetailsModal({
  isOpen,
  onOpenChange,
  payment
}: PaymentDetailsModalProps) {
  const [activeTab, setActiveTab] = useState("payment-lines")
  
  if (!payment) return null

  // Format the payment data for display
  const formattedStartDate = formatDate(payment.start_date)
  const formattedEndDate = formatDate(payment.end_date)
  
  // Calculate nights (if dates are available)
  const startDate = new Date(payment.start_date)
  const endDate = new Date(payment.end_date)
  const nights = isNaN(startDate.getTime()) || isNaN(endDate.getTime()) 
    ? 0 
    : Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  
  // Calculate service fee (10% of the total booking payment)
  const serviceFee = payment.amount * 0.1
  
  // Calculate host payout (total - service fee)
  const hostPayout = payment.amount - serviceFee

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Payment Details</DialogTitle>
        </DialogHeader>
        
        <div className="bg-slate-50 p-4 rounded-md mt-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10 bg-slate-200">
              <AvatarImage src={payment.renter.avatar_url} alt="Platform" />
              <AvatarFallback className="bg-slate-200">
                {payment.payment_method?.charAt(0).toUpperCase() || "P"}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">{payment.renter.full_name}</p>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            {formatDate(payment.payment_date)}
          </div>
        </div>
        
        <div className="mt-6">
          <Tabs defaultValue="payment-lines" onValueChange={setActiveTab} value={activeTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="payment-lines">Payment Details</TabsTrigger>
              <TabsTrigger value="workflow-logs">Transaction History</TabsTrigger>
            </TabsList>
            
            <TabsContent value="payment-lines" className="mt-4">
              <Card>
                <CardContent className="p-0">
                  <div className="py-4 px-6">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium">{payment.renter.full_name}</div>
                        <div className="text-sm text-blue-500 flex items-center">
                          {payment.renter.id} <ExternalLink className="h-3 w-3 ml-1" />
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm">
                          {formattedStartDate} - {formattedEndDate}
                        </div>
                        {nights > 0 && (
                          <div className="text-sm text-muted-foreground">{nights} nights</div>
                        )}
                      </div>
                    </div>
                    
                    {/* Main payment */}
                    <div className="flex justify-between py-1">
                      <div>{payment.payment_type.replace('_', ' ')}</div>
                      <div>{formatCurrency(payment.amount, payment.currency)}</div>
                    </div>
                    
                    {/* Service fee (10% of total) */}
                    <div className="flex justify-between py-1">
                      <div>Service Fee (10%)</div>
                      <div className="text-red-500">
                        {formatCurrency(-serviceFee, payment.currency)}
                      </div>
                    </div>
                    
                    <div className="flex justify-end mt-2 font-medium">
                      {formatCurrency(payment.amount, payment.currency)}
                    </div>
                    
                    <Separator className="mt-4" />
                    
                    {/* Car owner section */}
                    <div className="mt-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="font-medium">{payment.car_owner.full_name}</div>
                          <div className="text-sm text-blue-500 flex items-center">
                            {payment.car_owner.id} <ExternalLink className="h-3 w-3 ml-1" />
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-between py-1">
                        <div>Host Payout</div>
                        <div>
                          {formatCurrency(hostPayout, payment.currency)}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-slate-50 p-4 flex justify-between font-medium">
                    <div>Total</div>
                    <div>{formatCurrency(payment.amount, payment.currency)}</div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="workflow-logs" className="mt-4">
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                        <CheckCircle className="h-5 w-5" />
                      </div>
                      <div>
                        <p className="font-medium">Payment {payment.payment_status.toLowerCase()}</p>
                        <p className="text-sm text-muted-foreground">{formatDate(payment.payment_date)}</p>
                      </div>
                    </div>
                    
                    {payment.payment_status === "success" && (
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                          <ArrowDownCircle className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="font-medium">Payment processed</p>
                          <p className="text-sm text-muted-foreground">{formatDate(payment.payment_date)}</p>
                        </div>
                      </div>
                    )}
                    
                    {payment.payment_status === "success" && (
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                          <DollarSign className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="font-medium">Host payout initiated</p>
                          <p className="text-sm text-muted-foreground">{formatDate(payment.payment_date)}</p>
                        </div>
                      </div>
                    )}
                    
                    {/* If no additional timestamps are available, show updated_at */}
                    {payment.payment_status === "success" && (
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                          <ArrowDownCircle className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="font-medium">Last updated</p>
                          <p className="text-sm text-muted-foreground">{formatDate(payment.payment_date)}</p>
                        </div>
                      </div>
                    )}
                    
                    {/* Display transaction ID if available */}
                    {payment.transaction_id && (
                      <div className="mt-6 pt-4 border-t">
                        <p className="text-sm text-muted-foreground">Transaction ID:</p>
                        <p className="font-mono text-sm">{payment.transaction_id}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 