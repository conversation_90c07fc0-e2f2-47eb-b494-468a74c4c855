"use client"

import { BookingPayment } from "@/types/payments"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { formatDate, formatCurrency } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { Eye } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface PaymentsListProps {
  payments: BookingPayment[]
  loading: boolean
  onViewDetails: (payment: BookingPayment) => void
}

export default function PaymentsList({
  payments,
  loading,
  onViewDetails
}: PaymentsListProps) {
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return <Badge className="bg-green-500">Completed</Badge>
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>
      case "failed":
        return <Badge className="bg-red-500">Failed</Badge>
      case "refunded":
        return <Badge className="bg-blue-500">Refunded</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getPaymentTypeBadge = (type: string) => {
    switch (type.toLowerCase()) {
      case "booking":
        return <Badge variant="outline" className="bg-slate-100">Booking</Badge>
      case "security_deposit":
        return <Badge variant="outline" className="bg-blue-100">Security Deposit</Badge>
      case "extension":
        return <Badge variant="outline" className="bg-purple-100">Extension</Badge>
      case "damage":
        return <Badge variant="outline" className="bg-red-100">Damage</Badge>
      case "late_return":
        return <Badge variant="outline" className="bg-orange-100">Late Return</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  const truncateId = (id: string) => {
    return id.substring(0, 8) + "..."
  }

  return (
    <div className="border rounded-md overflow-auto" style={{ maxHeight: "calc(100vh - 200px)" }}>
      <Table>
        <TableHeader className="sticky top-0 bg-card z-10">
          <TableRow>
            <TableHead>Renter</TableHead>
            <TableHead>Car Owner</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Transaction ID</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Method</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading && payments.length === 0 ? (
            Array(10).fill(0).map((_, i) => (
              <TableRow key={i}>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-5 w-10" /></TableCell>
              </TableRow>
            ))
          ) : payments.length === 0 ? (
            <TableRow>
              <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                No payments found
              </TableCell>
            </TableRow>
          ) : (
            payments.map((payment) => (
              <TableRow key={payment.payment_id}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={payment.renter.avatar_url} alt={payment.renter.full_name} />
                      <AvatarFallback>
                        {payment.renter.full_name?.charAt(0) || "R"}
                      </AvatarFallback>
                    </Avatar>
                    <span className="truncate max-w-[120px]" title={payment.renter.full_name}>
                      {payment.renter.full_name}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={payment.car_owner.avatar_url} alt={payment.car_owner.full_name} />
                      <AvatarFallback>
                        {payment.car_owner.full_name?.charAt(0) || "O"}
                      </AvatarFallback>
                    </Avatar>
                    <span className="truncate max-w-[120px]" title={payment.car_owner.full_name}>
                      {payment.car_owner.full_name}
                    </span>
                  </div>
                </TableCell>
                <TableCell>{formatCurrency(payment.amount, payment.currency)}</TableCell>
                <TableCell>{payment.transaction_id}</TableCell>
                <TableCell>{getPaymentTypeBadge(payment.payment_type)}</TableCell>
                <TableCell className="capitalize">{payment.payment_method?.replace('_', ' ')}</TableCell>
                <TableCell>{getStatusBadge(payment.payment_status)}</TableCell>
                <TableCell>{formatDate(payment.payment_date)}</TableCell>
                <TableCell>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => onViewDetails(payment)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
} 