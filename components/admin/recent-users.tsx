import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { UserProfile } from "@/types/users"

interface RecentUsersProps {
  users?: UserProfile[]
}

export function RecentUsers({ users = [] }: RecentUsersProps) {
  // Take only the first 4 users
  const recentUsers = users.slice(0, 4)

  return (
    <div className="space-y-8">
      {recentUsers.map((user) => (
        <div key={user.id} className="flex items-center">
          <Avatar className="h-9 w-9">
            <AvatarImage src={user.avatar_url || "/placeholder-user.jpg"} alt={user.first_name} />
            <AvatarFallback>{user.first_name?.charAt(0) || "U"}</AvatarFallback>
          </Avatar>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{user.first_name} {user.last_name}</p>
            <p className="text-sm text-muted-foreground">{user.email}</p>
          </div>
          <div className="ml-auto text-right">
            <Badge variant={user.role === "user" ? "outline" : "secondary"}>
              {user.role === "user" ? "user" : user.role}
            </Badge>
            <p className="mt-1 text-sm text-muted-foreground">
              {user.created_at ? new Date(user.created_at).toLocaleDateString() : ''}
            </p>
          </div>
        </div>
      ))}
    </div>
  )
}

