"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { format } from "date-fns"
import { User as UserIcon } from "lucide-react"
import { User } from "@/types/users"

interface UserInfoCardProps {
  user: User
  role?: string
  relationshipId?: string
  relationshipDate?: string
  className?: string
}

export function UserInfoCard({ 
  user, 
  role, 
  relationshipId, 
  relationshipDate, 
  className 
}: UserInfoCardProps) {
  const name = `${user.first_name || ""} ${user.last_name || ""}`.trim() || "Unknown";
  
  return (
    <div className={`p-4 border rounded-lg space-y-3 ${className}`}>
      <div className="flex items-center gap-3">
        <Avatar>
          <AvatarImage src={user.avatar_url} />
          <AvatarFallback>
            <UserIcon className="h-4 w-4" />
          </AvatarFallback>
        </Avatar>
        <div>
          <p className="font-medium">{name}</p>
          {user.email && <p className="text-sm text-muted-foreground">{user.email}</p>}
          {user.phone && <p className="text-sm text-muted-foreground">{user.phone}</p>}
          {role && <p className="text-xs bg-primary/10 text-primary rounded-full px-2 py-0.5 inline-block mt-1">{role}</p>}
        </div>
      </div>
      
      {relationshipId && (
        <div className="grid grid-cols-2 gap-2 text-sm pt-2 border-t">
          <div>
            <p className="text-muted-foreground text-xs">User ID</p>
            <p className="font-mono text-xs">{user.id}</p>
          </div>
          {relationshipDate && (
            <div>
              <p className="text-muted-foreground text-xs">Joined</p>
              <p>{relationshipDate ? format(new Date(relationshipDate), "MMM d, yyyy") : "N/A"}</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
} 