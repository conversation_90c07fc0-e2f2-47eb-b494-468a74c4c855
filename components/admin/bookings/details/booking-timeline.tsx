"use client"

import { BookingResponse } from "@/types/bookings"
import { Check, Clock, X } from "lucide-react"
import { format } from "date-fns"

interface BookingTimelineProps {
  booking: BookingResponse
  className?: string
}

export function BookingTimeline({ booking, className }: BookingTimelineProps) {
  // Format date time
  const formatDateTime = (date?: string) => date ? format(new Date(date), "MMM d, yyyy 'at' h:mm a") : "N/A"

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-3">
        <Clock className="h-5 w-5 text-muted-foreground" />
        <div>
          <p className="font-medium">Booking Created</p>
          <p className="text-sm text-muted-foreground">{formatDateTime(booking.created_at)}</p>
        </div>
      </div>
      
      {booking.status === "confirmed" && (
        <div className="flex items-center gap-3">
          <Check className="h-5 w-5 text-green-500" />
          <div>
            <p className="font-medium">Booking Confirmed</p>
            <p className="text-sm text-muted-foreground">{formatDateTime(booking.updated_at)}</p>
          </div>
        </div>
      )}
      
      {booking.status === "ongoing" && (
        <div className="flex items-center gap-3">
          <Clock className="h-5 w-5 text-orange-500" />
          <div>
            <p className="font-medium">Booking Ongoing</p>
            <p className="text-sm text-muted-foreground">{formatDateTime(booking.updated_at)}</p>
          </div>
        </div>
      )}
      
      {booking.status === "cancelled" && (
        <div className="flex items-center gap-3">
          <X className="h-5 w-5 text-red-500" />
          <div>
            <p className="font-medium">Booking Cancelled</p>
            <p className="text-sm text-muted-foreground">{formatDateTime(booking.updated_at)}</p>
            {booking.cancellation_reason && (
              <p className="text-sm mt-1 p-2 bg-muted rounded-md">{booking.cancellation_reason}</p>
            )}
          </div>
        </div>
      )}
      
      {booking.status === "completed" && (
        <div className="flex items-center gap-3">
          <Check className="h-5 w-5 text-blue-500" />
          <div>
            <p className="font-medium">Booking Completed</p>
            <p className="text-sm text-muted-foreground">{formatDateTime(booking.updated_at)}</p>
          </div>
        </div>
      )}
    </div>
  )
} 