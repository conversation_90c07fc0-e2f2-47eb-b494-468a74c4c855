"use client"

import { BookingModificationRequest } from "@/types/bookings"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { <PERSON><PERSON>, AlertTitle } from "@/components/ui/alert"
import { ClipboardList, CalendarRange, MapPin, DollarSign, AlertTriangle, Calendar, CreditCard } from "lucide-react"

interface ModificationDetailsModalProps {
  modification: BookingModificationRequest
  booking: any
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ModificationDetailsModal({ 
  modification, 
  booking, 
  open, 
  onOpenChange 
}: ModificationDetailsModalProps) {
  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A"
    return format(new Date(dateString), "MMM d, yyyy")
  }

  // Format date and time
  const formatDateTime = (dateString?: string) => {
    if (!dateString) return "N/A"
    return format(new Date(dateString), "MMM d, yyyy 'at' h:mm a")
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-500 text-white">Approved</Badge>
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>
      case "declined":
        return <Badge variant="destructive">Declined</Badge>
      case "canceled":
        return <Badge className="bg-orange-500 text-white">Canceled</Badge>
      case "completed":
        return <Badge className="bg-blue-500 text-white">Completed</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <ClipboardList className="h-5 w-5" />
            Booking Modification Request
            {getStatusBadge(modification.status)}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 my-4">
          {/* Header Information */}
          <div className="grid grid-cols-2 gap-4 bg-muted/20 p-4 rounded-md">
            <div>
              <p className="text-sm text-muted-foreground">Requested By</p>
              <p className="font-medium">
                {modification.requester_id === booking.renter_id ? 'Renter' : 'Host'}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Requested On</p>
              <p>{formatDateTime(modification.created_at)}</p>
            </div>
            <div className="col-span-2">
              <p className="text-sm text-muted-foreground">Request Message</p>
              <p className="p-2 bg-muted rounded-md mt-1">
                {modification.message || 'No message provided'}
              </p>
            </div>
          </div>

          {/* Requested Changes Section */}
          <div>
            <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              Requested Changes
            </h3>

            <div className="space-y-4 border rounded-md p-4">
              {/* Date Changes */}
              {(modification.requested_changes.start_date || modification.requested_changes.end_date) && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-center gap-1 mb-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm font-medium">Date Changes</p>
                    </div>
                    <div className="grid grid-cols-2 gap-2 bg-muted/30 p-2 rounded-md">
                      <div>
                        <p className="text-xs text-muted-foreground">Original Start</p>
                        <p>{formatDate(modification.previous_data.start_date)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Original End</p>
                        <p>{formatDate(modification.previous_data.end_date)}</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-1 mb-1">
                      <CalendarRange className="h-4 w-4 text-green-500" />
                      <p className="text-sm font-medium">New Dates</p>
                    </div>
                    <div className="grid grid-cols-2 gap-2 bg-green-50 dark:bg-green-900/20 p-2 rounded-md">
                      <div>
                        <p className="text-xs text-muted-foreground">New Start</p>
                        <p>{formatDate(modification.requested_changes.start_date) || 'Unchanged'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">New End</p>
                        <p>{formatDate(modification.requested_changes.end_date) || 'Unchanged'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Location Changes */}
              {modification.requested_changes.pickup_location_id && (
                <div>
                  <div className="flex items-center gap-1 mb-1">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <p className="text-sm font-medium">Pickup Location Change</p>
                  </div>
                  <Alert>
                    <AlertTitle>Pickup location has been changed</AlertTitle>
                  </Alert>
                </div>
              )}

              {/* Price Changes */}
              {(modification.requested_changes.total_amount || modification.requested_changes.additional_amount) && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-center gap-1 mb-1">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm font-medium">Original Price</p>
                    </div>
                    <div className="bg-muted/30 p-2 rounded-md">
                      <p className="text-lg font-medium">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: booking.currency_details?.code || "UGX"
                        }).format(Number(modification.previous_data.total_amount) || 0)}
                      </p>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-1 mb-1">
                      <CreditCard className="h-4 w-4 text-green-500" />
                      <p className="text-sm font-medium">New Price</p>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 p-2 rounded-md">
                      <p className="text-lg font-medium">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: booking.currency_details?.code || "UGX"
                        }).format(Number(modification.requested_changes.total_amount) || Number(modification.previous_data.total_amount) + (Number(modification.requested_changes.additional_amount) || 0))}
                      </p>
                      {modification.requested_changes.additional_amount && (
                        <p className="text-xs text-muted-foreground">
                          Additional amount: {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: booking.currency_details?.code || "UGX"
                          }).format(Number(modification.requested_changes.additional_amount))}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Response Section (if any) */}
          {modification.status !== "pending" && (
            <div>
              <h3 className="text-lg font-medium mb-2">Response</h3>
              <div className="border rounded-md p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Status</p>
                    <div className="mt-1">{getStatusBadge(modification.status)}</div>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {modification.status === "approved" || modification.status === "completed" ? "Approved On" : "Responded On"}
                    </p>
                    <p>{modification.updated_at ? formatDateTime(modification.updated_at) : 'N/A'}</p>
                  </div>
                </div>
                {modification.host_response_message && (
                  <div className="mt-3">
                    <p className="text-sm text-muted-foreground">Host Message</p>
                    <p className="p-2 bg-muted rounded-md mt-1">
                      {modification.host_response_message}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Payment Details (if completed) */}
          {modification.status === "completed" && modification.payment_id && (
            <div>
              <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Payment Information
              </h3>
              <div className="border rounded-md p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Payment Status</p>
                    <Badge className="mt-1" variant={modification.payment_status === "pending" ? "outline" : "default"}>
                      {modification.payment_status || "Unknown"}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Transaction ID</p>
                    <p className="font-mono text-xs mt-1">{modification.payment_transaction_id || 'N/A'}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 