"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { CalendarClock, DollarSign, FileText, Users, XCircle } from "lucide-react"
import { BookingResponse } from "@/types/bookings"
import { updateBookingStatus, getBookingById, getRenterProfile } from "@/app/actions/admin/admin-actions"
import { useToast } from "@/hooks/use-toast"
import { BookingInfoCard } from "./booking-info-card"
import { UserInfoCard } from "./user-info-card"
import { BookingTimeline } from "./booking-timeline"
import { BookingPayments } from "./booking-payments"
import { BookingPeriod } from "./booking-period"
import { PaymentSummary } from "./payment-summary"
import { CarInfo } from "./car-info"

interface BookingDetailsModalProps {
  booking: BookingResponse | null
  isOpen: boolean
  onClose: () => void
}

export function BookingDetailsModal({ booking: initialBooking, isOpen, onClose }: BookingDetailsModalProps) {
  const router = useRouter()
  const [isProcessing, setIsProcessing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")
  const [booking, setBooking] = useState<BookingResponse | null>(initialBooking)
  const [renterProfile, setRenterProfile] = useState<any>(null)
  const { toast } = useToast()
  // Fetch full booking details and renter profile when modal opens
  useEffect(() => {
    const fetchDetails = async () => {
      if (!initialBooking?.id || !isOpen) return
      
      try {
        setIsLoading(true)
        
        // Fetch booking details
        const bookingDetails = await getBookingById(initialBooking.id)
        if (bookingDetails) {
          // Cast the response to unknown first to avoid TypeScript errors about missing fields
          setBooking(bookingDetails as unknown as BookingResponse)
        }
        
        // Fetch renter details if available
        if (bookingDetails?.renter_id) {
          const renterDetails = await getRenterProfile(bookingDetails.renter_id)
          setRenterProfile(renterDetails)
        }
      } catch (error) {
        console.error("Error fetching booking details:", error)
        toast({
          title: "Error",
          description: "Failed to load booking details",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchDetails()
  }, [initialBooking?.id, isOpen])
  
  // Handle status update
  const handleStatusUpdate = async (newStatus: "confirmed" | "cancelled" | "completed", notes?: string) => {
    if (!booking?.id) return
    
    try {
      setIsProcessing(true)
      await updateBookingStatus(booking.id, newStatus, notes)
      
      // Refresh booking data
      const updatedBooking = await getBookingById(booking.id)
      if (updatedBooking) {
        // Cast the response to unknown first to avoid TypeScript errors about missing fields
        setBooking(updatedBooking as unknown as BookingResponse)
      }
      
      toast({
        title: `Booking ${newStatus}`,
        description: `Booking has been marked as ${newStatus}`,
      })
      
      router.refresh()
    } catch (error) {
      console.error(`Error updating status to ${newStatus}:`, error)
      toast({
        title: "Error",
        description: `Failed to update booking status: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }
  
  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return <Badge className="bg-green-500 text-white">Confirmed</Badge>
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>
      case "completed":
        return <Badge className="bg-blue-500 text-white">Completed</Badge>
      case "ongoing":
        return <Badge className="bg-orange-500 text-white">Ongoing</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }
  
  // Loading state
  const renderLoading = () => (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[50vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle><Skeleton className="h-8 w-[200px]" /></DialogTitle>
          <DialogDescription><Skeleton className="h-4 w-full" /></DialogDescription>
        </DialogHeader>
        <div className="grid gap-4">
          <Skeleton className="h-64 w-full" />
          <div className="grid grid-cols-2 gap-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
  
  // If no booking or still loading initial data, show skeleton loader
  if (!booking || isLoading) {
    return renderLoading()
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
        <DialogHeader className="px-6 pt-6 pb-2">
          <DialogTitle className="text-xl flex items-center gap-2">
            <span>Booking {booking.id?.substring(0, 8)}</span>
            {getStatusBadge(booking.status)}
          </DialogTitle>
          <DialogDescription>
            {booking.brand_name} {booking.model} • {booking.start_date && new Date(booking.start_date).toLocaleDateString()} - {booking.end_date && new Date(booking.end_date).toLocaleDateString()}
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="px-6">
            <TabsList className="grid grid-cols-4 w-full max-w-xl">
              <TabsTrigger value="overview">
                Details
              </TabsTrigger>
              <TabsTrigger value="payments">
                Payments
              </TabsTrigger>
              <TabsTrigger value="users">
                Host
              </TabsTrigger>
              <TabsTrigger value="details">
                Details
              </TabsTrigger>
            </TabsList>
          </div>
          
          <ScrollArea className="overflow-y-auto">
            <div className="px-6 pb-6 pt-2">
              <TabsContent value="overview" className="mt-2 space-y-6">
                {/* Overview with a cleaner design */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <BookingInfoCard title="Booking Period" icon={<CalendarClock className="h-5 w-5" />}>
                    <BookingPeriod booking={booking} />
                  </BookingInfoCard>
                  
                  <BookingInfoCard title="Payment Summary" icon={<DollarSign className="h-5 w-5" />}>
                    <PaymentSummary booking={booking} />
                  </BookingInfoCard>
                </div>
                
                <BookingInfoCard title="Timeline">
                  <BookingTimeline booking={booking} />
                </BookingInfoCard>
              </TabsContent>
              
              <TabsContent value="payments" className="mt-2">
                <BookingPayments booking={booking} />
              </TabsContent>
              
              <TabsContent value="users" className="mt-2 space-y-6">
                {/* Car Owner */}
                <BookingInfoCard title="Car Owner">
                  <UserInfoCard 
                    name={`${booking.host?.first_name || ""} ${booking.host?.last_name || ""}`.trim()} 
                    avatar={booking.host?.avatar ? booking.host.avatar : undefined} 
                    phone={booking.host?.phone as string | undefined} 
                    role="Car Owner"
                  />
                </BookingInfoCard>
                
                {/* Renter */}
                <BookingInfoCard title="Renter">
                  {renterProfile ? (
                    <UserInfoCard 
                      name={`${renterProfile.first_name || ""} ${renterProfile.last_name || ""}`.trim()} 
                      avatar={renterProfile.avatar_url} 
                      email={renterProfile.email}
                      phone={renterProfile.phone as string | undefined} 
                      role="Renter"
                    />
                  ) : (
                    <div className="flex items-center justify-center py-4">
                      <Skeleton className="h-10 w-full" />
                    </div>
                  )}
                </BookingInfoCard>
              </TabsContent>
              
              <TabsContent value="details" className="mt-2 space-y-6">
                {/* Car Information */}
                <BookingInfoCard title="Car Information">
                  <CarInfo booking={booking} />
                </BookingInfoCard>
                
                {/* Booking Details */}
                <BookingInfoCard title="Booking Details">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Booking ID</p>
                        <p className="font-mono text-sm">{booking.id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Created</p>
                        <p>{booking.created_at && new Date(booking.created_at).toLocaleString()}</p>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    {booking.pickup_instructions && (
                      <div>
                        <p className="text-sm text-muted-foreground">Pickup Instructions</p>
                        <p className="p-2 bg-muted rounded-md mt-1">{booking.pickup_instructions}</p>
                      </div>
                    )}
                    
                    {booking.return_instructions && (
                      <div>
                        <p className="text-sm text-muted-foreground">Return Instructions</p>
                        <p className="p-2 bg-muted rounded-md mt-1">{booking.return_instructions}</p>
                      </div>
                    )}
                    
                    {booking.host_notes && (
                      <div>
                        <p className="text-sm text-muted-foreground">Host Notes</p>
                        <p className="p-2 bg-muted rounded-md mt-1">{booking.host_notes}</p>
                      </div>
                    )}
                  </div>
                </BookingInfoCard>
              </TabsContent>
            </div>
          </ScrollArea>
        </Tabs>
        
        <DialogFooter className="px-6 py-4 border-t">
          <Button variant="ghost" onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 