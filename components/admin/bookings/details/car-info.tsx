"use client"

import { BookingResponse } from "@/types/bookings"
import { Car, CreditCard } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"

interface CarInfoProps {
  booking: BookingResponse
  className?: string
}

export function CarInfo({ booking, className }: CarInfoProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-4">
        {booking.car_images?.[0] ? (
          <div className="relative w-24 h-24 rounded-md overflow-hidden">
            <img
              src={booking.car_images[0]}
              alt={`${booking.brand_name} ${booking.model}`}
              className="object-cover w-full h-full"
            />
          </div>
        ) : (
          <div className="w-24 h-24 rounded-md bg-muted flex items-center justify-center">
            <Car className="h-8 w-8 text-muted-foreground" />
          </div>
        )}
        
        <div>
          <h3 className="text-lg font-medium">{booking.brand_name} {booking.model}</h3>
          <p className="text-muted-foreground">
            {booking.year}
          </p>
          <div className="flex items-center gap-2 mt-1">
            <CreditCard className="h-4 w-4 text-muted-foreground" />
            <span>
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: booking.currency_details?.code || "USD"
              }).format(booking.daily_charge || 0)}
              <span className="text-sm text-muted-foreground"> / day</span>
            </span>
          </div>
        </div>
      </div>
      
      <Separator />
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Car ID</p>
          <p className="font-mono text-sm">{booking.car_id}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Booking Duration</p>
          <p>{booking.end_date && booking.start_date 
            ? `${Math.ceil((new Date(booking.end_date).getTime() - new Date(booking.start_date).getTime()) / (1000 * 60 * 60 * 24))} days` 
            : "N/A"}</p>
        </div>
      </div>
      
      <Button variant="outline" size="sm" className="w-full" asChild>
        <a href={`/admin/cars/${booking.car_id}`} target="_blank" rel="noopener noreferrer">
          <Car className="h-4 w-4 mr-2" />
          View Car Details
        </a>
      </Button>
    </div>
  )
} 