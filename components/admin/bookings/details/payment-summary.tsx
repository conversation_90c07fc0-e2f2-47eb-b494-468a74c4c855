"use client"

import { BookingResponse } from "@/types/bookings"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface PaymentSummaryProps {
  booking: BookingResponse
  className?: string
}

export function PaymentSummary({ booking, className }: PaymentSummaryProps) {
  return (
    <div className={`space-y-3 ${className}`}>
      <div>
        <p className="text-sm text-muted-foreground">Total Amount</p>
        <p className="text-xl font-semibold">
          {new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: booking.currency_details?.code || "USD"
          }).format(booking.total_amount || 0)}
        </p>
      </div>
      
      <Separator />
      
      <div>
        <p className="text-sm text-muted-foreground">Daily Rate...</p>
        <p className="text-sm text-muted-foreground">
          {new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: booking.currency_details?.code || "USD"
          }).format(booking.daily_charge || 0)}
          <span className="text-sm text-muted-foreground"> / day</span>
        </p>
      </div>
      
      <div>
        <p className="text-sm text-muted-foreground">Payment Status</p>
        <Badge variant="outline" className="mt-1">
          {booking.status}
        </Badge>
      </div>
    </div>
  )
} 