"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>ead<PERSON>, CardTitle } from "@/components/ui/card"
import { ReactNode } from "react"

interface BookingInfoCardProps {
  title: string
  children: ReactNode
  icon?: ReactNode
  className?: string
}

export function BookingInfoCard({ title, children, icon, className }: BookingInfoCardProps) {
  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  )
} 