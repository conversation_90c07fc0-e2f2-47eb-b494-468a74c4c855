"use client"

import { format } from "date-fns"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CreditCard, DollarSign } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { BookingPayment } from "@/types/bookings"

interface BookingPaymentsProps {
  payments: BookingPayment[]
  className?: string
}

export function BookingPayments({ payments, className }: BookingPaymentsProps) {
  if (!payments.length) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">Payment History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-4 rounded-lg text-center">
            <DollarSign className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">No payment records found for this booking.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Format date
  const formatDate = (date?: string) => date ? format(new Date(date), "MMMM d, yyyy") : "N/A"

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">Payment History</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {payments.map((payment: any, index) => (
          <div key={payment.id || index}>
            <div className="flex justify-between items-start mb-2">
              <div>
                <p className="font-medium flex items-center gap-1">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  {payment.payment_method}
                </p>
                <p className="text-sm text-muted-foreground">
                  {formatDate(payment.created_at)}
                </p>
              </div>
              <Badge variant={payment.payment_status === "successful" ? "default" : "outline"}>
                {payment.payment_status || "Unknown"}
              </Badge>
            </div>
            
            <Separator className="my-2" />
            
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Amount</span>
              <span className="font-semibold">
                {new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: payment.currency
                }).format(payment.amount || 0)}
              </span>
            </div>
            
            {payment.transaction_id && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">Transaction ID</span>
                <span className="font-mono text-xs">{payment.transaction_id}</span>
              </div>
            )}
            {payment.card && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">Card</span>
                <span className="font-mono text-xs">{payment.card.last_4digits}</span>
              </div>
            )}

            {payment.metadata?.customer && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">Customer</span>
                <span className="font-mono text-xs">{payment.metadata.customer.name}</span>
              </div>
            )}

            {payment.metadata?.customer?.email && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">Customer Email</span>
                <span className="font-mono text-xs">{payment.metadata.customer.email}</span>
              </div>
            )}

            {payment.metadata?.customer?.phone_number && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">Customer Phone</span>
                <span className="font-mono text-xs">{payment.metadata.customer.phone_number}</span>
              </div>
            )}
            

            {payment.metadata?.card && (
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">Card</span>
                <span className="font-mono text-xs">Last 4 digits: {payment.metadata.card.last_4digits}</span>
              </div>
            )}
            
          </div>
        ))}
      </CardContent>
    </Card>
  )
} 