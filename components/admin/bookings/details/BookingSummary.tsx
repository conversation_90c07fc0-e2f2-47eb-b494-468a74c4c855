import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { BookingResponse } from "@/types/bookings";
import { differenceInDays } from "date-fns";
import { calculateDaysBetweenDates } from "@/utils";

export const PaymentSummary = ({ booking }: { booking: BookingResponse }) => {
    
    // Format currency
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: booking.currency_details.code || "UGX"
      }).format(amount);
    };

    const bookingDuration = calculateDaysBetweenDates({
      start: new Date(booking.start_date),
      end: new Date(booking.end_date)
    });
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Payment Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between mb-2">
              <p className="text-sm text-muted-foreground">Daily Rate</p>
              <p className="text-sm text-muted-foreground">
                {formatCurrency(booking.daily_charge || 0)} 
              </p>
            </div>

            <div className="flex justify-between mb-2">
              <p className="text-sm text-muted-foreground">Duration</p>
              <p className="text-sm text-muted-foreground">{bookingDuration} {bookingDuration === 1 ? 'day' : 'days'}</p>
            </div>
            
            <div className="flex justify-between mb-2">
              <p className="text-sm text-muted-foreground">Subtotal</p>
              <p className="text-sm text-muted-foreground">{formatCurrency(booking.daily_charge * bookingDuration)}</p>
            </div>

            <div className="flex justify-between mb-2">
              <p className="text-sm text-muted-foreground">Pickup fee</p>
              <p className="text-sm text-muted-foreground">{formatCurrency(booking.pickup_fee || 0)}</p>
            </div>
          </div>
          
          <Separator />
          
          <div className="flex space-between gap-1 justify-between font-medium">
            <p className="text-sm">Total Amount</p>
            <p className="text-sm">{formatCurrency(booking.total_amount)}</p>
          </div>
          
          {booking.cancellation_policy && (
            <div className="flex flex-col gap-2 mt-4 p-3 bg-muted/50 rounded-md">
              <p className="text-sm text-muted-foreground">{booking.cancellation_policy.name} Cancellation Policy</p>
              <p className="text-xs">{booking.cancellation_policy.description}</p>
              <p className="text-xs text-muted-foreground">Refundable after inspection</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };