"use client"

import { useState } from "react"
import { BookingModificationRequest } from "@/types/bookings"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { format } from "date-fns"
import { Eye } from "lucide-react"
import { ModificationDetailsModal } from "./modification-details-modal"

interface BookingModificationsTableProps {
  modifications: BookingModificationRequest[]
  booking: any
  className?: string
}

export function BookingModificationsTable({ modifications, booking, className }: BookingModificationsTableProps) {
  const [selectedModification, setSelectedModification] = useState<BookingModificationRequest | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Get modification request status badge
  const getModificationBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-500 text-white">Approved</Badge>
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>
      case "declined":
        return <Badge variant="destructive">Declined</Badge>
      case "canceled":
        return <Badge className="bg-orange-500 text-white">Canceled</Badge>
      case "completed":
        return <Badge className="bg-blue-500 text-white">Completed</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleViewDetails = (modification: BookingModificationRequest) => {
    setSelectedModification(modification)
    setIsModalOpen(true)
  }

  if (modifications.length === 0) {
    return (
      <div className="py-4 text-center text-muted-foreground">
        No modification requests for this booking
      </div>
    )
  }

  return (
    <>
      <div className={`overflow-x-auto ${className}`}>
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left p-2">Requested By</th>
              <th className="text-left p-2">Status</th>
              <th className="text-left p-2">Message</th>
              <th className="text-left p-2">Additional Amount</th>
              <th className="text-left p-2">Requested</th>
              <th className="text-right p-2">Actions</th>
            </tr>
          </thead>
          <tbody>
            {modifications.map((request) => (
              <tr key={request.id} className="border-b hover:bg-muted/50">
                <td className="p-2">
                  {request.requester_id === booking.renter_id ? 'Renter' : 'Host'}
                </td>
                <td className="p-2">{getModificationBadge(request.status)}</td>
                <td className="p-2 max-w-xs truncate">{request.message || 'No message'}</td>
                <td className="p-2">
                  {request.requested_changes.additional_amount ? 
                    new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: booking.currency_details?.code || 'UGX'
                    }).format(request.requested_changes.additional_amount) : 
                    '-'
                  }
                </td>
                <td className="p-2">{request.created_at && format(new Date(request.created_at), "MMM d, yyyy")}</td>
                <td className="p-2 text-right">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleViewDetails(request)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {selectedModification && (
        <ModificationDetailsModal
          modification={selectedModification}
          booking={booking}
          open={isModalOpen}
          onOpenChange={setIsModalOpen}
        />
      )}
    </>
  )
} 