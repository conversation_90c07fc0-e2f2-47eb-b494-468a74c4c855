"use client"

import { BookingIssue } from "@/types/bookings"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"

interface BookingIssuesTableProps {
  issues: BookingIssue[]
  className?: string
}

export function BookingIssuesTable({ issues, className }: BookingIssuesTableProps) {
  // Get issue status badge
  const getIssueBadge = (status: string) => {
    switch (status) {
      case "resolved":
        return <Badge className="bg-green-500 text-white">Resolved</Badge>
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>
      case "in_progress":
        return <Badge className="bg-blue-500 text-white">In Progress</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  if (issues.length === 0) {
    return (
      <div className="py-4 text-center text-muted-foreground">
        No issues reported for this booking
      </div>
    )
  }

  return (
    <div className={`overflow-x-auto ${className}`}>
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-2">Type</th>
            <th className="text-left p-2">Description</th>
            <th className="text-left p-2">Status</th>
            <th className="text-left p-2">Created</th>
            <th className="text-left p-2">Resolved</th>
          </tr>
        </thead>
        <tbody>
          {issues.map((issue) => (
            <tr key={issue.id} className="border-b hover:bg-muted/50">
              <td className="p-2 capitalize">{issue.issue_type}</td>
              <td className="p-2">{issue.description || 'No description'}</td>
              <td className="p-2">{getIssueBadge(issue.status)}</td>
              <td className="p-2">{issue.created_at && format(new Date(issue.created_at), "MMM d, yyyy")}</td>
              <td className="p-2">{issue.resolved_at ? format(new Date(issue.resolved_at), "MMM d, yyyy") : '-'}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
} 