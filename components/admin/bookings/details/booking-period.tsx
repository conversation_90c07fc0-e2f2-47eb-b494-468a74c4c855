"use client"

import { BookingResponse } from "@/types/bookings"
import { Calendar, MapPin } from "lucide-react"
import { format } from "date-fns"

interface BookingPeriodProps {
  booking: BookingResponse
  className?: string
}

export function BookingPeriod({ booking, className }: BookingPeriodProps) {
  // Format date
  const formatDate = (date?: string) => date ? format(new Date(date), "MMMM d, yyyy") : "N/A"

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Start Date</p>
          <div className="flex items-center gap-2 mt-1">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <p>{formatDate(booking.start_date)}</p>
          </div>
          <p className="text-sm mt-1">{booking.pickup_time || "Any time"}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">End Date</p>
          <div className="flex items-center gap-2 mt-1">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <p>{formatDate(booking.end_date)}</p>
          </div>
          <p className="text-sm mt-1">{booking.dropoff_time || "Any time"}</p>
        </div>
      </div>
      
      {booking.pick_up_location && (
        <div>
          <p className="text-sm text-muted-foreground">Pickup Location</p>
          <div className="flex items-center gap-2 mt-1">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <p>{booking.pick_up_location?.place_name || booking.pick_up_location?.formatted_address || "Not specified"}</p>
          </div>
        </div>
      )}
    </div>
  )
} 