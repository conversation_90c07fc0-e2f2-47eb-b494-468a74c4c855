"use client"

import { format } from "date-fns"
import { BookingResponse } from "@/types/bookings"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CalendarDays, Car, Clock, Users } from "lucide-react"

interface BookingCardProps {
  booking: BookingResponse
  className?: string
}

export function BookingCard({ booking, className }: BookingCardProps) {
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return <Badge className="bg-green-500 text-white">Confirmed</Badge>
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>
      case "completed":
        return <Badge className="bg-blue-500 text-white">Completed</Badge>
      case "ongoing":
        return <Badge className="bg-orange-500 text-white">Ongoing</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <Card className={`border-border/60 shadow-sm overflow-hidden ${className}`}>
      <CardHeader className="border-b border-border/60 bg-muted/30 px-6 py-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl flex items-center gap-2">
            Booking {booking.id?.substring(0, 8)}
          </CardTitle>
          {getStatusBadge(booking.status)}
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium flex items-center gap-1 mb-1">
                <Car className="h-4 w-4 text-muted-foreground" />
                Vehicle
              </h3>
              <p>{booking.brand_name} {booking.model} ({booking.year})</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium flex items-center gap-1 mb-1">
                <CalendarDays className="h-4 w-4 text-muted-foreground" />
                Dates
              </h3>
              <p>
                {booking.start_date && booking.end_date ? (
                  <>
                    {format(new Date(booking.start_date), "MMM d, yyyy")} - {format(new Date(booking.end_date), "MMM d, yyyy")}
                  </>
                ) : (
                  "N/A"
                )}
              </p>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium flex items-center gap-1 mb-1">
                <Users className="h-4 w-4 text-muted-foreground" />
                Renter
              </h3>
              <p>{(booking as any).renter_name || "Unknown"}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium flex items-center gap-1 mb-1">
                <Clock className="h-4 w-4 text-muted-foreground" />
                Created
              </h3>
              <p>
                {booking.created_at ? format(new Date(booking.created_at), "PPP 'at' p") : "N/A"}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 