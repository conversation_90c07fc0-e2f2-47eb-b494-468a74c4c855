"use client"

import { useState, useRef } from "react"
import { useRouter } from "next/navigation"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Check, X, MessageSquare, Calendar, FileText } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { format, differenceInDays } from "date-fns"
import { useToast } from "@/hooks/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { updateBookingStatus } from "@/app/actions/admin/admin-actions"
import { BookingStatus } from "@/types/supabase"

// Define the Booking type
type Booking = {
  id: string
  car_id: string
  renter_id: string
  start_date: string
  end_date: string
  pickup_time: string
  dropoff_time: string
  status: "pending" | "confirmed" | "completed" | "cancelled"
  total_amount: number
  daily_charge: number
  currency_code: string
  created_at: string
  updated_at: string
  pickup_location: any
  pickup_instructions: string | null
  return_instructions: string | null
  host_notes: string | null
  cancellation_reason: string | null
  canceled_at: string | null
  car: {
    id: string
    make: string
    model: string
    year: number
    owner_id: string
    image_url: string | null
    daily_rate: number
    status: string
    registration_number?: string
  }
  renter: {
    id: string
    first_name: string
    last_name: string
    full_name: string
    email: string
    avatar_url: string | null
    phone: string | null
  }
}

interface BookingsManagementTableProps {
  bookings: Booking[]
}

export function BookingsManagementTable({ bookings }: BookingsManagementTableProps) {
  const router = useRouter()
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [isUpdating, setIsUpdating] = useState(false)
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [cancellationReason, setCancellationReason] = useState("")
  const cancelReasonRef = useRef<HTMLTextAreaElement>(null)
  const { toast } = useToast()
  // Handle status change for a booking
  const handleStatusChange = async (bookingId: string, status: BookingStatus, notes?: string) => {
    if (isUpdating) return

    setIsUpdating(true)
    try {
      await updateBookingStatus(bookingId, status, notes)

      toast({
        title: "Booking Updated",
        description: `Booking status has been updated to ${status}.`,
      })

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to update booking status: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  // Handle batch actions for multiple bookings
  const handleBatchAction = async (action: "confirm" | "cancel" | "complete") => {
    const selectedRows = table.getFilteredSelectedRowModel().rows

    if (selectedRows.length === 0) {
      toast({
        title: "No bookings selected",
        description: "Please select at least one booking to perform this action",
        variant: "destructive",
      })
      return
    }

    setIsUpdating(true)
    let successCount = 0
    let errorCount = 0

    try {
      // Process each selected booking
      for (const row of selectedRows) {
        const booking = row.original
        const newStatus =
          action === "confirm" ? "confirmed" :
          action === "cancel" ? "cancelled" :
          "completed"

        // Skip if the booking is already in the target status
        if (booking.status === newStatus) {
          continue
        }

        try {
          await updateBookingStatus(booking.id, newStatus)
          successCount++
        } catch (err) {
          errorCount++
          console.error(`Error processing booking ${booking.id}:`, err)
        }
      }

      // Show success toast
      toast({
        title: successCount > 0 ? "Batch Action Successful" : "Batch Action Failed",
        description: `${successCount} of ${selectedRows.length} bookings processed successfully. ${errorCount > 0 ? `${errorCount} failed.` : ''}`,
        variant: successCount > 0 ? "default" : "destructive",
      })

      // Refresh the page to show updated data
      if (successCount > 0) {
        router.refresh()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process batch action",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
      // Clear selection
      table.toggleAllRowsSelected(false)
    }
  }

  const columns: ColumnDef<Booking>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // ID column removed as requested
    {
      accessorKey: "car",
      header: "Car Details",
      cell: ({ row }) => {
        const car = row.original.car
        return (
          <div className="flex items-center gap-3">
            {car.image_url ? (
              <div className="h-10 w-10 rounded-md overflow-hidden">
                <img
                  src={car.image_url}
                  alt={`${car.make} ${car.model}`}
                  className="h-full w-full object-cover"
                />
              </div>
            ) : (
              <div className="h-10 w-10 rounded-md bg-muted flex items-center justify-center">
                <span className="text-xs text-muted-foreground">No img</span>
              </div>
            )}
            <div className="flex flex-col">
              <span className="font-medium">{car.make} {car.model}</span>
              <span className="text-xs text-muted-foreground">{car.year}</span>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: "renter",
      header: "Renter",
      cell: ({ row }) => {
        const renter = row.original.renter
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={renter.avatar_url || ""} alt={renter.full_name} />
              <AvatarFallback>{renter.full_name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium">{renter.full_name}</span>
              {renter.phone && <span className="text-xs text-muted-foreground">{renter.phone}</span>}
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: "dates",
      header: "Booking Period",
      cell: ({ row }) => {
        const booking = row.original
        const startDate = new Date(booking.start_date)
        const endDate = new Date(booking.end_date)
        const days = differenceInDays(endDate, startDate) + 1

        return (
          <div className="flex flex-col">
            <span>{format(startDate, "MMM d, yyyy")} - {format(endDate, "MMM d, yyyy")}</span>
            <span className="text-xs text-muted-foreground">{days} {days === 1 ? "day" : "days"}</span>
          </div>
        )
      },
    },
    {
      accessorKey: "total_amount",
      header: ({ column }) => {
        return (
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
            Amount
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const booking = row.original
        const amount = parseFloat(row.getValue("total_amount"))
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: booking.currency_code || "USD",
        }).format(amount)

        return <div>{formatted}</div>
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string

        return (
          <Badge
            variant={
              status === "confirmed"
                ? "default"
                : status === "pending"
                ? "outline"
                : status === "completed"
                ? "secondary"
                : "destructive"
            }
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        )
      },
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => {
        return <div>{format(new Date(row.getValue("created_at")), "MMM d, yyyy")}</div>
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const booking = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>

              <DropdownMenuItem
                onClick={() => {
                  setSelectedBooking(booking)
                  setIsDetailsOpen(true)
                }}
              >
                <FileText className="mr-2 h-4 w-4" />
                View details
              </DropdownMenuItem>

              {booking.status === "pending" && (
                <>
                  <DropdownMenuItem
                    onClick={() => handleStatusChange(booking.id, "confirmed")}
                    disabled={isUpdating}
                  >
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Confirm booking
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      setSelectedBooking(booking)
                      setCancellationReason("")
                      setIsDetailsOpen(true)
                      // Focus on the cancellation tab
                      setTimeout(() => {
                        const cancelTab = document.getElementById("cancel-tab")
                        if (cancelTab) cancelTab.click()
                      }, 100)
                    }}
                    disabled={isUpdating}
                  >
                    <X className="mr-2 h-4 w-4 text-red-500" />
                    Cancel booking
                  </DropdownMenuItem>
                </>
              )}

              {booking.status === "confirmed" && (
                <>
                  <DropdownMenuItem
                    onClick={() => handleStatusChange(booking.id, "completed")}
                    disabled={isUpdating}
                  >
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Mark as completed
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      setSelectedBooking(booking)
                      setCancellationReason("")
                      setIsDetailsOpen(true)
                      // Focus on the cancellation tab
                      setTimeout(() => {
                        const cancelTab = document.getElementById("cancel-tab")
                        if (cancelTab) cancelTab.click()
                      }, 100)
                    }}
                    disabled={isUpdating}
                  >
                    <X className="mr-2 h-4 w-4 text-red-500" />
                    Cancel booking
                  </DropdownMenuItem>
                </>
              )}

              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(booking.id)}>
                Copy booking ID
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data: bookings,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      rowSelection,
      columnVisibility,
    },
  })

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Input
          placeholder="Filter bookings..."
          value={(table.getColumn("renter")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("renter")?.setFilterValue(event.target.value)}
          className="max-w-sm"
        />
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => handleBatchAction("confirm")}
            disabled={isUpdating || Object.keys(rowSelection).length === 0}
          >
            <Check className="mr-2 h-4 w-4" />
            Confirm Selected
          </Button>
          <Button
            variant="outline"
            onClick={() => handleBatchAction("cancel")}
            disabled={isUpdating || Object.keys(rowSelection).length === 0}
          >
            <X className="mr-2 h-4 w-4" />
            Cancel Selected
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) => column.toggleVisibility(!!value)}
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No bookings found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s)
          selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
            Next
          </Button>
        </div>
      </div>

      {/* Booking Details Dialog */}
      {selectedBooking && (
        <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
          <DialogContent className="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle>Booking Details</DialogTitle>
              <DialogDescription>Booking ID: {selectedBooking.id}</DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="details">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="customer">Customer</TabsTrigger>
                <TabsTrigger id="cancel-tab" value="cancel">Cancel Booking</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4 pt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Car</h4>
                    <p className="font-medium">{selectedBooking.car.make} {selectedBooking.car.model} ({selectedBooking.car.year})</p>
                    {selectedBooking.car.registration_number && (
                      <p className="text-xs font-mono bg-muted inline-block px-1.5 py-0.5 rounded-sm mt-1">
                        {selectedBooking.car.registration_number}
                      </p>
                    )}
                  </div>
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Status</h4>
                    <Badge
                      variant={
                        selectedBooking.status === "confirmed"
                          ? "default"
                          : selectedBooking.status === "pending"
                          ? "outline"
                          : selectedBooking.status === "completed"
                          ? "secondary"
                          : "destructive"
                      }
                    >
                      {selectedBooking.status.charAt(0).toUpperCase() + selectedBooking.status.slice(1)}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Booking Period</h4>
                    <p className="font-medium">
                      {format(new Date(selectedBooking.start_date), "MMM d, yyyy")} - {format(new Date(selectedBooking.end_date), "MMM d, yyyy")}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {differenceInDays(new Date(selectedBooking.end_date), new Date(selectedBooking.start_date)) + 1} days
                    </p>
                  </div>
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Pickup/Dropoff Times</h4>
                    <p className="font-medium">
                      Pickup: {selectedBooking.pickup_time || 'Not specified'}
                    </p>
                    <p className="font-medium">
                      Dropoff: {selectedBooking.dropoff_time || 'Not specified'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Amount</h4>
                    <p className="font-medium">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: selectedBooking.currency_code || "USD",
                      }).format(selectedBooking.total_amount)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Daily rate: {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: selectedBooking.currency_code || "USD",
                      }).format(selectedBooking.daily_charge)}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">Created</h4>
                    <p className="font-medium">
                      {format(new Date(selectedBooking.created_at), "MMM d, yyyy h:mm a")}
                    </p>
                  </div>
                </div>

                {selectedBooking.pickup_location && (
                  <div className="space-y-1 mt-4">
                    <h4 className="text-sm font-medium text-muted-foreground">Pickup Location</h4>
                    <p className="font-medium">
                      {typeof selectedBooking.pickup_location === 'object' ? (
                        selectedBooking.pickup_location.formatted_address ||
                        selectedBooking.pickup_location.place_name ||
                        selectedBooking.pickup_location.address ||
                        'Location details not available'
                      ) : (
                        'Location details not available'
                      )}
                    </p>
                    {selectedBooking.pickup_location &&
                     typeof selectedBooking.pickup_location === 'object' &&
                     selectedBooking.pickup_location.place_name &&
                     selectedBooking.pickup_location.place_name !== selectedBooking.pickup_location.formatted_address && (
                      <p className="text-sm text-muted-foreground">
                        {selectedBooking.pickup_location.place_name}
                      </p>
                    )}
                    {selectedBooking.pickup_instructions && (
                      <div className="mt-2">
                        <h4 className="text-sm font-medium text-muted-foreground">Pickup Instructions</h4>
                        <p className="text-sm">{selectedBooking.pickup_instructions}</p>
                      </div>
                    )}
                  </div>
                )}

                {selectedBooking.return_instructions && (
                  <div className="space-y-1 mt-4">
                    <h4 className="text-sm font-medium text-muted-foreground">Return Instructions</h4>
                    <p className="text-sm">{selectedBooking.return_instructions}</p>
                  </div>
                )}

                {selectedBooking.host_notes && (
                  <div className="space-y-1 mt-4">
                    <h4 className="text-sm font-medium text-muted-foreground">Host Notes</h4>
                    <p className="text-sm">{selectedBooking.host_notes}</p>
                  </div>
                )}

                {selectedBooking.status === "cancelled" && selectedBooking.cancellation_reason && (
                  <div className="space-y-1 mt-4">
                    <h4 className="text-sm font-medium text-muted-foreground">Cancellation Reason</h4>
                    <p className="text-sm">{selectedBooking.cancellation_reason}</p>
                    {selectedBooking.canceled_at && (
                      <p className="text-xs text-muted-foreground">
                        Cancelled on {format(new Date(selectedBooking.canceled_at), "MMM d, yyyy h:mm a")}
                      </p>
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="customer" className="space-y-4 pt-4">
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={selectedBooking.renter.avatar_url || ""} alt={selectedBooking.renter.full_name} />
                    <AvatarFallback>{selectedBooking.renter.full_name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-lg font-medium">{selectedBooking.renter.full_name}</h3>
                    {selectedBooking.renter.phone && (
                      <p className="text-sm">{selectedBooking.renter.phone}</p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">Customer ID: {selectedBooking.renter.id}</p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="cancel" className="space-y-4 pt-4">
                {selectedBooking.status === "cancelled" ? (
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">This booking has already been cancelled</h3>
                    {selectedBooking.cancellation_reason && (
                      <div className="space-y-1">
                        <h4 className="text-sm font-medium text-muted-foreground">Cancellation Reason</h4>
                        <p className="text-sm">{selectedBooking.cancellation_reason}</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <h3 className="text-lg font-medium">Cancel this booking</h3>
                      <p className="text-sm text-muted-foreground">
                        Please provide a reason for cancellation. This will be visible to the customer.
                      </p>
                    </div>

                    <Textarea
                      ref={cancelReasonRef}
                      placeholder="Reason for cancellation"
                      value={cancellationReason}
                      onChange={(e) => setCancellationReason(e.target.value)}
                      className="min-h-[100px]"
                    />

                    <Button
                      variant="destructive"
                      onClick={() => {
                        if (!cancellationReason.trim()) {
                          toast({
                            title: "Cancellation reason required",
                            description: "Please provide a reason for cancellation",
                            variant: "destructive",
                          })
                          return
                        }

                        handleStatusChange(selectedBooking.id, "cancelled", cancellationReason)
                        setIsDetailsOpen(false)
                      }}
                      disabled={isUpdating}
                    >
                      Cancel Booking
                    </Button>
                  </div>
                )}
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <div className="flex justify-between w-full">
                <div>
                  {(selectedBooking.status === "pending" || selectedBooking.status === "confirmed") && (
                    <div className="flex gap-2">
                      {selectedBooking.status === "pending" && (
                        <Button
                          onClick={() => {
                            handleStatusChange(selectedBooking.id, "confirmed")
                            setIsDetailsOpen(false)
                          }}
                          disabled={isUpdating}
                        >
                          <Check className="mr-2 h-4 w-4" />
                          Confirm Booking
                        </Button>
                      )}
                      {selectedBooking.status === "confirmed" && (
                        <Button
                          onClick={() => {
                            handleStatusChange(selectedBooking.id, "completed")
                            setIsDetailsOpen(false)
                          }}
                          disabled={isUpdating}
                        >
                          <Check className="mr-2 h-4 w-4" />
                          Mark as Completed
                        </Button>
                      )}
                    </div>
                  )}
                </div>
                <Button variant="outline" onClick={() => setIsDetailsOpen(false)}>
                  Close
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
