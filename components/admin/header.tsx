"use client"

import { useState } from "react"
import { Bell, LogOut, Search, Settings, User } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User as LoggedInUser } from "@supabase/supabase-js"
import { signOut } from "@/app/actions/auth-actions"

export function AdminHeader({user}: {user: LoggedInUser}) {
  const [searchOpen, setSearchOpen] = useState(false)

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b border-border/40 bg-background/80 backdrop-blur-md px-4 md:px-8">
      <SidebarTrigger className="text-muted-foreground hover:text-foreground" />

      {searchOpen ? (
        <div className="flex-1 md:w-auto md:flex-none">
          <form className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search..."
              className="w-full rounded-lg bg-background/50 pl-8 md:w-[200px] lg:w-[336px] border-border/50 focus:border-primary/50"
            />
          </form>
        </div>
      ) : (
        <Button variant="ghost" size="icon" className="md:hidden text-muted-foreground" onClick={() => setSearchOpen(true)}>
          <Search className="h-4 w-4" />
          <span className="sr-only">Search</span>
        </Button>
      )}

      <div className="hidden md:flex md:flex-1">
        {/* <form className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search..."
            className="w-full rounded-lg bg-background/50 pl-8 md:w-[200px] lg:w-[336px] border-border/50 focus:border-primary/50"
          />
        </form> */}
      </div>

      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" className="relative text-muted-foreground hover:text-foreground">
          <Bell className="h-4 w-4" />
          <span className="sr-only">Notifications</span>
          <span className="absolute right-1 top-1 flex h-2 w-2 rounded-full bg-primary animate-pulse"></span>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="rounded-full flex items-center gap-2 px-2">
              <Avatar className="h-8 w-8 border-2 border-primary/20">
                <AvatarImage
                  src={"/placeholder-user.jpg"}
                  alt={user?.user_metadata.first_name || "Admin"}
                />
                <AvatarFallback className="bg-primary/10 text-primary">
                  {user?.user_metadata.first_name ? user.user_metadata.first_name.charAt(0).toUpperCase() : <User className="h-4 w-4" />}
                </AvatarFallback>
              </Avatar>
              <span className="hidden md:inline-block font-medium text-sm">
                {user?.user_metadata.first_name || "Admin"}
              </span>
              <span className="sr-only">Toggle user menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <div className="p-2 pt-1 pb-3 mb-2 border-b">
              <p className="font-medium">{`${user?.user_metadata.first_name || ''} ${user?.user_metadata.last_name || ''}`.trim() || "Admin Account"}</p>
              <p className="text-xs text-muted-foreground mt-1">{user?.email || "<EMAIL>"}</p>
            </div>
            <DropdownMenuItem asChild className="cursor-pointer">
              <a href="/admin/profile" className="flex items-center">
                <User className="mr-2 h-4 w-4" />
                Profile
              </a>
            </DropdownMenuItem>
            <DropdownMenuItem asChild className="cursor-pointer">
              <a href="/admin/settings" className="flex items-center">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </a>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild className="cursor-pointer text-rose-500 focus:text-rose-500">
              <form action={signOut}>
                <button className="w-full text-left flex items-center">
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign out
                </button>
              </form>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}

